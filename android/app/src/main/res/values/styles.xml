<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode
    setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
    <!-- UCrop custom theme to fix UI issues -->
    <style name="UCropTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Status bar color -->
        <item name="android:statusBarColor">#1A1A1A</item>

        <!-- Handle display cutout (notch/punch hole) -->
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>

        <!-- System window insets -->
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>

        <!-- Prevent status bar content overlap -->
        <item name="android:windowTranslucentNavigation">false</item>

        <!-- Toolbar and action bar colors -->
        <item name="colorPrimary">#1A1A1A</item>
        <item name="colorPrimaryDark">#1A1A1A</item>
        <item name="android:textColorPrimary">#FFFFFF</item>
    </style>
</resources>