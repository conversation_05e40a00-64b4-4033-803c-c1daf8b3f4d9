name: Sanity check

on:
  workflow_dispatch:
  # pull_request:
  #   types: [opened, synchronize, reopened]

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write  # Grants the GITHUB_TOKEN permission to write pull request comments
      contents: read        # Ensures the workflow can read the repository contents
    steps:
      - name: Checkout Branch
        uses: actions/checkout@v4
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ vars.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          cache-key: flutter # optional, change this to force refresh cache
          cache-path: ${{ runner.tool_cache }}/flutter # optional, change this to specify the cache path
          architecture: x64 # optional, x64 or arm64
      - name: Flutter pub get
        run: flutter pub get
      - name: Run build_runner
        run: dart run build_runner build
      - name: Flutter Analyze
        id: flutter_analyze
        run: flutter analyze > analyze_output.txt
      - name: Analyze `flutter analyze` output
        if: always()
        # Add the $MESSAGE variable to github environment variables
        # Add multiline $OUTPUT to github environment variables
        # Source: https://docs.github.com/en/actions/using-workflows/workflow-commands-for-github-actions#example-of-a-multiline-string
        run: |
          if grep -q "No issues found" analyze_output.txt; then
            MESSAGE="Flutter analyze completed successfully."
          else
            MESSAGE="Flutter analyze failed"
          fi
          echo $MESSAGE
          echo "MESSAGE=$MESSAGE" >> "$GITHUB_ENV"
          EOF=$(dd if=/dev/urandom bs=15 count=1 status=none | base64)
          echo "OUTPUT<<$EOF" >> "$GITHUB_ENV"
          cat analyze_output.txt >> "$GITHUB_ENV"
          echo "$EOF" >> "$GITHUB_ENV"
      - name: Comment on PR
        uses: unsplash/comment-on-pr@v1.3.0
        if: always()
        with:
          check_for_duplicate_msg: false
          msg: |
            **Flutter Analyze Output**
            >${{ env.MESSAGE }}
            ```
            ${{ env.OUTPUT }}
            ```
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
