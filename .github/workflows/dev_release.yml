name: Dev Release - Version Bump and Codemagic Build

on:
  workflow_dispatch:

env:
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}

jobs:
  version-bump-and-build:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ env.BRANCH_NAME }}

      - name: Bump version
        id: bump-version
        run: |
          FULL_VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //')

          CURRENT_VERSION=$(echo $FULL_VERSION | cut -d'+' -f1)
          VERSION_CODE=$(echo $FULL_VERSION | cut -d'+' -f2)

          if [ -z "$VERSION_CODE" ] || [ "$VERSION_CODE" = "$CURRENT_VERSION" ]; then
            VERSION_CODE=0
          fi

          NEW_VERSION=$(echo $CURRENT_VERSION | awk -F. '{$NF = $NF + 1;} 1' | sed 's/ /./g')

          NEW_VERSION_CODE=$((VERSION_CODE + 1))

          NEW_FULL_VERSION="${NEW_VERSION}+${NEW_VERSION_CODE}"
          echo "New version would be: $NEW_FULL_VERSION"

          while git rev-parse "v${NEW_VERSION}" >/dev/null 2>&1; do
            NEW_VERSION=$(echo $NEW_VERSION | awk -F. '{$NF = $NF + 1;} 1' | sed 's/ /./g')
          done

          sed -i.bak "s/^version: .*/version: $NEW_VERSION+${NEW_VERSION_CODE}/" pubspec.yaml && rm pubspec.yaml.bak
          
          echo "NEW_FULL_VERSION=$NEW_VERSION+${NEW_VERSION_CODE}" >> $GITHUB_ENV
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_ENV

      - name: Commit and push changes
        run: |
          git config --local user.name "github-actions[bot]"
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"

          git add pubspec.yaml
          git commit -m "Bump version to ${{ env.NEW_VERSION }}" \
            -m "Triggered by: @${{ github.actor }}" \
            --author="${{ github.actor }} <${{ github.actor_id }}+${{ github.actor }}@users.noreply.github.com>"
          git push origin HEAD:${{ env.BRANCH_NAME }}

      - name: Create and push tag
        run: |
          git tag -a "v${{ env.NEW_VERSION }}" -m "Release v${{ env.NEW_VERSION }}"
          git push origin v${{ env.NEW_VERSION }}

      - name: Create GitHub Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh release create v${{ env.NEW_VERSION }} \
            --title "v${{ env.NEW_VERSION }}" \
            --generate-notes \
            --target ${{ env.BRANCH_NAME }}

      - name: Start Codemagic build
        env:
          CODEMAGIC_API_TOKEN: ${{ secrets.CODEMAGIC_API_TOKEN }}
          CODEMAGIC_APP_ID: ${{ secrets.CODEMAGIC_APP_ID }}
          CODEMAGIC_WORKFLOW_ID: ${{ secrets.CODEMAGIC_WORKFLOW_ID }}
        run: |
          curl -H "Content-Type: application/json" \
               -H "x-auth-token: $CODEMAGIC_API_TOKEN" \
               --data '{
                 "appId": "'$CODEMAGIC_APP_ID'",
                 "workflowId": "'$CODEMAGIC_WORKFLOW_ID'",
                 "branch": "${{ env.BRANCH_NAME }}"
               }' \
               -X POST https://api.codemagic.io/builds
