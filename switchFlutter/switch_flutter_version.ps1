# PowerShell script to switch Flutter version based on pubspec.yaml using FVM
# For Windows systems

# Stop on first error
$ErrorActionPreference = "Stop"

# Check if FVM is installed and install if needed
try {
    $fvmVersion = fvm --version
    Write-Host "Found FVM version: $fvmVersion" -ForegroundColor Green
} catch {
    Write-Host "FVM (Flutter Version Management) is not installed. Installing now..." -ForegroundColor Yellow
    
    # Check if Dart/Flutter is installed first
    try {
        $dartVersion = dart --version
    } catch {
        Write-Host "Error: Dart SDK is not installed. Please install Flutter first." -ForegroundColor Red
        Write-Host "Visit: https://docs.flutter.dev/get-started/install" -ForegroundColor Yellow
        exit 1
    }
    
    # Install FVM using pub
    Write-Host "Installing FVM using 'dart pub global activate fvm'..." -ForegroundColor Yellow
    try {
        dart pub global activate fvm
    } catch {
        Write-Host "Error: Failed to install FVM. Please try installing manually:" -ForegroundColor Red
        Write-Host "dart pub global activate fvm" -ForegroundColor Yellow
        exit 1
    }
    
    # Add FVM to PATH if needed
    $dartPubCache = "$env:LOCALAPPDATA\Pub\Cache\bin"
    if ($env:Path -notlike "*$dartPubCache*") {
        Write-Host "Adding FVM to PATH..." -ForegroundColor Yellow
        $env:Path += ";$dartPubCache"
        [Environment]::SetEnvironmentVariable(
            "Path",
            [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::User) + ";$dartPubCache",
            [EnvironmentVariableTarget]::User
        )
    }
    
    # Verify FVM installation
    try {
        $fvmVersion = fvm --version
        Write-Host "FVM installed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "Error: FVM installation failed. Please try installing manually:" -ForegroundColor Red
        Write-Host "dart pub global activate fvm" -ForegroundColor Yellow
        exit 1
    }
}

# Get the project root directory (where pubspec.yaml is located)
$projectRoot = Get-Location
$pubspecPath = Join-Path -Path $projectRoot -ChildPath "pubspec.yaml"

# Check if pubspec.yaml exists
if (-not (Test-Path $pubspecPath)) {
    Write-Host "Error: pubspec.yaml not found at $pubspecPath" -ForegroundColor Red
    exit 1
}

# Extract Flutter SDK version from pubspec.yaml
# Looking for a line like: sdk: ^3.5.0 in the environment section
$pubspecContent = Get-Content $pubspecPath
$inEnvironmentSection = $false
$sdkVersion = $null

foreach ($line in $pubspecContent) {
    if ($line -match "^environment:") {
        $inEnvironmentSection = $true
        continue
    }
    
    if ($inEnvironmentSection -and $line -match "^\s+sdk:\s+(.*)") {
        # Extract version and remove any constraint characters (^, >=, ~=)
        $versionString = $matches[1].Trim()
        $versionString = $versionString -replace "[\^>=~'\"]|sdk:", ""
        $sdkVersion = $versionString.Trim()
        break
    }
    
    # Exit environment section if we hit another top-level section
    if ($inEnvironmentSection -and $line -match "^[a-zA-Z].*:") {
        $inEnvironmentSection = $false
    }
}

if (-not $sdkVersion) {
    Write-Host "Error: Could not find Flutter SDK version in pubspec.yaml" -ForegroundColor Red
    exit 1
}

Write-Host "Found Flutter SDK version in pubspec.yaml: $sdkVersion" -ForegroundColor Green

# Get available Flutter versions
$availableVersions = fvm releases

# Try to install the specified version directly
Write-Host "Attempting to install Flutter version $sdkVersion..." -ForegroundColor Yellow
try {
    fvm install $sdkVersion
    Write-Host "Successfully installed Flutter version $sdkVersion" -ForegroundColor Green
} catch {
    Write-Host "Failed to install Flutter version $sdkVersion, falling back to stable channel" -ForegroundColor Yellow
    $sdkVersion = "stable"
}

# Find the closest stable version to the SDK version
$closestVersion = $null
$majorMinor = $sdkVersion -replace '^(\d+\.\d+).*', '$1'

foreach ($version in $availableVersions) {
    # Skip non-version lines and pre-release versions
    if ($version -notmatch '^\d+\.\d+\.\d+$' -or $version -match 'pre|dev|beta|alpha') {
        continue
    }
    
    $versionMajorMinor = $version -replace '^(\d+\.\d+).*', '$1'
    if ($versionMajorMinor -eq $majorMinor) {
        $closestVersion = $version
        break
    }
}

# If no matching version found, use stable
if (-not $closestVersion) {
    Write-Host "No matching Flutter version found for $sdkVersion, using stable channel" -ForegroundColor Yellow
    $flutterVersion = "stable"
} else {
    Write-Host "Using Flutter version $closestVersion for SDK version $sdkVersion" -ForegroundColor Green
    $flutterVersion = $closestVersion
}

# Check if this version is already installed with FVM
$fvmList = fvm list
if ($fvmList -match $flutterVersion) {
    Write-Host "Flutter version $flutterVersion is already installed with FVM" -ForegroundColor Green
} else {
    Write-Host "Installing Flutter version $flutterVersion with FVM..." -ForegroundColor Yellow
    fvm install $flutterVersion
}

# Set this version as the project version
Write-Host "Setting Flutter version $flutterVersion for this project..." -ForegroundColor Yellow
fvm use $flutterVersion

Write-Host "Successfully switched to Flutter version $flutterVersion" -ForegroundColor Green
Write-Host "You can now run Flutter commands using: 'fvm flutter <command>'" -ForegroundColor Cyan
Write-Host "Or use the FVM-managed Flutter directly with: '$projectRoot\.fvm\flutter_sdk\bin\flutter.bat <command>'" -ForegroundColor Cyan