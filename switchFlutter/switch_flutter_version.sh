#!/bin/bash

# Script to switch Flutter version based on pubspec.yaml using FVM
# Supports both macOS and Windows (when run with Git Bash or similar)

set -e

# Detect OS
case "$(uname -s)" in
    Darwin*)
        OS="macos"
        ;;
    MINGW*|MSYS*|CYGWIN*)
        OS="windows"
        ;;
    *)
        echo "Unsupported operating system. This script supports macOS and Windows."
        exit 1
        ;;
esac

echo "Detected OS: $OS"

# Check if FVM is installed and install if needed
if ! command -v fvm &> /dev/null; then
    echo "FVM (Flutter Version Management) is not installed. Installing now..."
    
    # Check if Dart/Flutter is installed first
    if ! command -v dart &> /dev/null; then
        echo "Error: Dart SDK is not installed. Please install Flutter first."
        echo "Visit: https://docs.flutter.dev/get-started/install"
        exit 1
    fi
    
    # Install FVM using pub
    echo "Installing FVM using 'dart pub global activate fvm'..."
    dart pub global activate fvm
    
    # Add FVM to PATH if needed
    if [ "$OS" = "macos" ]; then
        DART_BIN="$HOME/.pub-cache/bin"
        if [[ ":$PATH:" != *":$DART_BIN:"* ]]; then
            echo "Adding FVM to PATH..."
            echo 'export PATH="$PATH:$HOME/.pub-cache/bin"' >> ~/.zshrc
            export PATH="$PATH:$HOME/.pub-cache/bin"
        fi
    fi
    
    # Verify FVM installation
    if ! command -v fvm &> /dev/null; then
        echo "Error: FVM installation failed. Please try installing manually:"
        echo "dart pub global activate fvm"
        exit 1
    fi
    
    echo "FVM installed successfully!"
fi

# Get the project root directory (where pubspec.yaml is located)
PROJECT_ROOT="$(pwd)"
PUBSPEC_PATH="$PROJECT_ROOT/pubspec.yaml"

# Check if pubspec.yaml exists
if [ ! -f "$PUBSPEC_PATH" ]; then
    echo "Error: pubspec.yaml not found at $PUBSPEC_PATH"
    exit 1
fi

# Extract Flutter version from pubspec.yaml
# Looking for a line like: flutter_version: "3.19.3"
FLUTTER_VERSION=$(awk '/flutter_version:/ {gsub(/["\"flutter_version: ]/, "", $2); print $2}' "$PUBSPEC_PATH")

if [ -z "$FLUTTER_VERSION" ]; then
    echo "Error: Could not find flutter_version in pubspec.yaml"
    echo "Please add 'flutter_version: \"x.y.z\"' to your pubspec.yaml"
    exit 1
fi

echo "Found Flutter version in pubspec.yaml: $FLUTTER_VERSION"

# Check for available Flutter versions
AVAILABLE_VERSIONS=$(fvm releases)

# Find the exact version match
CLOSEST_VERSION=$(echo "$AVAILABLE_VERSIONS" | grep -E "^[0-9]+\.[0-9]+\.[0-9]+" | sort -V | grep -v "pre" | grep -v "dev" | grep -v "beta" | grep -v "alpha" | while read version; do
    if [ "$version" = "$FLUTTER_VERSION" ]; then
        echo "$version"
        break
    fi
done)

# If no matching version found, try to install it
if [ -z "$CLOSEST_VERSION" ]; then
    echo "Attempting to install Flutter version $FLUTTER_VERSION..."
    if ! fvm install "$FLUTTER_VERSION"; then
        echo "Failed to install Flutter version $FLUTTER_VERSION, falling back to stable channel"
        FLUTTER_VERSION="stable"
    fi
else
    echo "Using Flutter version $CLOSEST_VERSION"
    FLUTTER_VERSION="$CLOSEST_VERSION"
fi

# Check if this version is already installed with FVM
FVM_LIST=$(fvm list)
if echo "$FVM_LIST" | grep -q "$FLUTTER_VERSION"; then
    echo "Flutter version $FLUTTER_VERSION is already installed with FVM"
else
    echo "Installing Flutter version $FLUTTER_VERSION with FVM..."
    fvm install "$FLUTTER_VERSION"
fi

# Set this version as the project version
echo "Setting Flutter version $FLUTTER_VERSION for this project..."
fvm use "$FLUTTER_VERSION"

echo "Successfully switched to Flutter version $FLUTTER_VERSION"
echo "You can now run Flutter commands using: 'fvm flutter <command>'"
echo "Or use the FVM-managed Flutter directly with: '$PROJECT_ROOT/.fvm/flutter_sdk/bin/flutter <command>'"