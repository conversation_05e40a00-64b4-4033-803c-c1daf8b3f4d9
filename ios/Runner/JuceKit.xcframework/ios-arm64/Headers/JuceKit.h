#pragma once

#ifdef __cplusplus
#define EXPORT_C_FUNC extern "C" __attribute__((visibility("default"))) __attribute__((used))
#else
#define EXPORT_C_FUNC extern
#endif

EXPORT_C_FUNC void juceMessageManagerInit();

EXPORT_C_FUNC void juce_enableLogs(int enable);

EXPORT_C_FUNC const char* juce_applyReverbEffect(const char* inputFilePath,
                                                 const char* outputFilePath,
                                                 const char* JSONpath);

EXPORT_C_FUNC const char* juce_applyProcessingGraph(const char *inputPath,
                                                    const char *outputPath,
                                                    const char *jsonPath,
                                                    const char *genreMapPath,
                                                    const char *bgmGenre,
                                                    void (*callback)(const char*),
                                                    void (*completion)(const char*));

EXPORT_C_FUNC const char* juce_applyProcessingGraphMaster(const char *inputPath,
                                                          const char *outputPath,
                                                          const char *jsonPath,
                                                          const char *genreMapPath,
                                                          const char *bgmGenre,
                                                          void (*callback)(const char*),
                                                          void (*completion)(const char*));

EXPORT_C_FUNC const char* juce_getAudioFileDetails(const char* inputFilePath);

EXPORT_C_FUNC const char* juce_convertWavToFlac(const char* inPath,
                                                const char* outPath);

// MARK: JuceMixer

EXPORT_C_FUNC void* JuceMixer_init();
EXPORT_C_FUNC void JuceMixer_deinit(void *ptr);
EXPORT_C_FUNC const char* JuceMixer_getAudioFileDetails(void *ptr, const char *filePath);
EXPORT_C_FUNC float JuceMixer_getAudioFileDuration(void *ptr, const char *filePath);
EXPORT_C_FUNC const char* JuceMixer_exportToFile(void *ptr, const char *json);

// MARK: JuceMixPlayer + Audio Filters

EXPORT_C_FUNC void juce_initAudioFilters(const char* configJsonString,
                                         const char* configMasterJsonString);

EXPORT_C_FUNC const char* JuceMixPlayer_setVocalFilter(void* ptr,
                                                       const char* marker);

EXPORT_C_FUNC const char* JuceMixPlayer_setMasterFilter(void* ptr,
                                                       const char* marker);

EXPORT_C_FUNC void juce_applyStereoAndNormalizeVocal(const char *inputPath,
                                                     const char *outputPath,
                                                     void (*completion)(const char*));

