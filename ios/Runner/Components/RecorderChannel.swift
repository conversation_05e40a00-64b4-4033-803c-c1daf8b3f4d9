import AVKit

class RecorderChannel: NSObject {

    private enum Error: Swift.Error {
        case error(String)
    }

    private enum RecorderState: String {
        case preparing, ready, recording, finishedSuccess, finishedFailed
    }

    static let shared = RecorderChannel()

    // Constants
    private let channelName = "recorder_channel"
    private let eventChannel = "recorder_channel_events"
    private let eventChannelPlayerProgress = "recorder_channel_events_player_progress"
    private let eventChannelAudioLevels = "recorder_channel_events_audio_levels"

    private var eventChannelSink: FlutterEventSink?
    private var eventChannelPlayerProgressSink: FlutterEventSink?
    private var eventChannelAudioLevelsSink: FlutterEventSink?

    private var recorder: AVAudioRecorder?
    private var selectedDevice: RecorderDevice?
    private var recordEndError: String? = "Unknown"
    private var stopTask: Task<(), Never>?
    private var player = AVAudioPlayer()
    private var timerPlayer = Timer()
    private var timerRecorder = Timer()

    private override init() {
        super.init()
    }

    func load() {
        if
            let delegate = UIApplication.shared.delegate as? FlutterAppDelegate,
            let controller = delegate.window.rootViewController as? FlutterViewController
        {
            let recorderChannel = FlutterMethodChannel(
                name: channelName,
                binaryMessenger: controller.binaryMessenger
            )
            recorderChannel.setMethodCallHandler(methodCallHandler(call:result:))

            [eventChannel, eventChannelPlayerProgress, eventChannelAudioLevels].forEach {
                FlutterEventChannel(
                    name: $0,
                    binaryMessenger: controller.binaryMessenger
                )
                .setStreamHandler(Self.shared)
            }
        } else {
            printc("Error creating recorder_channel")
        }
    }

    private func sendEvent(_ state: RecorderState) {
        Task {
            await Task.sleep(seconds: 0.01) // minor wait and send the event
            eventChannelSink?(state.rawValue)
        }
    }

    private func sendPlayerProgress(_ value: Int) {
        eventChannelPlayerProgressSink?(value)
    }

    private func sendRecorderLevels(_ value: Float) {
        eventChannelAudioLevelsSink?(value)
    }

    private func createError(_ msg: String) -> FlutterError {
        return FlutterError(code: "recorder_error",
                            message: msg,
                            details: nil)
    }

    private func requestMicPermission() async -> Bool {
        if #available(iOS 17.0, *) {
            return await AVAudioApplication.requestRecordPermission()
        } else {
            return await withCheckedContinuation { con in
                AVAudioSession.sharedInstance().requestRecordPermission { granted in
                    return con.resume(returning: granted)
                }
            }
        }
    }

    // main handler
    private func methodCallHandler(call: FlutterMethodCall, result: @escaping FlutterResult) {
        printc("methodCallHandler", call.method)
        Task {
            do {
                let val = try await methodCall(call: call)
                result(val.self is () ? nil : val)
            } catch let err {
                result(createError("\(err)"))
            }
        }
    }

    private func methodCall(call: FlutterMethodCall) async throws -> Any {
        return switch call.method {
        case "prepare":
            try await prepare(
                recPath: call.getArg("recPath")!,
                bgmPath: call.getArg("bgmPath")!,
                progressUpdateInterval: call.getArg("progressUpdateInterval")!,
                audioLevelUpdateInterval: call.getArg("audioLevelUpdateInterval")!
            )
        case "start":
            try start()
        case "stop":
            try await stop()
        case "hasPermission":
            hasPermission()
        case "isRecording":
            isRecording()
        case "listInputDevices":
            listInputDevices()
        case "getSelectedDevice":
            getSelectedDevice()
        case "selectDevice":
            try selectDevice(uid: call.getArg("uid")!)
        case "dispose":
            await dispose()
        default:
            throw Error.error("Flutter method not implemented")
        }
    }

    private func makeSessionActive() async throws {
        for _ in 1...5 {
            do {
                try AVAudioSession.sharedInstance().setCategory(
                    .playAndRecord,
                    options: [
                        .allowBluetoothA2DP
                    ]
                )
                try AVAudioSession.sharedInstance().setActive(true)
                return
            } catch _ {
                await Task.sleep(seconds: 0.5) // wait for 0.5 seconds
            }
        }
        throw Error.error("Failed to make AVAudioSession active")
    }

    private func preparePlayer(bgmUrl: URL, progressUpdateInterval: Int) throws {
        timerPlayer.invalidate()
        player.stop()
        player = try AVAudioPlayer(contentsOf: bgmUrl)
        player.pause()
        player.prepareToPlay()
        player.currentTime = 0
        player.delegate = self
        timerPlayer = Timer(timeInterval: TimeInterval(progressUpdateInterval)/TimeInterval(1000), repeats: true, block: { [weak self] timer in
            if let time = self?.player.currentTime {
                self?.sendPlayerProgress(Int(time * 1000))
            }
        })
    }

    private func prepareAudioLevelsMonitoring(audioLevelUpdateInterval: Int) {
        timerRecorder.invalidate()
        timerRecorder = Timer(timeInterval: TimeInterval(audioLevelUpdateInterval)/TimeInterval(1000), repeats: true, block: { [weak self] timer in
            guard let `self` = self, let recorder else { return }
            recorder.updateMeters()
            let averagePower = recorder.averagePower(forChannel: 0)
            // min -160 dBFS, max 0 dBFS
            self.sendRecorderLevels(averagePower)
        })
    }
}

// Flutter channel methods
extension RecorderChannel {

    private func prepare(
        recPath: String,
        bgmPath: String,
        progressUpdateInterval: Int,
        audioLevelUpdateInterval: Int
    ) async throws {
        sendEvent(.preparing)
        guard let recUrl = URL(string: recPath) else {
            throw Error.error("Unable to create url from \(recPath)")
        }
        guard let bgmUrl = URL(string: bgmPath) else {
            throw Error.error("Unable to create url from \(bgmPath)")
        }

        if recorder?.isRecording ?? false {
            recorder?.stop()
            self.recorder = nil
        }

        let granted = await requestMicPermission()
        if !granted {
            throw Error.error("Microphone permission denied")
        }

        try await makeSessionActive()

        try preparePlayer(bgmUrl: bgmUrl, progressUpdateInterval: progressUpdateInterval)
        prepareAudioLevelsMonitoring(audioLevelUpdateInterval: audioLevelUpdateInterval)

        let settings: [String: Any] = [
            AVFormatIDKey: kAudioFormatLinearPCM,
            AVNumberOfChannelsKey: 1,
            AVSampleRateKey : 48000.0,
            AVEncoderAudioQualityKey: AVAudioQuality.max.rawValue,
            AVLinearPCMBitDepthKey: 16,
            AVLinearPCMIsFloatKey: true
        ]

        recorder = try AVAudioRecorder(url: recUrl, settings: settings)
        recorder?.delegate = self
        recorder?.isMeteringEnabled = true

        let success = recorder?.prepareToRecord() ?? false
        if !success {
            throw Error.error("Failed to native prepareToRecord()")
        }

        sendEvent(.ready)
    }

    /// Returns delay between recording start and player start time (milliseconds)
    private func start() throws -> Int {
        recordEndError = "Unknown"
        guard let recorder = recorder else {
            throw Error.error("prepare() not called before start()")
        }

        if recorder.isRecording {
            return 0
        }
        var time = Date().timeIntervalSince1970
        let success = recorder.record()
        if !success {
            throw Error.error("Failed to native record()")
        }
        time = Date().timeIntervalSince1970 - time
        player.play()
        // start timer for player progress
        RunLoop.main.add(timerPlayer, forMode: .common)
        RunLoop.main.add(timerRecorder, forMode: .common)
        sendEvent(.recording)
        return Int(time * 1000)
    }

    private func stop() async throws {
        timerPlayer.invalidate()
        timerRecorder.invalidate()
        player.stop()
        guard let recorder = recorder, recorder.isRecording else {
            return
        }
        stopTask = Task {
            await Task.sleep(seconds: 20) // 20 seconds timeout
        }
        recorder.stop()
        await stopTask?.value
        sendEvent(recordEndError == nil ? .finishedSuccess : .finishedFailed)
        if let recordEndError {
            throw Error.error(recordEndError)
        }
    }

    private func hasPermission() -> Bool {
        if #available(iOS 17.0, *) {
            return AVAudioApplication.shared.recordPermission == .granted
        } else {
            return AVAudioSession.sharedInstance().recordPermission == .granted
        }
    }

    private func isRecording() -> Bool {
        return recorder?.isRecording ?? false
    }

    private func listInputDevices() -> String {
        let devices = AVAudioSession
            .sharedInstance()
            .availableInputs?
            .map { RecorderDevice(port: $0) }

        return devices?.toJsonString ?? ""
    }

    private func selectDevice(uid: String) throws {
        let devices = AVAudioSession.sharedInstance().availableInputs ?? []
        guard let selected = devices.first(where: { $0.uid == uid }) else {
            return
        }
        try AVAudioSession.sharedInstance().setPreferredInput(selected)
    }

    private func getSelectedDevice() -> String {
        guard let device = AVAudioSession.sharedInstance().preferredInput else {
            return ""
        }
        guard let json = RecorderDevice(port: device).toJsonString else {
            return ""
        }
        return json
    }

    private func dispose() async {
        timerPlayer.invalidate()
        timerRecorder.invalidate()
        player.stop()
        try? await stop()
    }
}

extension RecorderChannel: FlutterStreamHandler {
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        switch arguments as? String {
        case eventChannel:
            eventChannelSink = events
        case eventChannelPlayerProgress:
            eventChannelPlayerProgressSink = events
        case eventChannelAudioLevels:
            eventChannelAudioLevelsSink = events
        default:
            break
        }
        return nil
    }

    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        return nil
    }
}

extension RecorderChannel: AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        player.delegate = nil
        Task {
            try await stop()
        }
    }
}

extension RecorderChannel: AVAudioRecorderDelegate {

    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        defer {
            stopTask?.cancel()
        }
        guard flag else {
            printc("audioRecorderDidFinishRecording: false")
            recordEndError = "Record finished with error"
            return
        }
        let wav = recorder.url.absoluteString
        do {
            if !FileManager.default.fileExists(atPath: wav) {
                throw Error.error("Error converting audio file")
            }
            recordEndError = nil
        } catch let err {
            printc("audioRecorderDidFinishRecording: \(err)")
            recordEndError = "\(err)"
        }
    }
}

extension FlutterMethodCall {
    func getArg<T>(_ key: String) -> T? {
        guard
            let arguments = self.arguments,
            let args = arguments as? [String: Any],
            let value = args[key] as? T
        else {
            return nil
        }
        return value
    }
}

struct RecorderDevice: Codable {

    let uid: String
    let portName: String
    let portType: String

    init(port: AVAudioSessionPortDescription) {
        uid = port.uid
        portName = port.portName
        portType = port.portType.rawValue
    }

    func getDevice() -> AVAudioSessionPortDescription? {
        guard let device = AVAudioSession
            .sharedInstance()
            .availableInputs?
            .first(where: { $0.uid == uid }) else {
            return nil
        }
        return device
    }
}
