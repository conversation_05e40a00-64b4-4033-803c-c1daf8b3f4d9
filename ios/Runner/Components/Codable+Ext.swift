//
//  Codable.swift
//  melodyze-core
//
//  Created by <PERSON><PERSON> on 08/12/23.
//

import Foundation

extension Task where Success == Never, Failure == Never {
    static func sleep(seconds: Double) async {
        let nanoseconds = UInt64(seconds * 1_000_000_000)  // Convert seconds to nanoseconds
        try? await Task.sleep(nanoseconds: nanoseconds)
    }
}

public extension Decodable {
    init?(jsonString: String?) {
        if let jsonString = jsonString, let data = jsonString.data(using: .utf8) {
            do {
                let obj = try JSONDecoder().decode(Self.self, from: data)
                self = obj
                return
            } catch let err {
                printc(err, String(describing: Self.self))
                return nil
            }
        }
        return nil
    }
    
    init(jsonString_: String?) throws {
        if let jsonString = jsonString_, let data = jsonString.data(using: .utf8) {
            do {
                let obj = try JSONDecoder().decode(Self.self, from: data)
                self = obj
                return
            } catch let err {
                printc(err, String(describing: Self.self))
                throw err
            }
        }
        throw NSError(domain: "JSONDecoder error", code: -1)
    }
}

public extension Encodable {
    var toJsonString: String? {
        do {
            let data = try JSONEncoder().encode(self)
            return String(data: data, encoding: .utf8)
        } catch let err {
            printc(err)
            return nil
        }
    }
    
    var toDictionary: [String: Any]? {
        guard let data = try? JSONEncoder().encode(self) else { return nil }
        return (try? JSONSerialization.jsonObject(with: data, options: .allowFragments))
            .flatMap { $0 as? [String: Any] }
    }

    var toArray: [[String: Any]]? {
        guard let data = try? JSONEncoder().encode(self) else { return nil }
        return (try? JSONSerialization.jsonObject(with: data, options: .allowFragments))
            .flatMap { $0 as? [[String: Any]] }
    }
}

public extension String {
    var toDictionary: [String: Any]? {
        do {
            if let data = self.data(using: .utf8) {
                if let dict = try JSONSerialization.jsonObject(with: data) as? [String : Any] {
                    return dict
                }
            }
        } catch let err {
            printc(err)
        }
        return nil
    }
}
