//
//  Print.swift
//  melodyze-core
//
//  Created by <PERSON><PERSON> on 11/12/23.
//

import Foundation
import OSLog

@inline(__always)
public func printc(_ args: Any..., file: String = #file, line: Int = #line) {
//#if DEBUG
//    let file = NSString(string: file)
//    let last = file.lastPathComponent.split(separator: ".").first!
//    print("⬛️\(last):\(line):", args.map({ "\($0)" }).joined(separator: ", "))
//#endif
}

@inline(__always)
public func printError(_ args: Any..., file: String = #file, line: Int = #line) {
//#if DEBUG
//    let file = NSString(string: file)
//    let last = file.lastPathComponent.split(separator: ".").first!
//    print("🟥\(last):\(line):", args.map({ "\($0)" }).joined(separator: ", "))
//#endif
}
