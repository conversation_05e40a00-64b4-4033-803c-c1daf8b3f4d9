import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
    override func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        RecorderChannel.shared.load()
        GeneratedPluginRegistrant.register(with: self)
        juceMessageManagerInit()
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
}
