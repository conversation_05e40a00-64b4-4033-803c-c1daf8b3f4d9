import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class VocalFiltersRoutesPath {
  static const String vocalFilters = '/vocalFilters';
}

class VocalFiltersRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: VocalFiltersRoutesPath.vocalFilters,
          page: VocalFiltersRoute.page,
        ),
      ];
}
