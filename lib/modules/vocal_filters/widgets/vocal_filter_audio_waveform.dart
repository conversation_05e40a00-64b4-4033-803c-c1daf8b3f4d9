// import 'package:audio_waveforms/audio_waveforms.dart';
// import 'package:flutter/material.dart';
// import 'package:melodyze/core/ui/tokens/app_colors.dart';
// import 'package:melodyze/enums.dart';

// class VocalFilterAudioWaveform extends StatefulWidget {
//   final AudioWaveformPlayerController controller;
//   final void Function(int)? onDelayChange;
//   final Duration bgmStartOffset;

//   const VocalFilterAudioWaveform({
//     super.key,
//     required this.controller,
//     this.onDelayChange,
//     required this.bgmStartOffset,
//   });

//   @override
//   State<VocalFilterAudioWaveform> createState() => _VocalFilterAudioWaveformState();
// }

// extension PlayerControllerX on PlayerController {
//   Stream<Duration> get onCurrentDuration => onCurrentDurationChanged.map((event) => Duration(milliseconds: event));
// }

// class _VocalFilterAudioWaveformState extends State<VocalFilterAudioWaveform> {
//   late Future<bool> setupController;

//   @override
//   void initState() {
//     super.initState();
//     setupController = widget.controller.setupController();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder(
//         future: setupController,
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return const Center(child: CircularProgressIndicator());
//           } else if (snapshot.hasError) {
//             return Center(child: Text(snapshot.error.toString()));
//           }
//           return AudioFileWaveforms(
//             size: Size(MediaQuery.of(context).size.width, 100.0),
//             onSeek: widget.controller.type.isVocal
//                 ? (milliseconds) {
//                     widget.onDelayChange?.call(milliseconds);
//                   }
//                 : null,
//             playerController: widget.controller.playerController,
//             enableSeekGesture: widget.controller.type.isVocal,
//             waveformType: WaveformType.long,
//             waveformData: widget.controller.playerController.waveformData,
//             bgmStartTimeInMilliseconds: widget.controller.type.isVocal ? null : widget.bgmStartOffset.inMilliseconds,
//             playerWaveStyle: const PlayerWaveStyle(
//               fixedWaveColor: Colors.white,
//               liveWaveColor: AppColors.primaryAccent,
//               spacing: 4,
//               showSeekLine: false,
//             ),
//           );
//         });
//   }
// }

// class AudioWaveformPlayerController {
//   final String path;
//   final AudioSourceType type;
//   AudioWaveformPlayerController({
//     required this.path,
//     required this.type,
//   });

//   final playerController = PlayerController();

//   Future<bool> setupController() async {
//     try {
//       await playerController.preparePlayer(
//         path: path,
//         shouldExtractWaveform: true,
//         noOfSamples: 100,
//         volume: 0.0,
//       );
//       return true;
//     } catch (e) {
//       rethrow;
//     }
//   }
// }
