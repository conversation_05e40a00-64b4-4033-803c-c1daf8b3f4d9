// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:juce_mix_player/juce_mix_player.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/atom/app_slider.dart';
import 'package:melodyze/core/ui/molecules/butons/transparent_round_icon_button.dart';
import 'package:melodyze/core/ui/molecules/center_origin_horizontal_slider.dart';
import 'package:melodyze/core/ui/molecules/custom_vertical_slider.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/ui/molecules/expandable_swipe_widget.dart';
import 'package:melodyze/core/ui/molecules/filter_item.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/molecules/tab_bar_view.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/wrappers/remote_config_helper.dart';
import 'package:melodyze/modules/recording/cubit/download_wav_file_cubit.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
import 'package:melodyze/modules/vocal_filters/bloc/vocal_filters_bloc.dart';
import 'package:melodyze/modules/vocal_filters/vocal_filter_item.dart';

@RoutePage()
class VocalFiltersScreen extends StatelessWidget {
  final String recordedVocalPath;
  final String normalizedRecordingPath;
  final DownloadWavFileCubit downloadWavFileCubit;
  final int timeDiff;
  final MixerComposeModel mixComposeModel;

  const VocalFiltersScreen({
    super.key,
    required this.recordedVocalPath,
    required this.normalizedRecordingPath,
    required this.downloadWavFileCubit,
    required this.timeDiff,
    required this.mixComposeModel,
  });

  @override
  Widget build(BuildContext context) {
    final JuceMixBloc juceMixBloc = JuceMixBloc(mixComposeModel: mixComposeModel, initFrom: "VF");
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => VocalfiltersBloc(
            shareDataBloc: context.read<ShareDataBloc>(),
            recordedVocalPath: recordedVocalPath,
            normalizedRecordingPath: normalizedRecordingPath,
            juceMixPlayerBloc: juceMixBloc,
            downloadWavFileCubit: downloadWavFileCubit,
            timeDiff: timeDiff,
          ),
        ),
        BlocProvider.value(value: juceMixBloc),
      ],
      child: _VocalFiltersScreen(
        normalizedRecordingPath: normalizedRecordingPath,
      ),
    );
  }
}

class _VocalFiltersScreen extends StatefulWidget {
  final String normalizedRecordingPath;

  const _VocalFiltersScreen({
    required this.normalizedRecordingPath,
  });

  @override
  State<_VocalFiltersScreen> createState() => _VocalFiltersScreenState();
}

class _VocalFiltersScreenState extends State<_VocalFiltersScreen> with TickerProviderStateMixin {
  late final TabController _tabController;

  @override
  void initState() {
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {});
    });
    super.initState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MeloScaffold(
      popScopeDescription: 'Are you sure you want to discard the settings ?',
      onPopScopeDiscard: () => context.read<VocalfiltersBloc>().add(StopEvent()),
      showBackground: false,
      overlayAction: Positioned(
        top: MediaQuery.of(context).padding.top,
        child: Center(
          child: BlocBuilder<JuceMixBloc, JuceMixState>(
            buildWhen: (previous, current) => current is JuceMixAudioLoaded || current is JuceMixAudioError || current is JuceMixAudioInitEvent,
            builder: (context, state) {
              final juceMixBloc = context.read<JuceMixBloc>();
              final shareDataBloc = context.read<ShareDataBloc>();
              final guideVocalPath = shareDataBloc.annotatedData?.guideVocalPath;
              return ExpandableSwipeWidget(
                isLoading: state is JuceMixAudioInitEvent,
                items: [
                  ExpandableSwipeWidgetModel(
                    title: "Guide",
                    label: "Guide",
                    iconPath: AssetPaths.guide,
                    isDisabled: !juceMixBloc.isGuideEnabled,
                    initialVolume: juceMixBloc.guideVol,
                    onVolumeChanged: guideVocalPath != null
                        ? (value) {
                            juceMixBloc.add(JuceMixToggleGuideEvent(enable: true, volume: value));
                          }
                        : null,
                    onPressed: guideVocalPath != null
                        ? (enable) {
                            juceMixBloc.add(JuceMixToggleGuideEvent(enable: !enable, volume: 0));
                          }
                        : null,
                  ),
                  ExpandableSwipeWidgetModel(
                    title: "Metronome",
                    label: "Click",
                    iconPath: AssetPaths.metronome,
                    onVolumeChanged: (value) {
                      juceMixBloc.add(JuceMixToggleMetronomeEvent(enable: true, volume: value));
                    },
                    isDisabled: !juceMixBloc.isMetronomeEnabled,
                    initialVolume: juceMixBloc.metronomeVol,
                    onPressed: (enable) {
                      juceMixBloc.add(JuceMixToggleMetronomeEvent(enable: !enable, volume: 0));
                    },
                  )
                ],
              );
            },
          ),
        ),
      ),
      secondaryAction: (context) => BlocBuilder<VocalfiltersBloc, VocalfiltersState>(
        buildWhen: (previous, current) => current is! VocalFilterItemUpdated && current is! VocalFilterAdjustDelayState,
        builder: (context, state) {
          if (state is VocalFilterLoadingState) {
            return SizedBox();
          }

          return GestureDetector(
            onTap: () => _onSave(context),
            child: Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Column(
                children: [
                  ImageLoader.fromAsset(
                    AssetPaths.saveButton,
                    height: 20,
                  ),
                  Text(
                    "Save",
                    style: AppTextStyles.text14regular.copyWith(
                      fontFamily: AppFonts.iceland,
                    ),
                  )
                ],
              ),
            ),
          );
        },
      ),
      body: SafeArea(
          child: Stack(
        children: [
          BlocConsumer<VocalfiltersBloc, VocalfiltersState>(
              listenWhen: (_, current) => current is VocalFilterAdjustDelayState,
              listener: (context, state) {
                // if (state is VocalFilterAdjustDelayState) {
                //   AudioWaveform.showAudioAdjustmentBottomsheet(
                //     context: context,
                //     songPath: state.songPath,
                //     vocalPath: recordedAudioVideoPath,
                //     vocalFilterAudioPlayerBloc: context.read<VocalFilterAudioPlayerBloc>(),
                //     bgmStartOffset: lyricsData?.lyrics.data.first.endTime.parseAsDuration() ?? Duration.zero,
                //   );
                // }
              },
              buildWhen: (previous, current) => current is! VocalFilterItemUpdated && current is! VocalFilterAdjustDelayState,
              builder: (context, state) {
                if (state is VocalFilterLoadingState) {
                  return const Center(
                    child: AppCircularProgressIndicator(),
                  );
                }
                if (state is VocalFilterLoadedState || state is VocalFilterPlayerErrorState) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(
                        height: 16,
                      ),
                      state is VocalFilterLoadedState
                          ? Expanded(
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.bottomCenter,
                                    end: Alignment.topCenter,
                                    colors: [
                                      Colors.black,
                                      Colors.black,
                                      Colors.black,
                                      Colors.black.withAlpha((0.5 * 255).toInt()),
                                      Colors.black.withAlpha(0),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(12), // Apply the same border radius as ClipRRect
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 0,
                                        right: 0,
                                        top: 0,
                                        bottom: 0,
                                        child: BlocBuilder<JuceMixBloc, JuceMixState>(
                                          buildWhen: (previous, current) =>
                                              (current is JuceMixAudioPlaying || current is JuceMixAudioLoaded || current is JuceMixAudioPaused || current is JuceMixAudioStopped),
                                          builder: (context, state) {
                                            return ImageLoader.fromAsset(
                                              state is JuceMixAudioPlaying ? AssetPaths.catRoundEqualizer : AssetPaths.catRoundEqualizerStatic,
                                            );
                                          },
                                        ),
                                      ),
                                      Stack(
                                        children: [
                                          Positioned(
                                            left: 8,
                                            bottom: 68,
                                            top: 100,
                                            child: CustomVerticalSlider(
                                                value: 1,
                                                max: 1,
                                                label: 'VOCAL',
                                                color: AppColors.pink,
                                                alignment: SliderAlignment.left,
                                                onChangeStart: () {
                                                  context.read<VocalfiltersBloc>().add(VolumeSeekStartEvent(isVocal: true, tabIndex: _tabController.index));
                                                },
                                                onChangeEnd: (value) {
                                                  context.read<VocalfiltersBloc>().add(VolumeSeekEndEvent(isVocal: true, value: value, tabIndex: _tabController.index));
                                                }),
                                          ),
                                          Positioned(
                                            right: 8,
                                            bottom: 68,
                                            top: 100,
                                            child: CustomVerticalSlider(
                                                value: 1,
                                                max: 1,
                                                label: 'MUSIC',
                                                color: AppColors.pink2,
                                                alignment: SliderAlignment.right,
                                                onChangeStart: () {
                                                  context.read<VocalfiltersBloc>().add(VolumeSeekStartEvent(isVocal: false, tabIndex: _tabController.index));
                                                },
                                                onChangeEnd: (value) {
                                                  context.read<VocalfiltersBloc>().add(VolumeSeekEndEvent(isVocal: false, value: value, tabIndex: _tabController.index));
                                                }),
                                          ),
                                          Positioned(
                                            left: 8,
                                            right: 8,
                                            top: 20,
                                            child: CenterOriginHorizontalSlider(
                                              initialValue: context.read<VocalfiltersBloc>().userGivenDelay,
                                              range: RemoteConfigHelper.getInt(RemoteConfigKeys.delayRange),
                                              onChangeStart: () {
                                                context.read<VocalfiltersBloc>().add(DelaySeekStartEvent());
                                              },
                                              onChangeEnd: (value) {
                                                context.read<VocalfiltersBloc>().add(DelaySeekEndEvent(delay: value));
                                              },
                                            ),
                                          ),
                                          Positioned(
                                            left: 62,
                                            right: 62,
                                            bottom: 62,
                                            top: 100,
                                            child: state.isLoading
                                                ? const Center(
                                                    child: SizedBox(
                                                      height: 60,
                                                      width: 60,
                                                      child: AppCircularProgressIndicator(),
                                                    ),
                                                  )
                                                : const SizedBox.shrink(),
                                          ),
                                          Positioned(
                                            bottom: 20,
                                            left: 8,
                                            right: 8,
                                            child: Row(
                                              children: [
                                                BlocBuilder<JuceMixBloc, JuceMixState>(
                                                  buildWhen: (previous, current) => (current is JuceMixAudioPlaying ||
                                                      current is JuceMixAudioLoaded ||
                                                      current is JuceMixAudioPaused ||
                                                      current is JuceMixAudioStopped),
                                                  builder: (context, state) {
                                                    return Padding(
                                                      padding: const EdgeInsets.only(right: 8.0),
                                                      child: AppGradientContainer(
                                                        gradient: AppGradients.gradientPinkPurpleTransparent,
                                                        borderGradient: AppGradients.gradientPinkBorder,
                                                        borderRadius: BorderRadius.circular(32),
                                                        borderWidth: 1,
                                                        height: 36,
                                                        width: 36,
                                                        shape: BoxShape.circle,
                                                        child: TransParentRoundIconButton(
                                                          icon: state is JuceMixAudioPlaying ? Icons.pause : Icons.play_arrow,
                                                          backgroundColor: Colors.transparent,
                                                          onPressed: () {
                                                            state is JuceMixAudioPlaying
                                                                ? context.read<VocalfiltersBloc>().add(PauseEvent())
                                                                : context.read<VocalfiltersBloc>().add(PlayEvent());
                                                          },
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ),
                                                BlocBuilder<JuceMixBloc, JuceMixState>(
                                                  buildWhen: (previous, current) => (current is JuceMixAudioPlaying ||
                                                      current is JuceMixAudioLoaded ||
                                                      current is JuceMixAudioPaused ||
                                                      current is JuceMixAudioStopped ||
                                                      current is JuceMixAudioProgressUpdated),
                                                  builder: (context, state) {
                                                    if (state is JuceMixAudioError) {
                                                      return AppSlider(
                                                        value: 0,
                                                        max: 100,
                                                        onChanged: (value) {},
                                                      );
                                                    }

                                                    final juceMixBloc = context.read<JuceMixBloc>();
                                                    final double audioCurrentPosition =
                                                        state is JuceMixAudioProgressUpdated ? state.position.toDouble() : juceMixBloc.currentPosition;
                                                    return Expanded(
                                                      child: AppSlider(
                                                        value: audioCurrentPosition,
                                                        max: (context.read<VocalfiltersBloc>().recDuration * 1000),
                                                        onChangeStart: (value) {
                                                          context.read<VocalfiltersBloc>().add(SeekStartEvent());
                                                        },
                                                        onChangeEnd: (value) {
                                                          context.read<VocalfiltersBloc>().add(SeekEndEvent(milliseconds: value));
                                                        },
                                                        activeColor: AppColors.pink2,
                                                        thumbRadius: 0.0,
                                                        trackHeight: 1,
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : const Text('Something went wrong!'),
                      const SizedBox(
                        height: 8,
                      ),
                      TabBars<String>(
                        initialIndex: 0,
                        values: const ['Mix Filters', 'Master Filters'],
                        selectedItemColor: AppColors.brightPurple,
                        unselectedItemColor: AppColors.grey7E7D7D,
                        tabBarPadding: const EdgeInsets.symmetric(horizontal: 4),
                        tabTextStyle: AppTextStyles.text18regular,
                        onTap: (index) {
                          _tabController.index = index;
                        },
                        // Filter Tabs
                        child: Column(
                          children: [
                            SizedBox(height: 4),
                            SizedBox(
                              height: 125,
                              child: TabBarView(
                                controller: _tabController,
                                children: [
                                  createMixFiltersView(context, state),
                                  createMasterFiltersView(context, state),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }
                if (state is VocalFilterErrorState) {
                  return Center(
                    child: Text(state.error.toString()),
                  );
                }
                return const SizedBox.shrink();
              }),
        ],
      )),
    );
  }

  Widget createMixFiltersView(BuildContext context, VocalfiltersState state) {
    return BlocBuilder<VocalfiltersBloc, VocalfiltersState>(
        buildWhen: (previous, current) => current is VocalFilterItemUpdated,
        builder: (context, state) {
          VocalfiltersBloc bloc = context.read<VocalfiltersBloc>();
          List<VocalFilterItem> filterItems = bloc.mixFilters;
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: Container(
                  height: 125,
                  color: AppColors.black010101_25,
                  child: ListView.separated(
                    padding: const EdgeInsets.fromLTRB(8, 0, 16, 0),
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) => FilterItem(
                      title: filterItems[index].title,
                      thumbnailPath: filterItems[index].thumbnailPath,
                      isSelected: bloc.selectedMixFilterIndex == index,
                      onTap: () {
                        bloc.add(SelectMixFilterEvent(selectedFilterIndex: index));
                      },
                    ),
                    separatorBuilder: ((context, index) => const SizedBox(width: 8)),
                    itemCount: filterItems.length,
                  ),
                ),
              ),
            ],
          );
        });
  }

  Widget createMasterFiltersView(BuildContext context, VocalfiltersState state) {
    return BlocBuilder<VocalfiltersBloc, VocalfiltersState>(
        buildWhen: (previous, current) => current is VocalFilterItemUpdated,
        builder: (context, state) {
          VocalfiltersBloc bloc = context.read<VocalfiltersBloc>();
          List<VocalFilterItem> filterItems = bloc.masterFilters;
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), bottomLeft: Radius.circular(16)),
                  child: Container(
                    height: 125,
                    color: AppColors.black010101_25,
                    child: ListView.separated(
                      padding: const EdgeInsets.fromLTRB(8, 0, 16, 0),
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (context, index) => FilterItem(
                        title: filterItems[index].title,
                        thumbnailPath: filterItems[index].thumbnailPath,
                        isSelected: bloc.selectedMasterFilterIndex == index,
                        onTap: () {
                          bloc.add(SelectMasterFilterEvent(selectedFilterIndex: index));
                        },
                      ),
                      separatorBuilder: ((context, index) => const SizedBox(width: 8)),
                      itemCount: filterItems.length,
                    ),
                  ),
                ),
              ),
            ],
          );
        });
  }

  void _onSave(BuildContext context) async {
    final result = await showYesNoDialog(
      context: context,
      title: 'Save and Upload',
      subTitle: 'Are you sure you want to save and upload this recording ?',
    );

    if (result && context.mounted) {
      final jmb = context.read<JuceMixBloc>();
      jmb.add(JuceMixAudioStopEvent());
      jmb.add(JuceMixToggleMetronomeEvent(enable: false, volume: 0, showToast: false));
      jmb.add(JuceMixToggleGuideEvent(enable: false, volume: 0, showToast: false));
      unawaited(
        context.pushRoute(
          SaveAndUploadRoute(
            vocalfiltersBloc: context.read<VocalfiltersBloc>(),
            shareDataBloc: context.read<ShareDataBloc>(),
            juceMixBloc: context.read<JuceMixBloc>(),
          ),
        ),
      );
    }
  }
}
