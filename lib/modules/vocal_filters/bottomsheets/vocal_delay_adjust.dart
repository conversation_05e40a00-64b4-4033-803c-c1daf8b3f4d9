// import 'dart:async';

// // import 'package:audio_waveforms/audio_waveforms.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:melodyze/core/ui/tokens/app_colors.dart';
// import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
// import 'package:melodyze/enums.dart';
// import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
// import 'package:melodyze/modules/vocal_filters/widgets/vocal_filter_audio_waveform.dart';

// class AudioWaveform extends StatefulWidget {
//   final String songPath;
//   final String vocalPath;
//   final Duration bgmStartOffset;

//   const AudioWaveform({
//     super.key,
//     required this.songPath,
//     required this.vocalPath,
//     required this.bgmStartOffset,
//   });

//   AudioWaveform.showAudioAdjustmentBottomsheet({
//     super.key,
//     required BuildContext context,
//     required this.songPath,
//     required this.vocalPath,
//     required JuceMixBloc juceMixBloc,
//     required this.bgmStartOffset,
//   }) {
//     showModalBottomSheet(
//       context: context,
//       isDismissible: false,
//       builder: (context) => BlocProvider.value(
//         value: juceMixBloc,
//         child: AudioWaveform(
//           songPath: songPath,
//           vocalPath: vocalPath,
//           bgmStartOffset: bgmStartOffset,
//         ),
//       ),
//     );
//   }

//   @override
//   State<AudioWaveform> createState() => _AudioWaveformState();
// }

// class _AudioWaveformState extends State<AudioWaveform> {
//   late final AudioWaveformPlayerController songController;
//   late final AudioWaveformPlayerController vocalController;

//   // late StreamSubscription<PlayerState> vocalPlayerStateSubscription;

//   int currentDelayOffset = 0;

//   @override
//   void initState() {
//     super.initState();
//     songController = AudioWaveformPlayerController(path: widget.songPath, type: AudioSourceType.bgm);
//     vocalController = AudioWaveformPlayerController(path: widget.vocalPath, type: AudioSourceType.vocal);

//     // vocalPlayerStateSubscription = vocalController.playerController.onPlayerStateChanged.listen((state) {
//     //   if (state == PlayerState.stopped) {
//     //     songController.playerController.stopPlayer();
//     //   }
//     //   setState(() {});
//     // });
//   }

//   void playPausePlayer() async {
//     if (vocalController.playerController.playerState.isPlaying) {
//       unawaited(vocalController.playerController.pausePlayer());
//       unawaited(songController.playerController.pausePlayer());
//     } else {
//       context.read<JuceMixBloc>().add(JuceMixAudioPlayEvent());
//       unawaited(songController.playerController.seekTo(widget.bgmStartOffset.inMilliseconds));
//       if (currentDelayOffset > 0 && vocalController.playerController.playerState.isInitialised) {
//         unawaited(vocalController.playerController.seekTo(currentDelayOffset));
//       }
//       unawaited(vocalController.playerController.startPlayer());
//       unawaited(songController.playerController.startPlayer());
//     }
//   }

//   void onPlayerButtonPressed() {
//     if (vocalController.playerController.playerState.isStopped) {
//       playPausePlayer();
//     } else {
//       playPausePlayer();
//     }
//   }

//   IconData iconBasedOnPlayerState() {
//     if (vocalController.playerController.playerState.isPlaying) {
//       return Icons.pause_circle_filled_rounded;
//     } else {
//       return Icons.play_circle_filled_rounded;
//     }
//   }

//   Widget buildWaveform(AudioWaveformPlayerController controller) {
//     return Row(
//       children: [
//         Padding(
//           padding: const EdgeInsets.all(8.0),
//           child: Text(controller.type.title, style: const TextStyle(color: Colors.white)),
//         ),
//         Expanded(
//           child: Container(
//             decoration: const BoxDecoration(
//               border: Border(
//                 top: BorderSide(color: Colors.white),
//                 left: BorderSide(color: Colors.white),
//                 bottom: BorderSide(color: Colors.white),
//               ),
//               borderRadius: BorderRadius.only(
//                 topLeft: Radius.circular(16),
//                 bottomLeft: Radius.circular(16),
//               ),
//             ),
//             child: VocalFilterAudioWaveform(
//               controller: controller,
//               bgmStartOffset: widget.bgmStartOffset,
//               onDelayChange: (milliseconds) {
//                 context.read<JuceMixBloc>().add(JuceMixAudioSeekEvent(milliseconds: milliseconds.toDouble()));
//                 setState(() {
//                   currentDelayOffset = milliseconds;
//                 });
//               },
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return ColoredBox(
//       color: AppColors.primary100,
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Padding(
//             padding: const EdgeInsets.all(8.0),
//             child: Text(
//               'Delay - ${Duration(milliseconds: currentDelayOffset).toHHMMSS()}',
//               style: AppTextStyles.headline5.copyWith(color: Colors.white),
//             ),
//           ),
//           Column(
//             children: [
//               buildWaveform(songController),
//               const SizedBox(height: 16),
//               buildWaveform(vocalController),
//               IconButton(
//                 onPressed: onPlayerButtonPressed,
//                 icon: Icon(
//                   iconBasedOnPlayerState(),
//                   color: Colors.white,
//                   size: 48,
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }

//   @override
//   void dispose() {
//     songController.playerController.dispose();
//     vocalController.playerController.dispose();
//     vocalPlayerStateSubscription.cancel();
//     super.dispose();
//   }
// }
