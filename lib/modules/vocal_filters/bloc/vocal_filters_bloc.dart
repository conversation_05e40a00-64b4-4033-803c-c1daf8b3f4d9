import 'dart:async';
import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:juce_mix_player/juce_mix_player.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/services/file_manager/file_manager.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';
import 'package:melodyze/modules/recording/cubit/download_wav_file_cubit.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
import 'package:melodyze/modules/vocal_filters/vocal_filter_item.dart';

part 'vocal_filters_event.dart';
part 'vocal_filters_state.dart';

class VocalfiltersBloc extends SafeBloc<VocalFiltersEvent, VocalfiltersState> {
  final JuceMixBloc juceMixPlayerBloc;
  final ShareDataBloc shareDataBloc;
  final DownloadWavFileCubit downloadWavFileCubit;
  final int timeDiff;
  final String recordedVocalPath;
  final String normalizedRecordingPath;
  String bgmWavPath = "";
  String recordedVocalS3path = "";

  String vocalFilterName = "None";
  String masterFilterName = "None";
  int selectedMixFilterIndex = 0;
  int selectedMasterFilterIndex = 0;
  double vocalVolume = 1.0;
  double musicVolume = 1.0;
  int delayFinal = 0;
  int userGivenDelay = 0;
  double vocalFromTime = 0;
  double vocalOffset = 0;
  double recDuration = 0;
  List<VocalFilterItem> _mixFilters = [];
  List<VocalFilterItem> _masterFilters = [];

  final Debouncer _debouncer = Debouncer();
  StreamSubscription<DownloadWavFileState>? _downloadWavFileListener;
  StreamSubscription<JuceMixState>? _vocalPlayerListener;

  // Seeking state tracking
  bool _wasPlayingBeforeSeek = false;
  bool _isUserPaused = false;

  VocalfiltersBloc({
    required this.recordedVocalPath,
    required this.normalizedRecordingPath,
    required this.juceMixPlayerBloc,
    required this.shareDataBloc,
    required this.downloadWavFileCubit,
    required this.timeDiff,
  }) : super(VocalFilterInitialState()) {
    on<VocalFiltersLoadEvent>(_loadVocalFiltersData);
    on<PlayEvent>(_onPlay);
    on<PauseEvent>(_onPause);
    on<StopEvent>(_onStop);
    on<SeekStartEvent>(_onSeekStart);
    on<SeekEndEvent>(_onSeekEnd);
    on<DelaySeekStartEvent>(_onDelaySeekStart);
    on<DelaySeekEndEvent>(_onDelaySeekEnd);
    on<VolumeSeekStartEvent>(_onVolumeSeekStart);
    on<VolumeSeekEndEvent>(_onVolumeSeekEnd);
    on<SelectMixFilterEvent>(_selectMixFilter);
    on<SelectMasterFilterEvent>(_selectMasterFilter);
    on<CheckWavFileDownloadStatus>(_checkWavFileDownloadStatus);
    on<MixFilterEditEvent>(_onMixFilterEdit);
    on<MasterFilterEditEvent>(_onMasterFilterEdit);
    add(VocalFiltersLoadEvent());
    add(CheckWavFileDownloadStatus());
  }

  /// Initialize delay values from local storage
  void _initializeDelayValues() {
    userGivenDelay = CommonUtils.getUserDelay();
    _calculateDelayOffset(userGivenDelay);
  }

  FutureOr<void> _initializeJuceMixComposeModel() async {
    try {
      recDuration = await JuceMixer().getAudioFileDuration(normalizedRecordingPath);
      juceMixPlayerBloc.outputDuration = recDuration;
      await _updateJuceMixComposeModel();

      juceMixPlayerBloc.add(JuceMixAudioInitEvent( ));
      emit(const VocalFilterLoadedState());
    } catch (e) {
      logger.e("Error initializing juceMixComposeModel: $e");
      emit(VocalFilterErrorState("Error initializing audio: ${e.toString()}"));
    }
  }

  @override
  Future<void> close() async {
    logger.d("VocalfiltersBloc closed");
    await _vocalPlayerListener?.cancel();
    await _downloadWavFileListener?.cancel();
    await CommonUtils.saveUserDelay(userGivenDelay);
    _debouncer.dispose();
    unawaited(super.close());
  }

  FutureOr<void> _checkWavFileDownloadStatus(CheckWavFileDownloadStatus event, _) async {
    if (downloadWavFileCubit.state is DownloadWavFileInProgressState) {
      emit(VocalFilterLoadingState());
      _downloadWavFileListener = downloadWavFileCubit.stream.firstWhere((state) => state is! DownloadWavFileInProgressState).asStream().listen((_) {
        onWavFileDownloadCompletion();
      });
    } else {
      onWavFileDownloadCompletion();
    }
  }

  void onWavFileDownloadCompletion() {
    if (downloadWavFileCubit.state is DownloadWavFileSuccessState) {
      bgmWavPath = downloadWavFileCubit.bgmWavPath!;
      _initializeJuceMixComposeModel();
    } else if (downloadWavFileCubit.state is DownloadWavFileErrorState) {
      emit(const VocalFilterErrorState("Failed to load BGM, Please try again later."));
    }
  }

  FutureOr<void> _loadVocalFiltersData(VocalFiltersLoadEvent event, _) async {
    emit(VocalFilterLoadingState());
    try {
      if (juceMixPlayerBloc.mixComposeModel.tracks?.any((track) => track.id == "vocal") ?? false) {
        logger.d("Vocal is already added to juceMixComposeModel, don't add it again");
      } else {
        juceMixPlayerBloc.mixComposeModel.tracks?.add(MixerTrack(
          id: "vocal",
          path: normalizedRecordingPath,
          enabled: true,
          volume: vocalVolume,
          offset: vocalOffset,
          fromTime: vocalFromTime,
        ));
      }
      _initializeDelayValues();

      // Initialize mix filters and master filters with genre sequence
      await getMixFilters();
      await getMasterFilters();

      unawaited(FileManagerQuickAccess.upload(
        recordedVocalPath,
        Endpoints.getUploadRawVocalCachedSignedUrl,
        FileType.others,
      ).then((result) {
        if (result is FileOperationSuccess) {
          recordedVocalS3path = result.localPath;
          logger.i("_uploadRawVocal: completed");
        } else {
          logger.e("_uploadRawVocal: failed", error: (result as FileOperationError).message);
          emit(VocalFilterErrorState("Unable upload recorded audio"));
          throw Exception("Recording upload error");
        }
      }));

      _vocalPlayerListener = juceMixPlayerBloc.stream.listen((state) {
        if (state is JuceMixAudioStopped) {
          add(StopEvent());
        }
      });

      // Initialize JuceMixComposeModel if bgmWavPath is already available
      // Otherwise, it will be initialized in onWavFileDownloadCompletion
      if (downloadWavFileCubit.state is DownloadWavFileSuccessState && downloadWavFileCubit.bgmWavPath != null) {
        bgmWavPath = downloadWavFileCubit.bgmWavPath!;
        await _initializeJuceMixComposeModel();
      }
    } catch (e) {
      logger.e(e.toString());
      emit(VocalFilterErrorState(e.toString()));
    }
  }

  FutureOr<void> _onPlay(PlayEvent event, _) async {
    _isUserPaused = false;
    juceMixPlayerBloc.add(JuceMixAudioTogglePlayPauseEvent());
  }

  FutureOr<void> _onPause(PauseEvent event, _) async {
    _isUserPaused = true;
    juceMixPlayerBloc.add(JuceMixAudioTogglePlayPauseEvent());
  }

  FutureOr<void> _onStop(StopEvent event, _) async {
    juceMixPlayerBloc.add(JuceMixAudioStopEvent());
  }

  FutureOr<void> _onSeekStart(SeekStartEvent event, _) async {
    _wasPlayingBeforeSeek = juceMixPlayerBloc.isAudioPlaying;
    _isUserPaused = false;
    juceMixPlayerBloc.add(JuceMixAudioPauseEvent());
  }

  FutureOr<void> _onSeekEnd(SeekEndEvent event, _) async {
    juceMixPlayerBloc.add(JuceMixAudioSeekEvent(milliseconds: event.milliseconds));
    if (_wasPlayingBeforeSeek && !_isUserPaused) {
      juceMixPlayerBloc.add(JuceMixAudioPlayEvent());
    }
  }

  FutureOr<void> _onVolumeSeekStart(VolumeSeekStartEvent event, _) async {
    _wasPlayingBeforeSeek = juceMixPlayerBloc.isAudioPlaying;
    _isUserPaused = false;
    juceMixPlayerBloc.add(JuceMixAudioPauseEvent());
  }

  FutureOr<void> _onVolumeSeekEnd(VolumeSeekEndEvent event, _) async {
    if (event.isVocal) {
      vocalVolume = (event.value);
    } else {
      musicVolume = (event.value);
    }

    if (event.tabIndex == 0) {
      add(MixFilterEditEvent());
    } else if (event.tabIndex == 1) {
      add(MasterFilterEditEvent());
    }
    if (_wasPlayingBeforeSeek && !_isUserPaused) {
      juceMixPlayerBloc.add(JuceMixAudioPlayEvent());
    }
  }

  void _calculateDelayOffset(int delay) {
    userGivenDelay = delay;
    delayFinal = delay - timeDiff;
    if (delayFinal > 0) {
      vocalOffset = (vocalOffset + delayFinal.toDouble()).abs() / 1000;
      vocalFromTime = 0;
    } else if (delayFinal < 0) {
      vocalFromTime = (vocalFromTime + delayFinal.toDouble()).abs() / 1000;
      vocalOffset = 0;
    } else {
      vocalOffset = 0;
      vocalFromTime = 0;
    }
  }

  FutureOr<void> _onDelaySeekStart(DelaySeekStartEvent event, _) async {
    _wasPlayingBeforeSeek = juceMixPlayerBloc.isAudioPlaying;
    _isUserPaused = false;
    juceMixPlayerBloc.add(JuceMixAudioPauseEvent());
  }

  FutureOr<void> _onDelaySeekEnd(DelaySeekEndEvent event, _) async {
    _calculateDelayOffset(event.delay);
    add(MixFilterEditEvent());
    unawaited(CommonUtils.saveUserDelay(userGivenDelay));
    if (_wasPlayingBeforeSeek && !_isUserPaused) {
      juceMixPlayerBloc.add(JuceMixAudioPlayEvent());
    }
  }

  FutureOr<void> _selectMixFilter(SelectMixFilterEvent event, _) {
    selectedMixFilterIndex = event.selectedFilterIndex;
    juceMixPlayerBloc.onSetMixFilter(_mixFilters[selectedMixFilterIndex].id);
    emit(VocalFilterItemUpdated());
    add(MixFilterEditEvent());
    DI().resolve<AppToast>().showToast("${_mixFilters[selectedMixFilterIndex].title} applied");
    vocalFilterName = _mixFilters[selectedMixFilterIndex].title;
  }

  FutureOr<void> _selectMasterFilter(SelectMasterFilterEvent event, _) {
    selectedMasterFilterIndex = event.selectedFilterIndex;
    juceMixPlayerBloc.onSetMasterFilter(_masterFilters[selectedMasterFilterIndex].id);
    emit(VocalFilterItemUpdated());
    add(MasterFilterEditEvent());
    DI().resolve<AppToast>().showToast("${_masterFilters[selectedMasterFilterIndex].title} applied");
    masterFilterName = _masterFilters[selectedMasterFilterIndex].title;
  }

  Future<List<VocalFilterItem>> getMixFilters() async {
    const mixFilters = [
      ("NONE", "Normal", "normal"),
      ("AC_1", "Acoustic 1", "acoustic"),
      ("AC_2", "Acoustic 2", "acoustic"),
      ("Lofi_1", "LoFi 1", "lofi"),
      ("Lofi_2", "LoFi 2", "lofi"),
      ("Pop_1", "Pop 1", "pop"),
      ("Pop_2", "Pop 2", "pop"),
      ("Rock_1", "Rock 1", "rock"),
      ("Rock_2", "Rock 2", "rock"),
      ("Elec_1", "Electronic 1", "elec"),
    ];

    final filterItems = await _createFilterItemsWithGenreSequence(mixFilters);
    return _mixFilters = filterItems;
  }

  // Synchronous getter for UI - returns already loaded filters
  List<VocalFilterItem> get mixFilters => _mixFilters;

  Future<List<VocalFilterItem>> getMasterFilters() async {
    const List<(String, String, String)> masterFilters = [
      ("NONE", "Normal", "normal"),
      ("AC_MST", "Acoustic", "acoustic"),
      ("Pop_MST", "Pop", "pop"),
      ("Rock_MST", "Rock", "rock"),
      ("Lofi_MST", "LoFi", "lofi"),
      ("Elec_MST", "Electronic", "elec"),
    ];

    final filterItems = await _createFilterItemsWithGenreSequence(masterFilters);
    return _masterFilters = filterItems;
  }

  // Synchronous getter for UI - returns already loaded master filters
  List<VocalFilterItem> get masterFilters => _masterFilters;

  /// Common method to create filter items with genre sequence sorting
  Future<List<VocalFilterItem>> _createFilterItemsWithGenreSequence(
    List<(String, String, String)> filters,
  ) async {
    final genreMap = Map<String, String>.from(json.decode(await rootBundle.loadString(AssetPaths.genreMap)));
    final genreSequence = Map<String, List<dynamic>>.from(json.decode(await rootBundle.loadString(AssetPaths.genreSequence)));

    final songGenre = shareDataBloc.annotatedData?.genre ?? "";
    final genreKey = genreMap[songGenre] ?? "Elec";

    final sequenceOrder = genreSequence[genreKey]?.cast<String>();
    if (sequenceOrder == null || sequenceOrder.isEmpty) {
      throw Exception("Missing genre sequence for $genreKey");
    }

    final filterItems = filters
        .map((filter) => VocalFilterItem(
              id: filter.$1,
              title: filter.$2,
              thumbnailPath: "${AssetPaths.filterIcons}/${filter.$3}.png",
            ))
        .toList();

    String extractGenre(String id) {
      return id.split("_").firstWhere((_) => true, orElse: () => "Elec");
    }

    filterItems.sort((a, b) {
      if (a.id == "NONE") return -1;
      if (b.id == "NONE") return 1;

      final indexA = sequenceOrder.indexOf(extractGenre(a.id));
      final indexB = sequenceOrder.indexOf(extractGenre(b.id));

      return indexA.compareTo(indexB);
    });

    return filterItems;
  }

  FutureOr<void> _onMixFilterEdit(MixFilterEditEvent event, _) async {
    logger.d("onMixFilterEdit:");
    await _updateJuceMixComposeModel();
  }

  FutureOr<void> _onMasterFilterEdit(MasterFilterEditEvent event, _) async {
    logger.d("onMasterFilterEdit:");
    await _updateJuceMixComposeModel();
  }

  Future<void> _updateJuceMixComposeModel() async {
    try {
      final updatedTracks = juceMixPlayerBloc.mixComposeModel.tracks?.map((track) {
        if (track.id == "vocal") {
          return track.copyWith(
            volume: vocalVolume,
            offset: vocalOffset,
            fromTime: vocalFromTime,
          );
        } else if (track.id == "bgm") {
          return track.copyWith(path: bgmWavPath, volume: musicVolume, enabled: true);
        } else if (track.id == "guide") {
          return track.copyWith(enabled: juceMixPlayerBloc.isGuideEnabled, volume: juceMixPlayerBloc.guideVol);
        } else if (track.id.startsWith("metronome_track")) {
          return track.copyWith(enabled: juceMixPlayerBloc.isMetronomeEnabled, volume: juceMixPlayerBloc.metronomeVol);
        }
        return track;
      }).toList();

      juceMixPlayerBloc.mixComposeModel = juceMixPlayerBloc.mixComposeModel.copyWith(tracks: updatedTracks);
      juceMixPlayerBloc.mixComposeModel.outputDuration = recDuration;
      juceMixPlayerBloc.setMixData();

      logger.d("Updated juceMixComposeModel with ${juceMixPlayerBloc.mixComposeModel.tracks?.length ?? 0} tracks");
    } catch (e) {
      logger.e("Error updating juceMixComposeModel: $e");
    }
  }
}
