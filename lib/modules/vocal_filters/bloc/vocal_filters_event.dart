// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'vocal_filters_bloc.dart';

abstract class VocalFiltersEvent extends BaseEvent {
  const VocalFiltersEvent();
}

class VocalFiltersLoadEvent extends VocalFiltersEvent {
  @override
  List<Object?> get props => [];
}

class PlayEvent extends VocalFiltersEvent {
  @override
  List<Object?> get props => [];
}

class PauseEvent extends VocalFiltersEvent {
  @override
  List<Object?> get props => [];
}

class StopEvent extends VocalFiltersEvent {
  @override
  List<Object?> get props => [];
}

class SeekStartEvent extends VocalFiltersEvent {
  @override
  List<Object?> get props => [];
}

class SeekEndEvent extends VocalFiltersEvent {
  final double milliseconds;
  const SeekEndEvent({required this.milliseconds});

  @override
  List<Object> get props => [milliseconds];
}

class VolumeSeekStartEvent extends VocalFiltersEvent {
  final bool isVocal;
  final int tabIndex;
  const VolumeSeekStartEvent({required this.isVocal, required this.tabIndex});

  @override
  List<Object?> get props => [isVocal, tabIndex];
}

class VolumeSeekEndEvent extends VocalFiltersEvent {
  final double value;
  final int tabIndex;
  final bool isVocal;

  const VolumeSeekEndEvent({
    required this.value,
    required this.tabIndex,
    required this.isVocal,
  });

  @override
  List<Object?> get props => [value, tabIndex, isVocal];
}

class DelaySeekStartEvent extends VocalFiltersEvent {
  @override
  List<Object?> get props => [];
}

class DelaySeekEndEvent extends VocalFiltersEvent {
  final int delay;
  const DelaySeekEndEvent({required this.delay});

  @override
  List<Object?> get props => [delay];
}

class SelectMixFilterEvent extends VocalFiltersEvent {
  final int selectedFilterIndex;
  const SelectMixFilterEvent({required this.selectedFilterIndex});
  @override
  List<Object?> get props => [selectedFilterIndex];
}

class SelectMasterFilterEvent extends VocalFiltersEvent {
  final int selectedFilterIndex;
  const SelectMasterFilterEvent({required this.selectedFilterIndex});
  @override
  List<Object?> get props => [selectedFilterIndex];
}

class CheckWavFileDownloadStatus extends VocalFiltersEvent {
  @override
  List<Object?> get props => [];
}

class MixFilterEditEvent extends VocalFiltersEvent {
  const MixFilterEditEvent();

  @override
  List<Object?> get props => [];
}

class MasterFilterEditEvent extends VocalFiltersEvent {
  const MasterFilterEditEvent();

  @override
  List<Object?> get props => [];
}




// class DenoiseToggle extends VocalFiltersEvent {
//   const DenoiseToggle();
//   @override
//   List<Object?> get props => [];
// }


// class ShowDelayAdjustBottomSheet extends VocalFiltersEvent {
//   const ShowDelayAdjustBottomSheet();

//   @override
//   List<Object?> get props => [DateTime.now().millisecondsSinceEpoch];
// }

// class HideControls extends VocalFiltersEvent {
//   @override
//   List<Object?> get props => [];
// }


// class ShowControls extends VocalFiltersEvent {
//   @override
//   List<Object?> get props => [];
// }