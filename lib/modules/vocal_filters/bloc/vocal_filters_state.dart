part of 'vocal_filters_bloc.dart';

abstract class VocalfiltersState extends BlocState {
  const VocalfiltersState();
}

class VocalFilterInitialState extends VocalfiltersState {
  @override
  List<Object?> get props => [];
}

class VocalFilterLoadingState extends VocalfiltersState {
  @override
  List<Object?> get props => [];
}

class VocalFilterLoadedState extends VocalfiltersState {
  final bool isLoading;

  const VocalFilterLoadedState({this.isLoading = false});

  @override
  List<Object?> get props => [isLoading];
}

class VocalFilterErrorState extends VocalfiltersState {
  final String error;
  const VocalFilterErrorState(this.error);

  @override
  List<Object?> get props => [];
}

class VocalFilterPlayerErrorState extends VocalfiltersState {
  final String error;
  const VocalFilterPlayerErrorState(this.error);

  @override
  List<Object?> get props => [];
}

class VocalFilterPlayingState extends VocalfiltersState {
  @override
  List<Object?> get props => [];
}

class VocalFilterPausedState extends VocalfiltersState {
  @override
  List<Object?> get props => [];
}

class VocalFilterCurrentPositionState extends VocalfiltersState {
  final Duration position;
  const VocalFilterCurrentPositionState(this.position);
  @override
  List<Object?> get props => [position];
}

class VocalFilterItemUpdated extends VocalfiltersState {
  final int value;

  VocalFilterItemUpdated() : value = DateTime.now().millisecondsSinceEpoch;

  @override
  List<Object?> get props => [value];
}

class VocalFilterAdjustDelayState extends VocalfiltersState {
  final String songPath;
  final String vocalPath;
  final int uniqueKey;

  VocalFilterAdjustDelayState({
    required this.songPath,
    required this.vocalPath,
  }) : uniqueKey = DateTime.now().millisecondsSinceEpoch;

  @override
  List<Object?> get props => [songPath, vocalPath, uniqueKey];
}
