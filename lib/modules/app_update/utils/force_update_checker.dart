import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/modules/app_update/models/app_version_config.dart';

class ForceUpdateChecker {
  const ForceUpdateChecker._();

  static bool requiresForceUpdate(String currentVersion) {
    try {
      final config = AppVersionConfig.getConfig();
      if (config == null || !config.isForceUpdateEnabled) return false;

      final List<int> currentParts = currentVersion.split('.').map(int.parse).toList();
      final List<int> minParts = config.minimumSupportedVersion.split('.').map(int.parse).toList();

      for (int i = 0; i < 3; i++) {
        if (currentParts[i] < minParts[i]) {
          return true;
        } else if (currentParts[i] > minParts[i]) {
          break;
        }
      }

      return false;
    } catch (e, stackTrace) {
      logger.e('Error checking force update', error: e, stackTrace: stackTrace);
      return false;
    }
  }
}
