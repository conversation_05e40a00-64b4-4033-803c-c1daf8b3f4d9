import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/modules/app_update/models/app_version_config.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class ForceUpdateScreen extends StatelessWidget {
  final AppVersionConfig config;

  const ForceUpdateScreen({super.key, required this.config});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.dark,
        child: Scaffold(
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.system_update,
                    size: 100,
                    color: AppColors.primary50,
                  ),
                  const SizedBox(height: 32),
                  Text(
                    'Update Required',
                    style: AppTextStyles.headline5.copyWith(color: Colors.black),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    config.updateMessage,
                    style: AppTextStyles.text16medium.copyWith(color: Colors.black),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _openUpdateLink,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary50,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Update Now',
                        style: AppTextStyles.text14semiBold.copyWith(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _openUpdateLink() async {
    final url = Uri.parse(Platform.isAndroid ? config.androidAppUpdateUrl : config.iosAppUpdateUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }
}
