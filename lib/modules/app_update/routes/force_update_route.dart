import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class ForceUpdateRoutePaths {
  static const String forceUpdate = '/force-update';
}

class ForceUpdateRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: ForceUpdateRoutePaths.forceUpdate,
          page: ForceUpdateRoute.page,
        ),
      ];
}
