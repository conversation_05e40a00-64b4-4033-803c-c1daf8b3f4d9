# App Update Feature

An implementation for managing app updates through Firebase Remote Config, supporting both mandatory and version-based update requirements.

## Overview

The force update feature ensures users are running the latest version of the app by showing a non-dismissible update screen when necessary. It supports both Android and iOS platforms and can be configured remotely through Firebase Remote Config.

## Configuration

### Firebase Remote Config Structure
```json
{
  "app_version_config": {
    "minimum_supported_version": "1.0.0",
    "update_message": "A new version of Melodyze is available. Please update to continue using the app.",
    "android_app_update_url": "https://play.google.com/store/apps/details?id=ai.melodyze.music",
    "ios_app_update_url": "https://apps.apple.com/app/id959993156379",
    "is_force_update_enabled": true
  }
}
```

### Parameters
- `minimum_supported_version`: Minimum version required to run the app
- `update_message`: Custom message shown to users
- `android_app_update_url`: Android app URL
- `ios_app_update_url`: iOS app URL
- `is_force_update_enabled`: Toggle to enable/disable force updates


