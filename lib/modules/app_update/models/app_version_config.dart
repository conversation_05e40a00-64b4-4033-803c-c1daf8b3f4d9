import 'dart:convert';

import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/remote_config_helper.dart';

class AppVersionConfig {
  final String minimumSupportedVersion;
  final String updateMessage;
  final String androidAppUpdateUrl;
  final String iosAppUpdateUrl;
  final bool isForceUpdateEnabled;

  const AppVersionConfig({
    required this.minimumSupportedVersion,
    required this.updateMessage,
    required this.androidAppUpdateUrl,
    required this.iosAppUpdateUrl,
    required this.isForceUpdateEnabled,
  });

  factory AppVersionConfig.fromJson(Map<String, dynamic> json) {
    return AppVersionConfig(
      minimumSupportedVersion: json['minimum_supported_version'] as String,
      updateMessage: json['update_message'] as String,
      androidAppUpdateUrl: json['android_app_update_url'] as String,
      iosAppUpdateUrl: json['ios_app_update_url'] as String,
      isForceUpdateEnabled: json['is_force_update_enabled'] as bool,
    );
  }

  static AppVersionConfig? getConfig() {
    try {
      final configJson = RemoteConfigHelper.getString(RemoteConfigKeys.appVersionConfig);
      final json = jsonDecode(configJson) as Map<String, dynamic>;
      return AppVersionConfig.fromJson(json);
    } catch (error, stackTrace) {
      logger.e('Error parsing app version config', error: error, stackTrace: stackTrace);
    }
    return null;
  }
}
