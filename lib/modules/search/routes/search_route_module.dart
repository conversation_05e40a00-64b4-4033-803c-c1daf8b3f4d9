import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class SearchRoutesPath {
  static const String search = 'search';
}

class SearchRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: SearchRoutesPath.search,
          page: SearchRoute.page,
        ),
      ];
}
