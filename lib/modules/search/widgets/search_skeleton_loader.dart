// search_list_skeleton.dart
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class SearchListSkeleton extends StatefulWidget {
  final int itemCount;
  const SearchListSkeleton({super.key, this.itemCount = 10});

  @override
  State<SearchListSkeleton> createState() => _SearchListSkeletonState();
}

class _SearchListSkeletonState extends State<SearchListSkeleton>
    with TickerProviderStateMixin {
  late final AnimationController _pulseController;
  late final Animation<double> _pulse;

  // appear animation for the whole list
  late final AnimationController _appearController;
  late final Animation<double> _appearOpacity;
  late final Animation<double> _appearScale;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);
    _pulse = CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut);

    _appearController = AnimationController(
      duration: const Duration(milliseconds: 420),
      vsync: this,
    );
    _appearOpacity =
        CurvedAnimation(parent: _appearController, curve: Curves.easeInOut);
    _appearScale = Tween<double>(begin: 0.98, end: 1.0).animate(
      CurvedAnimation(parent: _appearController, curve: Curves.easeInOut),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) _appearController.forward();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _appearController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _appearOpacity,
      child: ScaleTransition(
        scale: _appearScale,
        child: AnimatedBuilder(
          animation: _pulse,
          builder: (context, _) {
            final base = Color.lerp(const Color(0xFF2A2A2A), const Color(0xFF404040), _pulse.value)!;
            final highlight = Color.lerp(const Color(0xFF505050), const Color(0xFF606060), _pulse.value)!;

            return ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.only(bottom: 8.0),
              itemCount: widget.itemCount,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: _SkeletonRow(base: base, highlight: highlight, index: index),
                );
              },
              separatorBuilder: (_, __) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 14.0),
                child: Container(
                  height: 1,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        Color(0x002A2A2A),
                        Color(0xFF2A2A2A),
                        Color(0xFF404040),
                        Color(0xFF2A2A2A),
                        Color(0x002A2A2A),
                      ],
                      stops: [0.0, 0.2, 0.5, 0.8, 1.0],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class _SkeletonRow extends StatelessWidget {
  final Color base;
  final Color highlight;
  final int index;
  const _SkeletonRow({
    required this.base,
    required this.highlight,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 400 + (index * 60)),
      tween: Tween(begin: 0, end: 1),
      curve: Curves.easeOutCubic,
      builder: (context, value, _) {
        return Opacity(
          opacity: value,
          child: Transform.scale(
            scale: 0.98 + (0.02 * value),
            child: Row(
              children: [
                // thumbnail 44x44 with 8 radius (matches SongListItem)
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Shimmer.fromColors(
                    baseColor: base,
                    highlightColor: highlight,
                    period: const Duration(milliseconds: 1200),
                    child: Container(
                      height: 46,
                      width: 46,
                      decoration: BoxDecoration(
                        color: const Color(0xFF2A2A2A),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xFF333333), width: 1),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // title + subtitle bars
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Shimmer.fromColors(
                        baseColor: base,
                        highlightColor: highlight,
                        period: const Duration(milliseconds: 1200),
                        child: Container(
                          height: 16,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: const Color(0xFF2A2A2A),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: const Color(0xFF333333), width: 0.5),
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Shimmer.fromColors(
                        baseColor: base,
                        highlightColor: highlight,
                        period: const Duration(milliseconds: 1200),
                        child: FractionallySizedBox(
                          widthFactor: 0.6, // shorter subtitle
                          child: Container(
                            height: 12,
                            decoration: BoxDecoration(
                              color: const Color(0xFF2A2A2A),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: const Color(0xFF333333), width: 0.5),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
