import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/marquee.dart';
import 'package:melodyze/core/ui/molecules/network_image.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';

class SongListItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final String s3ImagePath;
  final VoidCallback? onPressed;
  final VoidCallback? onIconPressed;
  const SongListItem({
    super.key,
    required this.title,
    required this.subtitle,
    required this.s3ImagePath,
    this.onPressed,
    this.onIconPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: onIconPressed,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: SizedBox(
              height: 44,
              width: 44,
              child: NetworkImageWidget(
                imageUrl: s3ImagePath,
              ),
            ),
          ),
        ),
        const SizedBox(
          width: 16,
        ),
        Expanded(
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: onPressed,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LayoutBuilder(
                  builder: (context, constraints) {
                    return Marqueee(
                      text: title,
                      style: AppTextStyles.text16semiBold,
                      width: constraints.maxWidth - 40,
                    );
                  },
                ),
                const SizedBox(
                  height: 8,
                ),
                LayoutBuilder(
                  builder: (context, constraints) {
                    return Marqueee(
                      text: subtitle,
                      style: AppTextStyles.text12regular.copyWith(
                        color: AppColors.greySlider,
                        fontFamily: AppFonts.iceland,
                      ),
                      width: constraints.maxWidth - 40,
                    );
                  },
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
