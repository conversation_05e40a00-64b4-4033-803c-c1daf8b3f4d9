import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/modules/search/model/search_result.dart';
import 'package:melodyze/modules/search/repo/search_repo.dart';

class SearchCubit extends Cubit<BlocState> {
  final SearchRepository _searchRepository;
  final String title;

  SearchCubit(this._searchRepository, {String? title})
      : title = title ?? 'Search',
        super(InitialState());

  Future<void> searchV2(String query) async {
    if (query.isEmpty) {
      emit(InitialState());
      return;
    }
    emit(LoadingState());
    final response = await _searchRepository.searchV2(query);
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }

    final searchResult = response.data as SearchResult;

    if (searchResult.matchedSongs.isEmpty && searchResult.youMayLikeSongs.isEmpty) {
      emit(EmptyDataState());
      return;
    }

    emit(BlocSuccessState<SearchResult>(searchResult));
  }

  Future<void> loadGridSongs(String gridId) async {
    emit(LoadingState());
    final response = (await _searchRepository.loadFromGridSongs(gridId));
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }
    final gridResult = response.data as SearchResult;

    if (gridResult.matchedSongs.isEmpty && gridResult.youMayLikeSongs.isEmpty) {
      emit(EmptyDataState());
      return;
    }
    emit(BlocSuccessState<SearchResult>(gridResult));
  }

  Future<void> loadFromTileSongs(String tileId) async {
    emit(LoadingState());
    final response = await _searchRepository.loadFromTileSongs(tileId);
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }
    final tileResult = response.data as SearchResult;

    if (tileResult.matchedSongs.isEmpty && tileResult.youMayLikeSongs.isEmpty) {
      emit(EmptyDataState());
      return;
    }
    emit(BlocSuccessState<SearchResult>(tileResult));
  }
}
