import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_arch/base_service.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class SearchService extends BaseService {
  Future<Map<String, dynamic>?> searchV2(String query) async {
    return await DI().resolve<ApiClient>().post(Endpoints.searchV2, body: {"search_text": query});
  }

  Future<Map<String, dynamic>?> loadFromGridSongs(String gridId) async {
    return await DI().resolve<ApiClient>().post(Endpoints.gridOrderSongs, body: {"grid_id": gridId});
  }

  Future<Map<String, dynamic>?> loadFromTileSongs(String tileId) async {
    return await DI().resolve<ApiClient>().post(Endpoints.tileOrderSongs, body: {"tile_id": tileId});
  }
}
