import 'package:freezed_annotation/freezed_annotation.dart';

part 'pagination_model.freezed.dart';
part 'pagination_model.g.dart';

@freezed
class PaginationModel with _$PaginationModel {
  factory PaginationModel({
    @Json<PERSON>ey(name: 'next') @Default(false) bool next,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'previous') @Default(false) bool previous,
    @<PERSON>son<PERSON>ey(name: 'content_length') @Default(0) int contentLength,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'skip') @Default(0) int skip,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'limit') @Default(300) int limit,
  }) = _PaginationModel;

  factory PaginationModel.fromJson(Map<String, dynamic> json) => _$PaginationModelFromJson(json);
}
