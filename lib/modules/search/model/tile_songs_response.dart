import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/search/model/pagination_model.dart';

part 'tile_songs_response.freezed.dart';
part 'tile_songs_response.g.dart';


@freezed
class TileSongsData with _$TileSongsData {
  factory TileSongsData({
    @JsonKey(name: 'tile_songs') required List<SongModel> matchedSongs,
    @JsonKey(name: 'you_may_like_songs') required List<SongModel> youMayLikeSongs,
  }) = _TileSongsData;

  factory TileSongsData.fromJson(Map<String, dynamic> json) => _$TileSongsDataFromJson(json);
}

@freezed
class TileSongsResponse with _$TileSongsResponse {
  factory TileSongsResponse({
    @JsonKey(name: 'success') required bool success,
    @JsonKey(name: 'message') required String message,
    @<PERSON>sonKey(name: 'data') required TileSongsData data,
    @Json<PERSON>ey(name: 'pagination') required PaginationModel pagination,
  }) = _TileSongsResponse;

  factory TileSongsResponse.fromJson(Map<String, dynamic> json) => _$TileSongsResponseFromJson(json);
}

