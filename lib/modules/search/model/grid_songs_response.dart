import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/search/model/pagination_model.dart';

part 'grid_songs_response.freezed.dart';
part 'grid_songs_response.g.dart';

@freezed
class GridSongsData with _$GridSongsData {
  factory GridSongsData({
    @JsonKey(name: 'grid_songs') required List<SongModel> matchedSongs,
    @JsonKey(name: 'you_may_like_songs') required List<SongModel> youMayLikeSongs,
  }) = _GridSongsData;

  factory GridSongsData.fromJson(Map<String, dynamic> json) => _$GridSongsDataFromJson(json);
}

@freezed
class GridSongsResponse with _$GridSongsResponse {
  factory GridSongsResponse({
    @JsonKey(name: 'success') required bool success,
    @JsonKey(name: 'message') required String message,
    @<PERSON>sonKey(name: 'data') required GridSongsData data,
    @Json<PERSON>ey(name: 'pagination') required PaginationModel pagination,
  }) = _GridSongsResponse;

  factory GridSongsResponse.fromJson(Map<String, dynamic> json) => _$GridSongsResponseFromJson(json);
}