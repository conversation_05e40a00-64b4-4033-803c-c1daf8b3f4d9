import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/search/model/pagination_model.dart';

part 'search_result.freezed.dart';
part 'search_result.g.dart';

@freezed
class SearchResult with _$SearchResult {
  factory SearchResult({
    @JsonKey(name: 'matched_songs') required List<SongModel> matchedSongs,
    @JsonKey(name: 'you_may_like_songs') required List<SongModel> youMayLikeSongs,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'pagination') required PaginationModel pagination,
  }) = _SearchResult;

  factory SearchResult.fromJson(Map<String, dynamic> json) => _$SearchResultFromJson(json);
}
