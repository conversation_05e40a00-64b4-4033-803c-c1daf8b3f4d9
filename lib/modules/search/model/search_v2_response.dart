import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/search/model/pagination_model.dart';

part 'search_v2_response.freezed.dart';
part 'search_v2_response.g.dart';

@freezed
class SearchV2Response with _$SearchV2Response {
  factory SearchV2Response({
    @Json<PERSON>ey(name: 'success') required bool success,
    @<PERSON>son<PERSON><PERSON>(name: 'message') required String message,
    @JsonKey(name: 'data') required SearchV2Data data,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'pagination') required PaginationModel pagination,
  }) = _SearchV2Response;

  factory SearchV2Response.fromJson(Map<String, dynamic> json) => _$SearchV2ResponseFromJson(json);
}

@freezed
class SearchV2Data with _$SearchV2Data {
  factory SearchV2Data({
    @JsonKey(name: 'matched_songs') required List<SongModel> matchedSongs,
    @Json<PERSON><PERSON>(name: 'you_may_like_songs') required List<SongModel> youMayLikeSongs,
  }) = _SearchV2Data;

  factory SearchV2Data.fromJson(Map<String, dynamic> json) => _$SearchV2DataFromJson(json);
}
