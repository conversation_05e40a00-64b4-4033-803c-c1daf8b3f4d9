import 'package:melodyze/core/generic_arch/base_repository.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/modules/search/model/search_result.dart';
import 'package:melodyze/modules/search/model/search_v2_response.dart';
import 'package:melodyze/modules/search/model/grid_songs_response.dart';
import 'package:melodyze/modules/search/model/tile_songs_response.dart';
import 'package:melodyze/modules/search/service/search_service.dart';

class SearchRepository extends BaseRepo {
  final SearchService _searchService;
  SearchRepository(SearchService searchService) : _searchService = searchService;

  Future<RepoResult> searchV2(String query) async {
    return await executeAndReturnResult(
      () => _searchService.searchV2(query),
      (json) async {
        try {
          final response = SearchV2Response.fromJson(json);
          final searchResult = SearchResult(
            matchedSongs: response.data.matchedSongs,
            youMayLikeSongs: response.data.youMayLikeSongs,
            pagination: response.pagination,
          );
          return RepoResult.success(searchResult);
        } catch (e) {
          return RepoResult.failure(RepoError('Failed to parse search response: $e'));
        }
      },
    );
  }

  Future<RepoResult> loadFromGridSongs(String gridId) async {
    return await executeAndReturnResult(
      () => _searchService.loadFromGridSongs(gridId),
      (json) async {
        try {
          final response = GridSongsResponse.fromJson(json);
          final result = SearchResult(
            matchedSongs: response.data.matchedSongs,
            youMayLikeSongs: response.data.youMayLikeSongs,
            pagination: response.pagination,
          );
          return RepoResult.success(result);
        } catch (e) {
          return RepoResult.failure(RepoError('Failed to parse grid songs response: $e'));
        }
      },
    );
  }

  Future<RepoResult> loadFromTileSongs(String tileId) async {
    return await executeAndReturnResult(
      () => _searchService.loadFromTileSongs(tileId),
      (json) async {
        try {
          final response = TileSongsResponse.fromJson(json);
          final result = SearchResult(
            matchedSongs: response.data.matchedSongs,
            youMayLikeSongs: response.data.youMayLikeSongs,
            pagination: response.pagination,
          );
          return RepoResult.success(result);
        } catch (e) {
          return RepoResult.failure(RepoError('Failed to parse tile songs response: $e'));
        }
      },
    );
  }

  // List<SongModel> _parseList(Map<String, dynamic> json) {
  //   return (json['data'] as List<dynamic>?)?.map((e) => SongModel.fromJson(e as Map<String, dynamic>)).toList() ?? const [];
  // }
}
