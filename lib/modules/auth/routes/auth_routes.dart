import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class AuthRoutesPath {
  static const String login = '/login';
}

class AuthRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: AuthRoutesPath.login,
          page: LoginRoute.page,
        ),
      ];
}
