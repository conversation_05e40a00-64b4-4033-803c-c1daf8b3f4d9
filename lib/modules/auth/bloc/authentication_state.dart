import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/modules/app_update/models/app_version_config.dart';

class WelcomeScreenState extends BlocState {
  @override
  List<Object?> get props => [];
}

class LoginScreenState extends BlocState {
  @override
  List<Object?> get props => [];
}

class PreferenceScreenState extends BlocState {
  @override
  List<Object?> get props => [];
}

class DashboardScreenState extends BlocState {
  const DashboardScreenState();
  @override
  List<Object?> get props => [];
}

class SignedOutState extends BlocState {
  const SignedOutState();
  @override
  List<Object?> get props => [];
}

class ForceUpdateScreenState extends BlocState {
  final AppVersionConfig config;
  const ForceUpdateScreenState({required this.config});
  @override
  List<Object?> get props => [config];
}
