import 'package:freezed_annotation/freezed_annotation.dart';

part 'melodyze_user.freezed.dart';
part 'melodyze_user.g.dart';

@freezed
class MelodyzeUser with _$MelodyzeUser {
  const factory MelodyzeUser({
    @J<PERSON><PERSON>ey(name: '_id') required String id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'email_id') @Default('') String email,
    @Json<PERSON><PERSON>(name: 'user_name') @Default('Anonymous') String username,
    @Json<PERSON><PERSON>(name: 'profile_picture_path') String? profilePicUrl,
    @JsonKey(name: 'preferences') @Default(Preferences()) Preferences preferences,
    @JsonKey(name: 'device_info') @Default(DeviceInfo()) DeviceInfo deviceInfo,
  }) = _MelodyzeUser;

  factory MelodyzeUser.fromJson(Map<String, dynamic> json) => _$MelodyzeUser<PERSON>romJson(json);
}

@freezed
class Preferences with _$Preferences {
  const factory Preferences({
    @<PERSON><PERSON><PERSON><PERSON>(name: 'genres') @Default([]) List<String> genres,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'artists') @Default([]) List<String> artists,
  }) = _Preferences;

  factory Preferences.fromJson(Map<String, dynamic> json) => _$PreferencesFromJson(json);
}

@freezed
class DeviceInfo with _$DeviceInfo {
  const factory DeviceInfo({
    @JsonKey(name: 'platform') @Default('') String platform,
    @JsonKey(name: 'device') @Default('') String device,
    @JsonKey(name: 'os') @Default('') String os,
    @JsonKey(name: 'uid') @Default('') String uid,
    @JsonKey(name: 'os_version') @Default('') String osVersion,
    @JsonKey(name: 'brand') @Default('') String brand,
  }) = _DeviceInfo;

  factory DeviceInfo.fromJson(Map<String, dynamic> json) => _$DeviceInfoFromJson(json);
}
