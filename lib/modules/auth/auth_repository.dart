import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_arch/base_repository.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/core/wrappers/analytics/crash_logger.dart';
import 'package:melodyze/core/services/user_data_manager.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/device_info_wrapper.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';
import 'package:melodyze/modules/auth/model/melodyze_user.dart';

class AuthRepository extends BaseRepo {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
  );

  // Hacky way to return the auth token and photourl both, that's why using a List
  Future<RepoResult<List<String?>>> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      logger.d('Google user sign-in successful: $googleUser');

      if (googleUser != null) {
        final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
        logger.d('Google authentication successful: $googleAuth');

        final AuthCredential credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );
        logger.d('Firebase auth credential created: $credential');

        final UserCredential authResult = await _auth.signInWithCredential(credential);
        logger.d('Firebase user sign-in successful: ${authResult.user}');

        final googleAuthToken = googleAuth.idToken;
        if (googleAuthToken == null) {
          const errorMessage = 'Google id token is null after sign-in attempt.';
          logger.e(errorMessage);
          return RepoResult.failure(RepoError(errorMessage));
        }

        return RepoResult.success([
          googleAuthToken,
          authResult.user?.photoURL,
        ]);
      } else {
        const errorMessage = 'Google user is null after sign-in attempt.';
        logger.e(errorMessage);
        return RepoResult.failure(RepoError(errorMessage));
      }
    } catch (e) {
      logger.e('Error in signInWithGoogle', error: e);
      return RepoResult.failure(RepoError('Error in signInWithGoogle', e));
    }
  }

  Future<RepoResult> melodyzeLogin({required String googleAuthToken}) async {
    final deviceInfo = await DeviceInfoWrapper.getSystemInfo();

    return await executeAndReturnResult(
      () => DI().resolve<ApiClient>().post(Endpoints.login, body: {
        "x-google-oauth-token": googleAuthToken,
        "device_info": deviceInfo.toJson(),
      }),
      (json) async {
        final Map<String, dynamic>? userData = json['data'];
        return RepoResult.success(MelodyzeUser.fromJson(userData?['user'] ?? {}));
      },
    );
  }

  Future<void> signOut() async {
    await _auth.signOut();
    await _googleSignIn.signOut();
    await DI().resolve<SecureStorageHelper>().deleteAll();
    await DI().resolve<AccessTokenHelper>().deleteAccessToken();
    await DI().resolve<CrashLogger>().clearUserIdentifier();
    await DI().resolve<UserDataManager>().clearUserData();
  }
}
