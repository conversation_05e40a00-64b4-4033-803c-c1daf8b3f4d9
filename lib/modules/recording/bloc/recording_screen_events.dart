import 'package:melodyze/core/generic_bloc/events.dart';
// import 'package:melodyze/core/recorder/recorder_channel.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';

class StartRecordingEvent extends BaseEvent {
  @override
  List<Object?> get props => [];
}

class StopRecordingEvent extends BaseEvent {
  final bool isDisposed;
  final bool isSongEnded;
  final bool discardRecording;

  const StopRecordingEvent({
    this.isDisposed = false,
    this.isSongEnded = false,
    this.discardRecording = false,
  });
  @override
  List<Object?> get props => [];
}

class InitRecordingEvent extends BaseEvent {
  final bool showTips;
  const InitRecordingEvent({this.showTips = true});
  @override
  List<Object?> get props => [];
}

// class ChangeAudioInputEvent extends BaseEvent {
//   final RecorderDevice mic;
//   const ChangeAudioInputEvent({required this.mic});
//   @override
//   List<Object?> get props => [mic];
// }

class InternalJuceStateUpdateEvent extends BaseEvent {
  final JuceMixState juceState;
  const InternalJuceStateUpdateEvent(this.juceState);

  @override
  List<Object?> get props => [juceState];
}