import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/ui/utilities/screen_utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';
import 'package:melodyze/core/wrappers/remote_config_helper.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_events.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_states.dart';
import 'package:melodyze/modules/recording/cubit/download_wav_file_cubit.dart';
import 'package:melodyze/modules/recording/repo/recording_repo.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';
import 'package:permission_handler/permission_handler.dart';

class RecordingScreenBloc extends SafeBloc {
  StreamSubscription<JuceMixState>? _juceMixBlocSubscription;
  late String recordingFilePath;

  AppLifecycleListener? _lifecycleListener;
  bool hasClipped = false;
  ({double min, double max}) audioLevelRange = (min: -160.0, max: 0.0);

  // BloC Data
  final RecordingRepo recordingRepo;
  final AnnotatedData annotatedData;
  final DownloadWavFileCubit downloadWavFileCubit;
  final JuceMixBloc juceMixBloc;

  RecordingScreenBloc({
    required this.recordingRepo,
    required this.annotatedData,
    required this.downloadWavFileCubit,
    required this.juceMixBloc,
  }) : super(InitialState()) {
    updateAudioLevelRange();
    ScreenUtils.keepScreenOn();

    on<InitRecordingEvent>(_initRecording);
    on<StartRecordingEvent>(_startRecording);
    on<StopRecordingEvent>(_stopRecording);
    on<InternalJuceStateUpdateEvent>(_handleJuceStateUpdate);
    _juceMixBlocSubscription = juceMixBloc.stream.listen((juceState) {
      add(InternalJuceStateUpdateEvent(juceState));
    });
    add(InitRecordingEvent());
  }

  @override
  Future<void> close() async {
    logger.d("RecordingScreenBloc closed");
    await ScreenUtils.keepScreenOff();
    await _juceMixBlocSubscription?.cancel();
    _lifecycleListener?.dispose();
    unawaited(super.close());
  }

  Future<void> _handleJuceStateUpdate(InternalJuceStateUpdateEvent event, _) async {
    final juceState = event.juceState;

    switch (juceState) {
      case JuceMixRecordPrepared():
        emit(AudioRecorderReadyState());
        break;
      case JuceMixRecordStarted():
        emit(RecordingInProgressState());
        break;
      case JuceMixRecordStopped():
        // Apply stereo normalization to the recorded vocal
        final normalizedRecordingPath = await PathWrapper.getNormalizedRecordingPath();
        await JuceKitWrapper().applyStereoAndNormalizeVocal(recordingFilePath, normalizedRecordingPath);
        
        emit(RecordingSuccessState(
          normalizedOutputFilePath: normalizedRecordingPath,
          outputFilePath: recordingFilePath,
          timeDiff: juceMixBloc.timeDiff,
          hasClipped: hasClipped,
          inputMic: juceMixBloc.inputChannelName,
        ));
        break;
      case JuceMixRecordLevelUpdateState():
        if (juceState.level >= -4.0) {
          hasClipped = true;
        }
        emit(RecordingUpdateAudioLevelState(audioLevel: juceState.level));
        break;
      case JuceMixRecordProgressUpdateState():
        emit(RecordingUpdatePlayerProgressState(progressInMilliseconds: juceState.progressInMillis));
        break;
      case JuceMixRecordError():
        emit(RecordingErrorState(errorMessage: 'Recorder error: ${juceState.error}'));
        break;
      case JuceMixError():
        emit(RecordingErrorState(errorMessage: 'JuceMixBloc error: ${juceState.error}'));
        break;
      default:
        break;
    }
  }

  Future<void> _initRecording(InitRecordingEvent event, _) async {
    _lifecycleListener ??= AppLifecycleListener(
      onHide: () {
        add(StopRecordingEvent(discardRecording: true));
      },
      onShow: () {
        add(InitRecordingEvent(showTips: false));
      },
    );
    try {
      if (!(await Permission.microphone.isGranted)) {
        logger.i('Microphone permission not granted, requesting permission');
        final status = await Permission.microphone.request();
        logger.i('Microphone permission request: ${status.toString()}');
        if (!status.isGranted) {
          logger.i('Microphone permission request: ${status.toString()}');
          emit(ShowMicPermissionDialogState());
          return;
        }
      }
      if (event.showTips) emit(ShowRecordingMessage());

      recordingFilePath = await PathWrapper.getRecordingPath();
      logger.i('recordring path: $recordingFilePath');
      if (File(recordingFilePath).existsSync()) {
        File(recordingFilePath).deleteSync();
      }

      hasClipped = false;

      juceMixBloc.add(JuceMixRecordInitEvent(
        recordingFilePath: recordingFilePath,
      ));
    } catch (e, stackTrace) {
      logger.e('Failed to initialize audio recorder', error: e, recordError: true, stackTrace: stackTrace);
      if (Platform.isIOS && e.toString().toLowerCase().contains('microphone permission denied')) {
        emit(ShowMicPermissionDialogState());
      } else {
        emit(RecordingErrorState(errorMessage: 'Failed to initialize audio recorder: ${e.toString()}'));
      }
    }
  }

  Future<void> _startRecording(StartRecordingEvent event, _) async {
    try {
      emit(RecordLoadingState());
      juceMixBloc.add(JuceMixRecordStartEvent());
      unawaited(downloadWavFileCubit.downloadWavFile(
        masterSongId: annotatedData.masterSongId,
        genre: annotatedData.genre,
        genreId: annotatedData.genreId,
        scale: annotatedData.scale,
        tempo: annotatedData.tempo,
      ));
    } catch (e) {
      emit(RecordingErrorState(errorMessage: 'Error starting recording: ${e.toString()}'));
    }
  }

  Future<void> _stopRecording(StopRecordingEvent event, _) async {
    try {
      emit(RecordLoadingState());
      juceMixBloc.add(JuceMixRecordStopEvent());
      if (event.isDisposed) {
        return;
      }
      if (event.discardRecording) {
        emit(AudioRecorderReadyState());
        return;
      }
    } catch (e) {
      emit(RecordingErrorState(errorMessage: 'Error stopping recording: $e'));
    }
  }

  void updateAudioLevelRange() {
    final configJson = RemoteConfigHelper.getString(RemoteConfigKeys.audioLevelRange);
    final rangeMap = jsonDecode(configJson) as Map<String, dynamic>;
    audioLevelRange = (
      min: double.parse(rangeMap['min'].toString()),
      max: double.parse(rangeMap['max'].toString()),
    );
  }

  //   void _setAudioCategory() {
  //   AudioSession.instance.then((session) {
  //     session.configure(const AudioSessionConfiguration.music());
  //   });
  // }
}
