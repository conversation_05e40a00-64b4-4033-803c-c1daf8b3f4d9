import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:juce_mix_player/juce_mix_player.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/butons/transparent_round_icon_button.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/ui/molecules/expandable_swipe_widget.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/lyrics_viewer.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_bloc.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_events.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_states.dart';
import 'package:melodyze/modules/recording/cubit/download_wav_file_cubit.dart';
import 'package:melodyze/modules/recording/repo/recording_repo.dart';
import 'package:melodyze/modules/recording/service/recording_service.dart';
import 'package:melodyze/modules/recording/widget/recording_button.dart';
import 'package:melodyze/modules/recording/widget/vu_meter.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/song_personalization_bloc.dart';

@RoutePage()
class RecordingScreen extends StatelessWidget {
  final LyricsViewerBloc lyricsViewerBloc;
  final MixerComposeModel mixComposeModel;

  const RecordingScreen({
    required this.lyricsViewerBloc,
    required this.mixComposeModel,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final JuceMixBloc juceMixBloc = JuceMixBloc(mixComposeModel: mixComposeModel, initFrom: "REC");
    final wavFileCubit = DownloadWavFileCubit(
      recordingRepo: RecordingRepo(
        recordingService: RecordingService(),
      ),
    );

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => RecordingScreenBloc(
            annotatedData: context.read<ShareDataBloc>().annotatedData!,
            recordingRepo: RecordingRepo(
              recordingService: RecordingService(),
            ),
            downloadWavFileCubit: wavFileCubit,
            juceMixBloc: juceMixBloc,
          ),
        ),
        BlocProvider(
          create: (context) => juceMixBloc,
        ),
        BlocProvider(
          create: (context) => wavFileCubit,
        ),
        BlocProvider.value(
          value: lyricsViewerBloc,
        ),
      ],
      child: _RecordingScreen(),
    );
  }
}

class _RecordingScreen extends StatelessWidget {
  static const double _bottomSectionHeight = 214;
  final GlobalKey<State<LyricsViewer>> _lyricsViewerKey = GlobalKey<State<LyricsViewer>>();

  @override
  Widget build(BuildContext context) {
    final recordingScreenBloc = context.read<RecordingScreenBloc>();
    final bool isGuideAvailable = !recordingScreenBloc.annotatedData.guideVocalPath.isNullOrEmpty;

    return Stack(
      children: [
        AppGradientContainer(
          borderGradient: AppGradients.gradientBlackPinkBlueBlackBorder,
          borderWidth: 8,
          borderRadius: BorderRadius.circular(36),
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(36),
            child: OverflowBox(
              maxWidth: MediaQuery.of(context).size.width,
              child: BlocBuilder<RecordingScreenBloc, BlocState>(
                builder: (context, state) {
                  return MeloScaffold(
                    popScopeDescription: 'Are you sure you want to discard the recording ?',
                    onPopScopeDiscard: () {
                      recordingScreenBloc.add(const StopRecordingEvent(isDisposed: true));
                    },
                    showBackButton: state is AudioRecorderReadyState,
                    overlayAction: Positioned(
                      top: MediaQuery.of(context).padding.top,
                      child: Center(
                        child: BlocBuilder<RecordingScreenBloc, BlocState>(
                          buildWhen: (previous, current) => current is! RecordLoadingState,
                          builder: (context, state) {
                            return BlocBuilder<RecordingScreenBloc, BlocState>(
                              buildWhen: (previous, current) => current is RecordLoadingState || current is AudioRecorderReadyState,
                              builder: (context, audioPlayerState) {
                                if (state is! AudioRecorderReadyState) {
                                  return SizedBox();
                                }
                                final juceMixBloc = context.read<JuceMixBloc>();
                                return ExpandableSwipeWidget(
                                  isLoading: false,
                                  items: [
                                    ExpandableSwipeWidgetModel(
                                      title: "Guide",
                                      label: "Guide",
                                      iconPath: AssetPaths.guide,
                                      isDisabled: !juceMixBloc.isGuideEnabled,
                                      initialVolume: juceMixBloc.guideVol,
                                      onVolumeChanged: isGuideAvailable
                                          ? (value) {
                                              juceMixBloc.add(JuceMixToggleGuideEvent(enable: true, volume: value));
                                            }
                                          : null,
                                      onPressed: isGuideAvailable
                                          ? (isDisabled) {
                                              juceMixBloc.add(JuceMixToggleGuideEvent(enable: !isDisabled, volume: 0));
                                            }
                                          : null,
                                    ),
                                    ExpandableSwipeWidgetModel(
                                      title: "Metronome",
                                      label: "Click",
                                      iconPath: AssetPaths.metronome,
                                      isDisabled: !juceMixBloc.isMetronomeEnabled,
                                      initialVolume: juceMixBloc.metronomeVol,
                                      onVolumeChanged: (value) {
                                        juceMixBloc.add(JuceMixToggleMetronomeEvent(enable: true, volume: value));
                                      },
                                      onPressed: (isDisabled) {
                                        if (isDisabled) {
                                          juceMixBloc.add(JuceMixToggleMetronomeEvent(enable: !isDisabled, volume: 0));
                                        }
                                      },
                                    )
                                  ],
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ),
                    // secondaryAction: (context) {
                    //   return BlocBuilder<RecordingScreenBloc, BlocState>(
                    //     buildWhen: (previous, current) => current is! RecordLoadingState,
                    //     builder: (context, state) {
                    //       if (state is AudioRecorderReadyState) {
                    //         final micList = recordingScreenBloc.micList;
                    //         return micList.isNotEmpty
                    //             ? MicSelectPopUp(
                    //                 micList: micList,
                    //                 selectedMic: recordingScreenBloc.selectedMic,
                    //                 onSelected: (mic) => recordingScreenBloc.add(ChangeAudioInputEvent(mic: mic)),
                    //               )
                    //             : const SizedBox.shrink();
                    //       }
                    //       return const SizedBox.shrink();
                    //     },
                    //   );
                    // },
                    showBackground: false,
                    body: SafeArea(
                      child: Stack(
                        children: [
                          BlocConsumer<RecordingScreenBloc, BlocState>(
                            listenWhen: (previous, current) => current is ShowMicPermissionDialogState,
                            listener: (context, state) async {
                              if (state is ShowMicPermissionDialogState) {
                                await showGivePermissionDialog(context: context, title: "Give Microphone Permission");
                              }
                            },
                            builder: (context, state) {
                              return Container();
                            },
                          ),
                          BlocConsumer<RecordingScreenBloc, BlocState>(
                            listenWhen: (previous, current) => current is RecordingSuccessState || current is ShowRecordingMessage,
                            listener: (context, state) async {
                              if (state is ShowRecordingMessage) {
                                await showOkDialog(
                                  context: context,
                                  title: 'Tips:',
                                  subTitle: 'To get best recording quality\n\n- Record in noiseless environment.\n- Use headphones.\n- Maintain fair distance from mic.',
                                );
                              }
                              // Reset lyrics viewer to initial position when recording stops successfully
                              if (state is RecordingSuccessState || state is RecordingErrorState) {
                                LyricsViewer.resetPosition(_lyricsViewerKey);
                              }

                              if (state is RecordingSuccessState && context.mounted) {
                                if (state.hasClipped) {
                                  final isConfirmed = await showYesNoDialog(
                                    context: context,
                                    title: 'Recording Alert',
                                    subTitle: 'Your recording has clipped. Do you still want to continue?',
                                  );
                                  if (isConfirmed == false) {
                                    recordingScreenBloc.add(const StopRecordingEvent(discardRecording: true));
                                    recordingScreenBloc.add(InitRecordingEvent(showTips: false));
                                    return;
                                  }
                                }
                              }
                            },
                            buildWhen: (previous, current) =>
                                current is! ShowRecordingMessage &&
                                (current is AudioRecorderReadyState || current is RecordingErrorState || current is AudioRecorderInitializingState),
                            builder: (context, state) {
                              if (state is RecordingErrorState) {
                                final isGuideMissing = state.errorMessage.contains('guide') || state.errorMessage.contains('octet-stream');
                                if (isGuideMissing) {
                                  return Center(
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          'Guide file missing or inaccessible. Please check your internet connection or permissions.',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(fontSize: 16, color: Colors.red),
                                        ),
                                        SizedBox(height: 16),
                                        ElevatedButton(
                                          onPressed: () {
                                            // Reload the screen by re-initializing recording
                                            recordingScreenBloc.add(InitRecordingEvent(showTips: false));
                                          },
                                          child: Text('Reload'),
                                        ),
                                      ],
                                    ),
                                  );
                                } else {
                                  final isGenericRecorderError =
                                      state.errorMessage.toLowerCase().contains('recorder error:') || state.errorMessage.toLowerCase().contains('something went wrong');
                                  if (isGenericRecorderError) {
                                    Future.microtask(() {
                                      if (context.mounted) {
                                        showDialog(
                                          context: context,
                                          builder: (ctx) => AlertDialog(
                                            title: Text('Recording Error'),
                                            content: Text('Something went wrong with the recorder. Please try again or reload.'),
                                            actions: [
                                              TextButton(
                                                onPressed: () {
                                                  Navigator.of(ctx).pop();
                                                  recordingScreenBloc.add(InitRecordingEvent(showTips: false));
                                                },
                                                child: Text('Reload'),
                                              ),
                                              TextButton(
                                                onPressed: () => Navigator.of(ctx).pop(),
                                                child: Text('Close'),
                                              ),
                                            ],
                                          ),
                                        );
                                      }
                                    });
                                    return SizedBox.shrink();
                                  }
                                  return Center(
                                    child: Text(state.errorMessage),
                                  );
                                }
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                          BlocBuilder<LyricsViewerBloc, BlocState>(
                            builder: (context, state) {
                              if (state is LoadingState) {
                                return const Center(
                                  child: AppCircularProgressIndicator(),
                                );
                              }
                              if (state is BlocSuccessState<LyricsData>) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: _bottomSectionHeight, left: 16.0, right: 16.0),
                                  child: BlocBuilder<JuceMixBloc, BlocState>(
                                      buildWhen: (previous, current) => current is JuceMixRecordProgressUpdateState,
                                      builder: (context, playerState) {
                                        return IgnorePointer(
                                          child: RepaintBoundary(
                                            child: LyricsViewer(
                                              key: _lyricsViewerKey,
                                              lyricsData: state.data,
                                              position: playerState is JuceMixRecordProgressUpdateState ? playerState.progressInMillis : 0,
                                            ),
                                          ),
                                        );
                                      }),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                          Positioned(
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: SizedBox(
                                height: _bottomSectionHeight,
                                child: Stack(
                                  children: [
                                    Align(
                                      alignment: Alignment.center,
                                      child: BlocBuilder<RecordingScreenBloc, BlocState>(
                                          buildWhen: (_, current) => current is! RecordingUpdatePlayerProgressState && current is! RecordingUpdateAudioLevelState,
                                          builder: (context, state) {
                                            return Padding(
                                              padding: const EdgeInsets.symmetric(horizontal: 16),
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  state is RecordingSuccessState
                                                      ? GestureDetector(
                                                          onTap: () {
                                                            recordingScreenBloc.add(const StopRecordingEvent(discardRecording: true));
                                                            recordingScreenBloc.add(InitRecordingEvent(showTips: false));
                                                          },
                                                          child: Padding(
                                                            padding: const EdgeInsets.only(left: 16.0),
                                                            child: _PostRecordingIcon(
                                                              label: 'Restart',
                                                              icon: AssetPaths.restartButton,
                                                              labelSize: 10,
                                                              iconSize: 28,
                                                              extraPadding: 6,
                                                            ),
                                                          ),
                                                        )
                                                      : SizedBox(
                                                          width: TransParentRoundIconButtonSize.large.size,
                                                        ),
                                                  state is RecordingSuccessState
                                                      ? GestureDetector(
                                                          onTap: () async {
                                                            if (context.mounted) {
                                                              final recordingBloc = context.read<RecordingScreenBloc>();
                                                              MixerComposeModel newComposeModel = MixerComposeModel.fromJson(context.read<JuceMixBloc>().mixComposeModel.toJson());
                                                              context.read<ShareDataBloc>().add(ShareDataEvent(inputMic: state.inputMic));

                                                              unawaited(context
                                                                  .pushRoute(
                                                                VocalFiltersRoute(
                                                                  recordedVocalPath: state.outputFilePath,
                                                                  normalizedRecordingPath: state.normalizedOutputFilePath,
                                                                  downloadWavFileCubit: context.read<DownloadWavFileCubit>(),
                                                                  mixComposeModel: newComposeModel,
                                                                  timeDiff: state.timeDiff,
                                                                ),
                                                              )
                                                                  .then((_) async {
                                                                logger.d("=========================== BACK TO RECORDING SCREEN ==========================");
                                                                if (context.mounted) {
                                                                  recordingBloc.add(InitRecordingEvent(showTips: false));
                                                                }
                                                              }));
                                                            }
                                                          },
                                                          child: Padding(
                                                            padding: const EdgeInsets.only(left: 12.0),
                                                            child: _PostRecordingIcon(
                                                              label: 'Mix & Master',
                                                              icon: AssetPaths.finalize,
                                                              labelSize: 12,
                                                              iconSize: 84,
                                                            ),
                                                          ),
                                                        )
                                                      : RecordingButton(
                                                          isLoading: state is! RecordingInProgressState &&
                                                              state is! AudioRecorderReadyState &&
                                                              state is! RecordingUpdatePlayerProgressState &&
                                                              state is! RecordingUpdateAudioLevelState,
                                                          isRecording: state is RecordingInProgressState,
                                                          onPressed: () {
                                                            HapticFeedback.mediumImpact();
                                                            if (state is! RecordingInProgressState) {
                                                              recordingScreenBloc.add(StartRecordingEvent());
                                                            } else {
                                                              recordingScreenBloc.add(const StopRecordingEvent());
                                                            }
                                                          },
                                                        ),
                                                  state is RecordingSuccessState
                                                      ? GestureDetector(
                                                          onTap: () async {
                                                            Navigator.pop(context);
                                                          },
                                                          child: Padding(
                                                            padding: const EdgeInsets.only(right: 16.0),
                                                            child: _PostRecordingIcon(
                                                              label: 'Discard',
                                                              icon: AssetPaths.discard,
                                                              labelSize: 10,
                                                            ),
                                                          ),
                                                        )
                                                      : SizedBox(
                                                          width: TransParentRoundIconButtonSize.large.size,
                                                        ),
                                                ],
                                              ),
                                            );
                                          }),
                                    )
                                  ],
                                ),
                              )),
                          Positioned(
                            bottom: 16,
                            width: MediaQuery.sizeOf(context).width,
                            child: VUMeter(),
                          )
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        )
      ],
    );
  }
}

class _PostRecordingIcon extends StatelessWidget {
  final String label;
  final String icon;
  final double labelSize;
  final double iconSize;
  final double extraPadding;

  const _PostRecordingIcon({
    required this.label,
    required this.icon,
    required this.labelSize,
    this.iconSize = 32,
    this.extraPadding = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox.square(
          dimension: iconSize,
          child: ImageLoader.fromAsset(icon),
        ),
        SizedBox(height: extraPadding),
        Text(
          label,
          style: AppTextStyles.text10regular.copyWith(
            fontFamily: AppFonts.iceland,
            fontSize: labelSize,
          ),
        )
      ],
    );
  }
}
