import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_arch/base_service.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class RecordingService extends BaseService {
  Future<Map<String, dynamic>?> getAnnotatedWav({
    required String songId,
    required String genre,
    required String genreId,
    required String scale,
    required String userTempo,
  }) async {
    return await DI().resolve<ApiClient>().post(
      Endpoints.getAnnotatedWav,
      body: {"master_song_id": songId, "genre": genre, "genre_id": genreId, "scale": scale, "user_tempo": userTempo},
    );
  }
}
