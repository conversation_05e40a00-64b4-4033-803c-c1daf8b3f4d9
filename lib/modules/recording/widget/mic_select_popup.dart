import 'package:flutter/material.dart';
import 'package:melodyze/core/recorder/recorder_channel.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/butons/transparent_round_icon_button.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

class MicSelectPopUp extends StatelessWidget {
  final List<RecorderDevice> micList;
  final Function(RecorderDevice mic)? onSelected;
  final RecorderDevice? selectedMic;

  const MicSelectPopUp({
    super.key,
    required this.micList,
    this.onSelected,
    this.selectedMic,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showMenu(context),
      child: AppGradientContainer(
        height: 26,
        width: 26,
        gradient: AppGradients.gradientBlackPurpleTeal,
        borderGradient: AppGradients.gradientPinkBlueBorder,
        borderRadius: BorderRadius.circular(32),
        borderWidth: 1,
        child: Transform.scale(
          scale: 0.8,
          child: const TransParentRoundIconButton(
            icon: Icons.menu,
            backgroundColor: Colors.transparent,
          ),
        ),
      ),
    );
  }

  Future<void> _showMenu(BuildContext context) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset.zero, ancestor: overlay),
        button.localToGlobal(button.size.bottomRight(Offset.zero), ancestor: overlay),
      ),
      Offset.zero & overlay.size,
    );

    final selectedValue = await showMenu(
        context: context,
        position: position,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: Colors.transparent,
        elevation: 0,
        items: [
          PopupMenuItem(
            padding: EdgeInsets.zero,
            child: AppGradientContainer(
              gradient: AppGradients.gradientBlackTeal,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(micList.length, (index) {
                  final mic = micList[index];
                  final selectedMicColor = mic == selectedMic ? AppColors.white : AppColors.white50;
                  return PopupMenuItem(
                    value: mic,
                    enabled: mic != selectedMic,
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                          child: Row(
                            children: [
                              Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  ImageLoader.fromAsset(
                                    AssetPaths.microphone,
                                    color: selectedMicColor,
                                    height: 24,
                                    width: 24,
                                  ),
                                  if (mic == selectedMic)
                                    const Positioned(
                                      bottom: 0,
                                      right: -2,
                                      child: Icon(
                                        Icons.check_circle,
                                        size: 12,
                                      ),
                                    )
                                ],
                              ),
                              const SizedBox(width: 5),
                              Flexible(
                                child: Text(
                                  mic.portType,
                                  style: AppTextStyles.text16medium.copyWith(
                                    color: selectedMicColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (index != micList.length - 1) ImageLoader.fromAsset(AssetPaths.gradientdivider),
                      ],
                    ),
                  );
                }),
              ),
            ),
          )
        ]);
    if (selectedValue != null) {
      onSelected?.call(selectedValue);
    }
  }
}
