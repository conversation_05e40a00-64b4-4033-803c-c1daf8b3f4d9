import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_bloc.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_states.dart';

class VUMeter extends StatefulWidget {
  const VUMeter({super.key});

  @override
  State<VUMeter> createState() => _VUMeterState();
}

class _VUMeterState extends State<VUMeter> with SingleTickerProviderStateMixin {
  bool _hasClipped = false;
  double minDecibel = -160.0;
  double maxDecibel = 0.0;
  late double _currentLevel = minDecibel;

  void updateAudioLevel(double level) {
    setState(() {
      _currentLevel = level;
      if (level >= maxDecibel) {
        _hasClipped = true;
      }
    });
  }

  double get progress {
    return (_currentLevel - minDecibel) / (maxDecibel - minDecibel);
  }

  Color get levelColor {
    if (progress > 0.9) return Colors.red;
    if (progress > 0.7) return Colors.orangeAccent;
    return Colors.green;
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RecordingScreenBloc, BlocState>(
      listenWhen: (_, current) => current is RecordingUpdateAudioLevelState || current is AudioRecorderReadyState,
      listener: (context, state) {
        if (state is RecordingUpdateAudioLevelState) {
          updateAudioLevel(state.audioLevel);
        }

        if (state is AudioRecorderReadyState) {
          final recordingBloc = context.read<RecordingScreenBloc>();
          setState(() {
            _hasClipped = false;
            minDecibel = recordingBloc.audioLevelRange.min;
            maxDecibel = recordingBloc.audioLevelRange.max;
          });
        }
      },
      buildWhen: (previous, current) => current is RecordingInProgressState || current is AudioRecorderReadyState,
      builder: (context, state) {
        return state is RecordingInProgressState
            ? Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: SizedBox(
                            width: MediaQuery.sizeOf(context).width,
                            child: LinearProgressIndicator(
                              value: progress,
                              backgroundColor: AppColors.white5,
                              valueColor: AlwaysStoppedAnimation<Color>(levelColor),
                              borderRadius: BorderRadius.circular(15),
                            ),
                          ),
                        ),
                        SizedBox(width: 8.0),
                        if (_hasClipped)
                          Icon(
                            Icons.error,
                            color: Colors.red,
                            size: 16,
                          ),
                      ],
                    ),
                  ],
                ),
              )
            : SizedBox();
      },
    );
  }
}
