import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/atom/gradient_animation.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

class RecordingButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final bool isRecording;
  final bool isLoading;

  const RecordingButton({
    super.key,
    this.onPressed,
    this.isRecording = false,
    this.isLoading = false,
  });

  @override
  State<RecordingButton> createState() => _RecordingButtonState();
}

class _RecordingButtonState extends State<RecordingButton> {
  BoxShadow shadow(double blurRadius) {
    return BoxShadow(
      color: AppColors.pinkGlow,
      blurRadius: blurRadius,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        child: Stack(
          key: const ValueKey('button'),
          alignment: Alignment.center,
          children: [
            if (widget.isRecording && !widget.isLoading)
              RippleAnimation(
                size: 200,
                primaryColor: Color(0xFFD355D3), // Pink
                secondaryColor: Color(0xFF4D00FF), // Purple
                child: Icon(
                  Icons.mic,
                  size: 50,
                  color: Colors.white,
                ),
              ),
            SizedBox(
              width: 80,
              height: 80,
              child: widget.isLoading
                  ? const CircularProgressIndicator(
                      key: ValueKey('loading'),
                      color: AppColors.white70,
                    )
                  : GestureDetector(
                      onTap: widget.onPressed,
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.black,
                          boxShadow: widget.isRecording
                              ? [
                                  shadow(0.12),
                                  shadow(0.25),
                                  shadow(0.87),
                                  shadow(1.75),
                                  shadow(3.0),
                                  shadow(5.24),
                                ]
                              : null,
                        ),
                        child: AppGradientContainer(
                          gradient: widget.isRecording ? null : AppGradients.gradientBlackPurpleTeal,
                          borderGradient: widget.isRecording ? null : AppGradients.gradientPinkBlueBorder,
                          borderRadius: BorderRadius.circular(50),
                          child: ImageLoader.fromAsset(widget.isRecording ? AssetPaths.recordMicOn : AssetPaths.recordMicOff),
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
