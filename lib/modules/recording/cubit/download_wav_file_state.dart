part of 'download_wav_file_cubit.dart';

sealed class DownloadWavFileState extends BlocState {
  const DownloadWavFileState();

  @override
  List<Object> get props => [];
}

final class DownloadWavFileInitial extends DownloadWavFileState {}

final class DownloadWavFileInProgressState extends DownloadWavFileState {}

final class DownloadWavFileSuccessState extends DownloadWavFileState {}

final class DownloadWavFileErrorState extends DownloadWavFileState {}
