import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/services/file_manager/file_manager.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/modules/recording/repo/recording_repo.dart';

part 'download_wav_file_state.dart';

class DownloadWavFileCubit extends Cubit<DownloadWavFileState> {
  final RecordingRepo recordingRepo;
  String? bgmWavPath;

  DownloadWavFileCubit({required this.recordingRepo}) : super(DownloadWavFileInitial());

  Future<void> downloadWavFile({
    required String masterSongId,
    required String genre,
    required String genreId,
    required String scale,
    required String tempo,
  }) async {
    try {
      logger.d("⏳ wav_file downloading ");
      emit(DownloadWavFileInProgressState());
      final response = await recordingRepo.getAnnotatedWav(
        songId: masterSongId,
        genre: genre,
        genreId: genreId,
        scale: scale,
        userTempo: tempo,
      );
      String wavS3url = (response.data as Map<String, dynamic>)["wav_song_path"];
      final result = await FileManagerQuickAccess.download(wavS3url, FileType.bgmWav);
      if (result is FileOperationSuccess) {
        bgmWavPath = result.localPath;
      } else {
        throw Exception('Failed to download WAV file: ${(result as FileOperationError).message}');
      }
      logger.d("✅ wav_file downloaded");
      emit(DownloadWavFileSuccessState());
    } catch (e) {
      logger.e("Error downloading wav file: $e");
      emit(DownloadWavFileErrorState());
    }
  }
}
