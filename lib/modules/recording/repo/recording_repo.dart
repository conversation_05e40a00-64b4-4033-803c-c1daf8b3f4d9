import 'dart:async';

import 'package:melodyze/core/generic_arch/base_repository.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/modules/recording/service/recording_service.dart';

class RecordingRepo extends BaseRepo {
  final RecordingService recordingService;

  RecordingRepo({required this.recordingService});

  Future<RepoResult> getAnnotatedWav({
    required String songId,
    required String genre,
    required String genreId,
    required String scale,
    required String userTempo,
  }) async {
    return await executeAndReturnResult(
      () => recordingService.getAnnotatedWav(
        songId: songId,
        genre: genre,
        genreId: genreId,
        scale: scale,
        userTempo: userTempo,
      ),
      (json) async => RepoResult.success(json["data"]),

      // (json['data'] as List<dynamic>).map((e) => AnnotatedData.fromJson(e)).toList()),
    );
  }
}
