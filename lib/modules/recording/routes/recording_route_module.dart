import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class RecordingRoutesPath {
  static const String recording = '/recording';
}

class RecordingRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: RecordingRoutesPath.recording,
          page: RecordingRoute.page,
        ),
      ];
}
