import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_arch/base_service.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class PreferenceService extends BaseService {
  Future<Map<String, dynamic>?> getPreferences(int skip, int limit, String? searchQuery) async {
    final queryParams = <String, String>{
      "skip": skip.toString(),
      "limit": limit.toString(),
    };

    if (searchQuery != null && searchQuery.isNotEmpty) {
      queryParams['search_text'] = searchQuery;
    }
    
    return await DI().resolve<ApiClient>().get(
          Endpoints.getPreferences,
          queryParameters: queryParams,
        );
  }

  Future<Map<String, dynamic>?> savePreferences({
    required List<String> commercialGenreIds,
    required List<String> artistIds,
  }) async {
    final body = {
      "commercial_genre_ids": commercialGenreIds,
      "arist_ids": artistIds,
    };
    return await DI().resolve<ApiClient>().post(Endpoints.savePreferences, body: body);
  }
}
