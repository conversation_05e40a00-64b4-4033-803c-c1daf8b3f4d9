// write flutter freezed class for  List<PreferenceList> preferenceList;

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/modules/preference/model/preference_model/preference_model.dart';
import 'package:melodyze/modules/preference/model/pagination_model/pagination_model.dart';

part 'preference_list.freezed.dart';
part 'preference_list.g.dart';

@freezed
class PreferenceList with _$PreferenceList {
  factory PreferenceList({
    @JsonKey(name: 'data') @Default([]) List<PreferenceModel> data,
    @JsonKey(name: 'pagination') PaginationModel? pagination,
  }) = _PreferenceList;

  factory PreferenceList.fromJson(Map<String, dynamic> payload) =>
      _$PreferenceListFromJson(payload);
}
