import 'package:freezed_annotation/freezed_annotation.dart';

part 'preference_model.freezed.dart';
part 'preference_model.g.dart';

@freezed
class PreferenceModel with _$PreferenceModel {
  factory PreferenceModel({
    @<PERSON><PERSON><PERSON>ey(name: '_id') required String id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'genre_name') String? genreName,
    @<PERSON>son<PERSON>ey(name: 'artist_name') String? artistName,
    @Json<PERSON>ey(name: 'is_active') @Default(false) bool isActive,
    @<PERSON>son<PERSON>ey(name: 'thumbnail_path') @Default('') String thumbnailPath,
  }) = _PreferenceModel;

  factory PreferenceModel.fromJson(Map<String, dynamic> payload) =>
      _$PreferenceModelFromJson(payload);
}
