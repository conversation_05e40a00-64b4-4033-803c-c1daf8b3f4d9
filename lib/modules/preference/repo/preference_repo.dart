import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/core/generic_arch/base_repository.dart';
import 'package:melodyze/modules/preference/model/preference_list.dart';
import 'package:melodyze/modules/preference/service/preference_service.dart';

class PreferenceRepo extends BaseRepo {
  final PreferenceService preferenceService;

  PreferenceRepo({required this.preferenceService});

  Future<RepoResult> getPreferences(int skip, int limit, String? searchQuery) async {
    return await executeAndReturnResult(
      () => preferenceService.getPreferences(skip, limit, searchQuery),
      (json) async => RepoResult.success(PreferenceList.fromJson(json)),
    );
  }

  Future<RepoResult> savePreferences({
    required List<String> commercialGenreIds,
    required List<String> artistIds,
  }) async {
    return await executeAndReturnResult(
      () => preferenceService.savePreferences(
        commercialGenreIds: commercialGenreIds,
        artistIds: artistIds,
      ),
      (json) async => RepoResult.success(true),
    );
  }
}
