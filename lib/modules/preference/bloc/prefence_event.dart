import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/modules/preference/model/preference_model/preference_model.dart';

class SavePreferenceEvent extends BaseEvent {
  final List<PreferenceModel> selectedPreferences;
  const SavePreferenceEvent(this.selectedPreferences);

  @override
  List<Object?> get props => [];
}

class PreferenceLoadDataEvent extends BaseEvent {
  final String? searchQuery;
  const PreferenceLoadDataEvent({this.searchQuery});

  @override
  List<Object?> get props => [searchQuery];
}

class PreferenceLoadMoreEvent extends BaseEvent {
  final String? searchQuery;
  const PreferenceLoadMoreEvent({this.searchQuery});

  @override
  List<Object?> get props => [searchQuery];
}