import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/core/wrappers/analytics/user_event.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';
import 'package:melodyze/modules/preference/bloc/prefence_event.dart';
import 'package:melodyze/modules/preference/bloc/preference_state.dart';
import 'package:melodyze/modules/preference/model/preference_list.dart';
import 'package:melodyze/modules/preference/model/preference_model/preference_model.dart';
import 'package:melodyze/modules/preference/repo/preference_repo.dart';

class PreferenceBloc extends SafeBloc {
  final PreferenceRepo preferenceRepo;
  final SecureStorageHelper _secureStorageHelper;

  bool isFetching = false;
  bool hasMore = true;
  int skip = 0;
  static const int limit = 200;

  PreferenceBloc({
    required this.preferenceRepo,
    required SecureStorageHelper secureStorageHelper,
  })  : _secureStorageHelper = secureStorageHelper,
        super(InitialState()) {
    on<PreferenceLoadDataEvent>(_loadData);
    on<PreferenceLoadMoreEvent>(_loadMoreData);

    on<SavePreferenceEvent>(_savePreference);
    add(PreferenceLoadDataEvent(searchQuery: null));
    UserEvent.shared.prefs_loaded();
  }

  FutureOr<void> _loadData(PreferenceLoadDataEvent event, _) async {
    emit(LoadingState());
    skip = 0; // Reset pagination when new search starts
    hasMore = true;

    final preferences = await preferenceRepo.getPreferences(skip, limit, event.searchQuery);

    if (!preferences.isSuccess) {
      emit(BlocFailureState(preferences.error));
      return;
    }

    final PreferenceList data = preferences.data as PreferenceList;
    hasMore = data.pagination?.next ?? false;
    skip = data.pagination?.skip ?? 0; // Update skip value for next request

    emit(BlocSuccessState<PreferenceList>(data));
  }

  FutureOr<void> _loadMoreData(PreferenceLoadMoreEvent event, _) async {
    if (isFetching || !hasMore) return; // Prevent multiple calls
    isFetching = true;

    final preferences = await preferenceRepo.getPreferences(skip + limit, limit, event.searchQuery);
    if (!preferences.isSuccess) {
      emit(BlocFailureState(preferences.error));
      isFetching = false;
      return;
    }

    final PreferenceList newData = preferences.data as PreferenceList;
    hasMore = newData.pagination?.next ?? false;
    skip = newData.pagination?.skip ?? 0; // Update skip for next call

    if (state is BlocSuccessState<PreferenceList>) {
      final currentList = (state as BlocSuccessState<PreferenceList>).data.data;
      emit(BlocSuccessState<PreferenceList>(PreferenceList(data: [...currentList, ...newData.data], pagination: newData.pagination)));
    } else {
      emit(BlocSuccessState<PreferenceList>(newData));
    }

    isFetching = false;
  }

  FutureOr<void> _savePreference(SavePreferenceEvent event, Emitter<BlocState> _) async {
    var items = event.selectedPreferences.map((e) => "${e.id}:${e.artistName ?? e.genreName}").join(', ');
    UserEvent.shared.prefs_proceed_click(items);
    emit(SaveLoadingState());
    List<String> commercialGenreIds = [], artistIds = [];

    try {
      for (PreferenceModel pref in event.selectedPreferences) {
        if (pref.genreName != null) {
          commercialGenreIds.add(pref.id);
        } else {
          artistIds.add(pref.id);
        }
      }
      final result = await preferenceRepo.savePreferences(commercialGenreIds: commercialGenreIds, artistIds: artistIds);
      if (result.isSuccess) {
        await _secureStorageHelper.write(SecureStorageKeys.isPreferenceSelected, 'true');
        emit(SaveBlocSuccessState<bool>(result.data));
        UserEvent.shared.prefs_proceed_success();
      } else {
        emit(SaveBlocFailureState(result.error));
        UserEvent.shared.prefs_proceed_failure(result.error.toString());
      }
    } catch (e) {
      emit(BlocFailureState(RepoError('Error in _savePreference', e)));
      UserEvent.shared.prefs_proceed_failure(e.toString());
    }
  }
}
