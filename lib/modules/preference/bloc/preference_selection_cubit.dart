import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/wrappers/analytics/user_event.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/preference/model/preference_model/preference_model.dart';

class PreferenceSelectionCubit extends Cubit<List<PreferenceModel>> {
  List<PreferenceModel>? allPreferences;

  PreferenceSelectionCubit() : super([]);

  static const int minSelection = 6;
  static const int maxSelection = 6;

  bool isSelectionValid() {
    return state.length >= minSelection && state.length <= maxSelection;
  }

  void setAllPreferences(List<PreferenceModel> preferences) {
    allPreferences = preferences;
  }

  void toggleSelection(PreferenceModel selectedPreference) {
    // If the preference is not part of allPreferences, do not toggle it.
    if (allPreferences == null || !allPreferences!.any((p) => p.id == selectedPreference.id)) return;

    String title = '${selectedPreference.genreName ?? selectedPreference.artistName}';
    if (state.any((preference)=> preference.id == selectedPreference.id)) {
      UserEvent.shared.prefs_deselect(title, selectedPreference.id);
      emit(state.where((p) => p.id != selectedPreference.id).toList());
    } else {
      if (state.length >= maxSelection) {
        DI().resolve<AppToast>().showToast('You can only select up to $maxSelection preferences.');
        return;
      }
      UserEvent.shared.prefs_select(title, selectedPreference.id);
      emit([...state, selectedPreference]);
    }
  }

  List<PreferenceModel> getSelectedPreferences() {
    return state;
  }

  void resetSelections() {
    emit([]);
  }
}
