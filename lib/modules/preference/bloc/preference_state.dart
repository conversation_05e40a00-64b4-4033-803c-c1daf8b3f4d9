import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';

class SaveLoadingState extends BlocState {
  @override
  List<Object?> get props => [];
}

class SaveBlocSuccessState<T> extends BlocState {
  final T data;

  const SaveBlocSuccessState(
    this.data,
  );

  @override
  List<Object?> get props => [data];
}

class SaveBlocFailureState extends BlocState {
  final RepoError error;

  const SaveBlocFailureState(this.error);

  @override
  List<Object?> get props => [error];
}
