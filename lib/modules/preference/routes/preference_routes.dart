import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class PreferenceRoutesPath {
  static const String preferencePath = '/preference';
}

class PreferenceRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: PreferenceRoutesPath.preferencePath,
          page: PreferenceRoute.page,
        ),
      ];
}
