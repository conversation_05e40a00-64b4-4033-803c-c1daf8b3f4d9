import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/ui/molecules/checked_tile.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/modules/preference/bloc/preference_selection_cubit.dart';
import 'package:melodyze/modules/preference/model/preference_model/preference_model.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

class PreferenceGrid extends StatelessWidget {
  const PreferenceGrid({super.key});
  @override
  Widget build(BuildContext context) {
    final List<PreferenceModel> allPreferences = context.read<PreferenceSelectionCubit>().allPreferences ?? [];
    return AnimationLimiter(
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 0.75,
          crossAxisSpacing: 20,
          mainAxisSpacing: 16,
        ),
        itemCount: allPreferences.length,
        itemBuilder: (context, index) {
          final item = allPreferences[index];
          return AnimationConfiguration.staggeredGrid(
            position: index,
            duration: const Duration(milliseconds: 600),
            columnCount: 3,
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: BlocBuilder<PreferenceSelectionCubit, List<PreferenceModel>>(
                  builder: (context, selectedPreferences) {
                    final isSelected = selectedPreferences.any((selected) => selected.id == item.id);
                    return CheckedTile(
                      onTap: () => context.read<PreferenceSelectionCubit>().toggleSelection(item),
                      isSelected: isSelected,
                      s3ImageUrl: item.thumbnailPath,
                      title: '${item.genreName ?? item.artistName}',
                    );
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class PreferenceGridShimmer extends StatelessWidget {
  const PreferenceGridShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3, // Same as PreferenceGrid
          childAspectRatio: 0.75, // Match PreferenceGrid aspect ratio
          crossAxisSpacing: 20,
          mainAxisSpacing: 16,
        ),
        itemCount: 15, // Placeholder count (adjust as needed)
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredGrid(
            position: index,
            duration: const Duration(milliseconds: 400),
            columnCount: 3,
            child: SlideAnimation(
              verticalOffset: 30.0,
              child: FadeInAnimation(
                child: _buildShimmerTile(),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildShimmerTile() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Image shimmer
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(top: 4.0),
            child: Shimmer.fromColors(
              baseColor: AppColors.darkBlack.withValues(alpha: 0.3),
              highlightColor: AppColors.darkCyan.withValues(alpha: 0.1),
              period: const Duration(milliseconds: 600),
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.darkCyan.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(36),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.darkBlack.withValues(alpha: 0.1),
                        const Color.fromARGB(255, 5, 49, 54).withValues(alpha: 0.05),
                        AppColors.darkBlack.withValues(alpha: 0.1),
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                    borderRadius: BorderRadius.circular(36),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        // Title shimmer
        Shimmer.fromColors(
          baseColor: AppColors.darkBlack.withValues(alpha: 0.2),
          highlightColor: Color.fromARGB(255, 5, 49, 54).withValues(alpha: 0.05),
          child: Container(
            height: 14,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.darkCyan.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ],
    );
  }
}
