import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/butons/app_button.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/ui/tokens/design_constants.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/save_and_upload/bloc/save_and_upload_bloc.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
import 'package:melodyze/modules/vocal_filters/bloc/vocal_filters_bloc.dart';

@RoutePage()
class SaveAndUploadScreen extends StatelessWidget {
  final ShareDataBloc shareDataBloc;
  final VocalfiltersBloc vocalfiltersBloc;
  final JuceMixBloc juceMixBloc;


  const SaveAndUploadScreen({
    super.key,
    required this.shareDataBloc,
    required this.vocalfiltersBloc,
    required this.juceMixBloc,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(providers: [
      BlocProvider(
        create: (context) => SaveAndUploadBloc(
          userId: context.authBloc.user?.id,
          shareDataBloc: context.read<ShareDataBloc>(),
          vocalfiltersBloc: vocalfiltersBloc,
          juceMixBloc: juceMixBloc,
        ),
      ),
      // BlocProvider.value(value: masterFiltersBloc),
      // BlocProvider.value(value: vocalfiltersBloc)
    ], child: const _SaveAndUploadScreen());
  }
}

class _SaveAndUploadScreen extends StatelessWidget {
  const _SaveAndUploadScreen();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SaveAndUploadBloc, SaveAndUploadState>(
        buildWhen: (previous, current) => current is UploadSuccessState,
        builder: (context, state) {
          return MeloScaffold(
            onPopInvoked: () => context.replaceRoute(DashboardRoute()),
            popScopeDescription: state is UploadSuccessState ? null : 'Are you sure you want to discard the recording?',
            body: SafeArea(
              child: Center(
                child: AppGradientContainer(
                  width: 354,
                  margin: const EdgeInsets.all(24),
                  padding: const EdgeInsets.all(24),
                  borderGradient: AppGradients.gradientPinkBlueBorder,
                  borderRadius: const BorderRadius.all(Radius.circular(DesignConstants.defaultRadius)),
                  child: BlocBuilder<SaveAndUploadBloc, SaveAndUploadState>(builder: (context, state) {
                    if (state is SaveAndUploadErrorState) {
                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            state.error,
                            textAlign: TextAlign.center,
                            style: AppTextStyles.text12regular,
                          ),
                          const SizedBox(height: 16),
                          AppButton(
                            text: 'Try Again',
                            onPressed: () {
                              context.read<SaveAndUploadBloc>().add(UploadEvent());
                            },
                          ),
                        ],
                      );
                    }

                    if (state is UploadSuccessState) {
                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 76,
                            height: 76,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: ImageLoader.fromAsset(AssetPaths.recordingSaved),
                            ),
                          ),
                          Text(
                            'Recording Saved',
                            style: AppTextStyles.text24semiBold.copyWith(
                              fontFamily: AppFonts.iceland,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 36.0,
                            child: AppButton(
                              text: 'View Saved Recording',
                              gradient: AppGradients.gradientBlackPurpleTeal,
                              borderGradient: AppGradients.gradientPinkBlueBorder,
                              innerPadding: EdgeInsets.symmetric(horizontal: 27, vertical: 8),
                              textStyle: AppTextStyles.text16semiBold,
                              onPressed: () {
                                DI().resolve<ProfileBloc>().add(const LoadRecordingsEvent(showCurrentRecording: true));
                                context.replaceRoute(ProfileRoute());
                              },
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              DI().resolve<ProfileBloc>().add(const LoadRecordingsEvent());
                              context.replaceRoute(ProfileRoute(showCurrentRecording: true));
                            },
                            child: Text(
                              'My Recordings',
                              style: AppTextStyles.text14semiBold.copyWith(
                                fontFamily: AppFonts.inter,
                                color: AppColors.white,
                              ),
                            ),
                          ),
                          // Text(
                          //   'Share with Your Friends',
                          //   style: AppTextStyles.text14regular.copyWith(color: AppColors.primary100),
                          // ),
                          // IconButton(
                          //   icon: const Icon(
                          //     Icons.ios_share,
                          //     color: AppColors.white,
                          //   ),
                          //   onPressed: () async {
                          //     final bloc = context.read<SaveAndUploadBloc>();
                          //     final outputPath = bloc.outputPath;
                          //     final result = await Share.shareXFiles([XFile(outputPath)], text: bloc.saveFileName);

                          //     if (result.status == ShareResultStatus.success) {
                          //       DI().resolve<AppToast>().showToast('Successfully shared');
                          //     }
                          //   },
                          // )
                          const SizedBox(height: 16)
                        ],
                      );
                    }

                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Processing Your Recording',
                          style: AppTextStyles.text24semiBold.copyWith(
                            fontFamily: AppFonts.iceland,
                          ),
                        ),
                        const SizedBox(height: 24),
                        const AppCircularProgressIndicator(),
                        const SizedBox(height: 24),
                        Text(
                          (state is MergeProcessingState)
                              ? 'Merging audio and video...'
                              : (state is UploadProcessingState)
                                  ? 'Uploading your recording...'
                                  : 'Initializing...',
                          style: AppTextStyles.text10medium.copyWith(
                            fontFamily: AppFonts.inter,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    );
                  }),
                ),
              ),
            ),
          );
        });
  }
}
