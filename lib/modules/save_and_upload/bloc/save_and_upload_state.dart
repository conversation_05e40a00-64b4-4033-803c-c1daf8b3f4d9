part of 'save_and_upload_bloc.dart';

abstract class SaveAndUploadState extends BlocState {}

abstract class SaveAndUploadErrorState extends SaveAndUploadState {
  final String error;
  SaveAndUploadErrorState(this.error);
}

class SaveAndU<PERSON>loadInitial extends SaveAndU<PERSON>loadState {
  @override
  List<Object?> get props => [];
}

class MergeProcessingState extends SaveAndUploadState {
  @override
  List<Object?> get props => [];
}

class MergeSuccessState extends SaveAndUploadState {
  final String outputPath;
  MergeSuccessState(this.outputPath);

  @override
  List<Object?> get props => [outputPath];
}

class MergeFailureState extends SaveAndUploadErrorState {
  MergeFailureState(super.error);

  @override
  List<Object?> get props => [error];
}

class UploadProcessingState extends SaveAndUploadState {
  @override
  List<Object?> get props => [];
}

class UploadSuccessState extends SaveAndUploadState {
  @override
  List<Object?> get props => [];
}

class UploadFailureState extends SaveAndUploadErrorState {
  UploadFailureState(super.error);

  @override
  List<Object?> get props => [error];
}
