import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class SaveAndUploadRoutesPath {
  static const String saveAndUpload = '/saveAndUpload';
}

class SaveAndUploadRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: SaveAndUploadRoutesPath.saveAndUpload,
          page: SaveAndUploadRoute.page,
        ),
      ];
}
