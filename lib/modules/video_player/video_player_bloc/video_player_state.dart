part of 'video_player_bloc.dart';

abstract class VideoState extends BlocState {
  const VideoState();
}

class <PERSON>I<PERSON>tialState extends VideoState {
  @override
  List<Object?> get props => [];
}

class VideoLoadingState extends VideoState {
  @override
  List<Object?> get props => [];
}

class VideoLoadedState extends VideoState {
  @override
  List<Object?> get props => [];
}

class VideoPlayingState extends VideoState {
  @override
  List<Object?> get props => [];
}

class VideoPausedState extends VideoState {
  @override
  List<Object?> get props => [];
}

class VideoCurrentPositionState extends VideoState {
  final Duration position;
  const VideoCurrentPositionState(this.position);
  @override
  List<Object?> get props => [position];
}

class VideoErrorState extends VideoState {
  final String error;
  const VideoErrorState(this.error);

  @override
  List<Object?> get props => [];
}
