part of 'video_player_bloc.dart';

abstract class VideoPlayerEvent extends BaseEvent {
  const VideoPlayerEvent();
}

class LoadVideoEvent extends VideoPlayerEvent {
  final String videoFilePath;
  final VideoType videoType;
  final bool autoPlay;
  final bool loop;
  final bool mixWithOthers;

  const LoadVideoEvent({
    required this.videoFilePath,
    this.videoType = VideoType.file,
    this.autoPlay = false,
    this.loop = false,
    this.mixWithOthers = false,
  });
  @override
  List<Object?> get props => [videoFilePath, videoType, autoPlay, loop, mixWithOthers];
}

class VideoPlayEvent extends VideoPlayerEvent {
  @override
  List<Object?> get props => [];
}

class VideoPauseEvent extends VideoPlayerEvent {
  @override
  List<Object?> get props => [];
}

class VideoStopEvent extends VideoPlayerEvent {
  @override
  List<Object?> get props => [];
}

class VideoSeekEvent extends VideoPlayerEvent {
  final double milliseconds;
  const VideoSeekEvent({required this.milliseconds});

  @override
  List<Object> get props => [milliseconds];
}
