import 'dart:async';

import 'package:better_player_plus/better_player_plus.dart';
import 'package:flutter/widgets.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/ui/utilities/screen_utils.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

part 'video_player_event.dart';
part 'video_player_state.dart';

enum VideoType { url, file }

class VideoPlayerBloc extends SafeBloc<VideoPlayerEvent, VideoState> {
  final bool isReel;
  VideoPlayerBloc({this.isReel = false}) : super(VideoInitialState()) {
    ScreenUtils.keepScreenOn();
    if (isReel) {
      pageController = PageController(keepPage: false);
    }
    on<LoadVideoEvent>(_loadVideo);
    on<VideoPlayEvent>(_playVideo);
    on<VideoPauseEvent>(_pauseVideo);
    on<VideoStopEvent>(_stopVideo);
    on<VideoSeekEvent>(_seekVideo);
  }

  BetterPlayerController? controller;
  static const aspectRatio = 9 / 16;
  final Debouncer _debouncer = Debouncer();
  PageController? pageController;

  @override
  Future<void> close() async {
    await controller?.pause();
    controller?.dispose();
    await ScreenUtils.keepScreenOff();
    _debouncer.dispose();
    pageController?.dispose();
    await super.close();
  }

  FutureOr<void> _loadVideo(LoadVideoEvent event, _) async {
    emit(VideoLoadingState());
    try {
      await controller?.pause();
      controller?.dispose();

      final BetterPlayerDataSource dataSource;
      switch (event.videoType) {
        case VideoType.url:
          dataSource = BetterPlayerDataSource(BetterPlayerDataSourceType.network, event.videoFilePath);
          break;
        case VideoType.file:
          dataSource = BetterPlayerDataSource(BetterPlayerDataSourceType.file, event.videoFilePath);
          break;
      }
      dataSource.copyWith(
        cacheConfiguration: BetterPlayerCacheConfiguration(
          useCache: true,
          preCacheSize: 10 * 1024 * 1024, // 10MB pre-cache
          maxCacheSize: 100 * 1024 * 1024, // 100MB max cache
          maxCacheFileSize: 15 * 1024 * 1024,
        ),
        bufferingConfiguration: BetterPlayerBufferingConfiguration(
          minBufferMs: 2000,
          maxBufferMs: 10000,
          bufferForPlaybackMs: 1500,
          bufferForPlaybackAfterRebufferMs: 2000,
        ),
      );
      BetterPlayerConfiguration configuration = BetterPlayerConfiguration(
        aspectRatio: aspectRatio,
        handleLifecycle: true,
        autoDispose: false,
        controlsConfiguration: BetterPlayerControlsConfiguration(
          showControls: false,
        ),
      );
      controller ??= BetterPlayerController(configuration);
      await controller?.setupDataSource(dataSource);
      controller?.setMixWithOthers(event.mixWithOthers);

      controller?.isVideoInitialized();

      controller?.addEventsListener((BetterPlayerEvent event) {
        // Ensure the controller and its videoPlayerController are initialized
        final videoController = controller?.videoPlayerController;
        if (videoController == null) {
          return;
        }

        switch (event.betterPlayerEventType) {
          case BetterPlayerEventType.finished:
            if (isReel) {
              autoScroll();
            } else {
              add(VideoStopEvent());
            }
            break;

          case BetterPlayerEventType.progress:
            final position = videoController.value.position;
            if (position > Duration.zero) {
              _debouncer.debounce(() => emit(VideoCurrentPositionState(position)));
            }
            break;

          case BetterPlayerEventType.initialized:
            emit(VideoLoadedState());
            logger.i('BetterPlayerEvent: ${event.betterPlayerEventType.toString()}');
            break;

          default:
            break;
        }
      });
      if (event.autoPlay) {
        add(VideoPlayEvent());
      }
      if (event.loop) {
        await controller?.setLooping(true);
      }
    } catch (e) {
      emit(VideoErrorState('Playback error occurred'));
      //Add a logger with error message and the videplay url or path
      logger.e('VideoPlayerBloc{message: $e, videoFilePath: ${event.videoFilePath} }');
    }
  }

  FutureOr<void> _playVideo(VideoPlayEvent event, _) async {
    emit(VideoPlayingState());
    if (controller?.isPlaying() ?? true) return;
    await controller?.play();
  }

  FutureOr<void> _pauseVideo(VideoPauseEvent event, _) async {
    await controller?.pause();
    emit(VideoPausedState());
  }

  FutureOr<void> _stopVideo(VideoStopEvent event, _) async {
    await controller?.pause();
    await controller?.seekTo(Duration.zero);
    emit(VideoPausedState());
  }

  FutureOr<void> _seekVideo(VideoSeekEvent event, _) async {
    _debouncer.debounce(() async {
      await controller?.seekTo(Duration(milliseconds: event.milliseconds.toInt()));
    }, 50);
  }

  void togglePlay() {
    if (controller?.isPlaying() ?? false) {
      add(VideoPauseEvent());
    } else {
      add(VideoPlayEvent());
    }
  }

  void autoScroll() {
    if (pageController == null) return;
    final nextPage = (pageController?.page ?? 0) + 1;
    pageController?.animateToPage(
      nextPage.toInt(),
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }
}
