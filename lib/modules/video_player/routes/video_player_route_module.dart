import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class VideoPlayerRoutesPath {
  static const String videoPlayerReelScreen = 'videoPlayerReelScreen';
}

class VideoPlayerRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: VideoPlayerRoutesPath.videoPlayerReelScreen,
          page: VideoPlayerReelRoute.page,
        ),
      ];
}
