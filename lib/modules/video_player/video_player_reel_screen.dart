import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/ui/molecules/video_processing_dialog.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/modules/profile/ui/widget/recording_popup_menu.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_state.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/video_player/video_player_bloc/video_player_bloc.dart';
import 'package:melodyze/modules/video_player/video_player_full_screen.dart';
import 'package:visibility_detector/visibility_detector.dart';

@RoutePage()
class VideoPlayerReelScreen extends StatefulWidget {
  final List<RecordingModel> recordings;
  final bool showMenubar;
  final bool showAppBar;
  final bool disableVideo;
  final bool showPersonalizationButton;
  final bool isReelScreen;
  final bool autoPlay;

  const VideoPlayerReelScreen({
    super.key,
    required this.recordings,
    this.showMenubar = false,
    this.showAppBar = true,
    this.disableVideo = false,
    this.showPersonalizationButton = false,
    this.isReelScreen = false,
    this.autoPlay = true,
  });

  @override
  State<VideoPlayerReelScreen> createState() => _VideoPlayerReelScreenState();
}

class _VideoPlayerReelScreenState extends State<VideoPlayerReelScreen> {
  late VideoPlayerBloc videoPlayerBloc;
  int currentIndex = 0;

  bool get isPublishedRecording => widget.recordings[currentIndex].isPublished;

  @override
  void initState() {
    super.initState();
    videoPlayerBloc = VideoPlayerBloc(isReel: widget.isReelScreen);
  }

  @override
  void dispose() {
    videoPlayerBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('video_player_reel_screen'),
      onVisibilityChanged: (info) {
        if (info.visibleFraction == 0) {
          videoPlayerBloc.add(VideoPauseEvent());
        }
      },
      child: BlocListener<ProfileBloc, BlocState>(
        listener: (context, state) {
          if (state is ProfileVideoCreatingState) {
            showVideoProcessingDialog(context);
          }
          // Remove the immediate dismissal - let the dialog handle its own state transitions
        },
        child: MeloScaffold(
        showAppBar: widget.showAppBar,
        showBackground: false,
        body: Padding(
          padding: EdgeInsets.only(top: 58.0, bottom: 108.0),
          child: BlocProvider(
            create: (context) {
              final videoFilePath = widget.recordings.first.finalVideoFilePath.isNotEmpty ? widget.recordings.first.finalVideoFilePath : widget.recordings.first.finalMixedAudioPath;
              return videoPlayerBloc
                ..add(LoadVideoEvent(
                  videoFilePath: videoFilePath,
                  videoType: VideoType.url,
                  autoPlay: widget.autoPlay,
                  loop: false,
                ));
            },
            child: PageView.builder(
              scrollDirection: Axis.vertical,
              itemCount: widget.recordings.length,
              controller: videoPlayerBloc.pageController,
              onPageChanged: (value) {
                setState(() {
                  currentIndex = value;
                });
                final recording = widget.recordings[value];
                videoPlayerBloc.add(LoadVideoEvent(
                  videoFilePath: recording.finalVideoFilePath.isNotEmpty ? recording.finalVideoFilePath : recording.finalMixedAudioPath,
                  videoType: VideoType.url,
                  autoPlay: true,
                  loop: false,
                ));
              },
              itemBuilder: (context, index) {
                final recording = widget.recordings[index];
                return VideoPlayerFullScreen(
                  key: Key(recording.id),
                  disableVideo: widget.disableVideo || recording.finalVideoFilePath.isEmpty,
                  recordingModel: recording,
                  showPersonalizationButton: widget.showPersonalizationButton,
                  isReelScreen: widget.isReelScreen,
                );
              },
            ),
          ),
        ),
        secondaryAction: widget.showMenubar
            ? (context) {
                return RecordingPopupMenu(
                  recording: widget.recordings[currentIndex],
                  isFromPlayer: true,
                  onStop: () {
                    videoPlayerBloc.add(VideoStopEvent());
                  },
                );
              }
            : null,
        ),
      ),
    );
  }
}