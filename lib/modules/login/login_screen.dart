import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/atom/top_gradient.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/analytics/user_event.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/modules/auth/bloc/authentication_bloc.dart';
import 'package:melodyze/modules/auth/bloc/authentication_event.dart';
import 'package:melodyze/modules/auth/bloc/authentication_state.dart';

@RoutePage()
class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, BlocState>(
      listener: (context, state) {
        if (state is DashboardScreenState) {
          context.replaceRoute(DashboardRoute());
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.black010101,
        body: Column(
          children: [
            Flexible(
              child: Stack(
                children: [
                  TopGradient(),
                  Center(
                    child: SizedBox.square(
                      dimension: 205,
                      child: ImageLoader.fromAsset(AssetPaths.catComplete),
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    child: SizedBox(
                      height: 223,
                      width: MediaQuery.sizeOf(context).width,
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Login or Sign up with',
                              style: AppTextStyles.text20regular.copyWith(
                                fontFamily: AppFonts.iceland,
                              ),
                            ),
                            const SizedBox(
                              height: 16,
                            ),
                            BlocBuilder<AuthenticationBloc, BlocState>(builder: (context, state) {
                              if (state is LoadingState) {
                                return const Center(
                                  child: AppCircularProgressIndicator(),
                                );
                              }
                              return Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // InkWell(
                                  //   onTap: () {
                                  //     UserEvent.shared.login_login_click('facebook');
                                  //     debugPrint('Facebook');
                                  //   },
                                  //   child: ImageLoader.fromAsset(AssetPaths.facebook),
                                  // ),
                                  // const SizedBox(
                                  //   width: 16,
                                  // ),
                                  // InkWell(
                                  //   onTap: () {
                                  //     UserEvent.shared.login_login_click('instagram');
                                  //     debugPrint('Instagram');
                                  //   },
                                  //   child: ImageLoader.fromAsset(AssetPaths.instagram),
                                  // ),
                                  // const SizedBox(
                                  //   width: 16,
                                  // ),
                                  InkWell(
                                    onTap: () {
                                      UserEvent.shared.login_login_click('google');
                                      context.read<AuthenticationBloc>().add(GoogleLogin());
                                    },
                                    child: ImageLoader.fromAsset(AssetPaths.google),
                                  ),
                                  // const SizedBox(
                                  //   width: 16,
                                  // ),
                                  // InkWell(
                                  //   onTap: () {
                                  //     UserEvent.shared.login_login_click('apple');
                                  //     debugPrint('Tiktok');
                                  //   },
                                  //   child: ImageLoader.fromAsset(AssetPaths.apple),
                                  // ),
                                  // const SizedBox(
                                  //   width: 16,
                                  // ),
                                  // InkWell(
                                  //   onTap: () {
                                  //     UserEvent.shared.login_login_click('tiktok');
                                  //     debugPrint('Tiktok');
                                  //   },
                                  //   child: ImageLoader.fromAsset(AssetPaths.tiktok),
                                  // ),
                                ],
                              );
                            })
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
