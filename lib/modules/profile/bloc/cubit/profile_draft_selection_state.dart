part of 'profile_draft_selection_cubit.dart';

sealed class ProfileDraftSelectionState extends BlocState {
  final RecordingModel? recording;
  const ProfileDraftSelectionState(
    this.recording,
  );

  @override
  List<Object> get props => [];
}

final class ProfileDraftSelectionInitial extends ProfileDraftSelectionState {
  const ProfileDraftSelectionInitial() : super(null);
}

final class ProfileDraftSelectionSelected extends ProfileDraftSelectionState {
  const ProfileDraftSelectionSelected(RecordingModel super.recording);
}
