import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';

part 'profile_draft_selection_state.dart';

class ProfileDraftSelectionCubit extends Cubit<ProfileDraftSelectionState> {
  ProfileDraftSelectionCubit() : super(ProfileDraftSelectionInitial());

  RecordingModel? selectedRecording;

  bool get isRecordingSelected => selectedRecording != null;

  void onDraftRecordingSelected(RecordingModel recording) {
    selectedRecording = recording;
    emit(ProfileDraftSelectionSelected(recording));
  }

  void onDraftRecordingUnselected() {
    selectedRecording = null;
    emit(ProfileDraftSelectionInitial());
  }
}
