import 'package:melodyze/core/generic_arch/base_repository.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/serivce/profile_service.dart';
import 'package:melodyze/modules/auth/model/melodyze_user.dart';

class ProfileRepo extends BaseRepo {
  final ProfileService profileService;
  ProfileRepo({required this.profileService});

  Future<RepoResult> getProfile() async {
    return await executeAndReturnResult(
      () => profileService.getProfile(),
      (json) async => RepoResult.success(MelodyzeUser.fromJson(json['data'] ?? {})),
    );
  }

  Future<RepoResult> getRecordings() async {
    return await executeAndReturnResult(
      () => profileService.getRecordings(),
      (Map<String, dynamic> json) async => RepoResult.success(
        ((json['data'] as Map<String, dynamic>)['recordings'] as List<dynamic>).map((dynamic e) => RecordingModel.fromJson(e as Map<String, dynamic>)).toList(),
      ),
    );
  }

  Future<RepoResult> deleteRecording(String recordingId) async {
    return await executeAndReturnResult(
      () => profileService.deleteRecording(recordingId),
      (json) async => RepoResult.success(json["data"] as Map<String, dynamic>),
    );
  }

  Future<RepoResult> deleteAccount() async {
    return await executeAndReturnResult(
      () => profileService.deleteAccount(),
      (json) async => RepoResult.success(json),
    );
  }

  Future<RepoResult> togglePublish(String recordingId, bool publish, String? videoS3path) async {
    return await executeAndReturnResult(
      () => profileService.togglePublish(recordingId, publish, videoS3path),
      (json) async => RepoResult.success(json),
    );
  }

  Future<RepoResult> updateProfilePhoto(String path) async {
    return await executeAndReturnResult(
      () => profileService.updateProfilePhoto(path),
      (json) async => RepoResult.success(json),
    );
  }

  Future<RepoResult> removeProfilePhoto() async {
    return await executeAndReturnResult(
      () => profileService.removeProfilePhoto(),
      (json) async => RepoResult.success(json),
    );
  }
}
