import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/helper/initial_tab_helper.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class ProfileRoutesPath {
  static const String profile = 'profile';
}

class ProfileRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: ProfileRoutesPath.profile,
          page: ProfileRoute.page,
          initial: InitialTabHelper.isInitialTab(2),
        ),
      ];
}
