
import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class ProfileDraftRecordingPlayerRoutesPath {
  static const String profileDraftRecordingPlayer = 'profileDraftRecordingPlayer';
}

class ProfileDraftRecordingPlayerRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: ProfileDraftRecordingPlayerRoutesPath.profileDraftRecordingPlayer,
          page: ProfileDraftRecordingPlayerRoute.page,
        ),
      ];
}
