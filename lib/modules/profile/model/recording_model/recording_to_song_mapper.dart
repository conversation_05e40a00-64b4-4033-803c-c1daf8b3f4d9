import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';

extension RecordingModelMapper on RecordingModel {
  SongModel toSongModel() {
    return SongModel(
      id: masterSongId,
      title: title,
      singer: singer,
      thumbnailPath: thumbnailPath,
      defaultGenre: genre,
    );
  }
}
