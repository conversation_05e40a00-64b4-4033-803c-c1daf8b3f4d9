import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';

part 'recording_model_list.freezed.dart';
part 'recording_model_list.g.dart';

@freezed
class RecordingModelList with _$RecordingModelList {
  factory RecordingModelList({
    @JsonKey(name: 'data') @Default([]) List<RecordingModel> data,
  }) = _RecordingModelList;

  factory RecordingModelList.fromJson(Map<String, dynamic> payload) => _$RecordingModelListFromJson(payload);
}
