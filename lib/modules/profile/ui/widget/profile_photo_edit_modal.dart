import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/image_utils.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';

class ProfilePhototureEditModal extends StatelessWidget {
  final String? currentImageUrl;
  final bool hasUserDp;

  const ProfilePhototureEditModal({
    super.key,
    this.currentImageUrl,
    required this.hasUserDp,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: AppGradientContainer(
        gradient: AppGradients.gradientBlackTeal,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.white50,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // Profile picture preview
            if (currentImageUrl != null) ...[
              SizedBox(
                height: 200,
                width: 200,
                child: ClipRRect(
                  child: ImageLoader.network(currentImageUrl!),
                ),
              ),
              const SizedBox(height: 20),
            ],
            const SizedBox(height: 24),

            // Options
            _buildOption(
              context,
              icon: Icons.photo_outlined,
              title: 'Choose photo',
              onTap: () => _updateProfilePhoto(context),
            ),

            ImageLoader.fromAsset(AssetPaths.gradientdivider),

            _buildOption(
              context,
              icon: Icons.delete_outlined,
              title: 'Delete photo',
              onTap: () => _removeProfilePhoto(context),
              isDestructive: true,
              enabled: hasUserDp,
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool enabled = true,
    bool isDestructive = false,
  }) {
    final baseColor = isDestructive ? Colors.red : AppColors.white;
    final color = enabled ? baseColor : baseColor.withValues(alpha: 0.4);

    return InkWell(
      onTap: enabled ? onTap : null, // disable tap when not enabled
      splashColor: enabled ? null : Colors.transparent,
      highlightColor: enabled ? null : Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.text18semiBold.copyWith(
                  color: color,
                  fontFamily: AppFonts.inter,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color.withValues(alpha: 0.5),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _updateProfilePhoto(BuildContext context) async {
    Navigator.of(context).pop();

    try {
      // Pick image from gallery
      final pickedFile = await ImageUtils.pickImageFromGallery();
      if (pickedFile == null) {
        return;
      }

      final processedImagePath = await ImageUtils.processProfilePhoto(pickedFile.path);
      if (processedImagePath == null) {
        if (context.mounted) {
          _showErrorSnackBar(context, 'Failed to process image');
        }
        return;
      }

      DI().resolve<ProfileBloc>().add(UpdateProfilePhotoEvent(processedImagePath));
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, 'Failed to update profile photo');
      }
    }
  }

  void _removeProfilePhoto(BuildContext context) {
    Navigator.of(context).pop();
    DI().resolve<ProfileBloc>().add(const RemoveProfilePhotoEvent());
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: AppTextStyles.text16medium,
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
