import 'dart:async';
import 'package:flutter/material.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/modules/profile/ui/widget/recording_popup_menu.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:just_audio/just_audio.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/ui/atom/marquee.dart';
import 'package:melodyze/core/ui/atom/app_slider.dart';
import 'package:melodyze/core/ui/molecules/auto_fade_widget.dart';

@RoutePage()
class ProfileDraftRecordingPlayerScreen extends StatefulWidget {
  final RecordingModel recording;

  const ProfileDraftRecordingPlayerScreen({
    super.key,
    required this.recording,
  });

  @override
  State<ProfileDraftRecordingPlayerScreen> createState() => _ProfileDraftRecordingPlayerScreenState();
}

class _ProfileDraftRecordingPlayerScreenState extends State<ProfileDraftRecordingPlayerScreen> {
  late AudioPlayer _audioPlayer;
  int _playbackPositionMillis = 0;
  int _audioDurationMillis = 0;
  bool _isPlaying = false;
  LyricsData? _lyricsData;
  bool _lyricsLoaded = false;

  String get songtitle => FileUtils.fromSnakeCase(widget.recording.title.split('-').first);
  String get info => "${Config.keyMapShort[widget.recording.scale] ?? widget.recording.scale} · ${widget.recording.tempo} bpm";
  String get genre => widget.recording.genre;
  String get singer => widget.recording.singer.isNotEmpty ? widget.recording.singer : ' ';
  bool get isPublishedRecording => widget.recording.isPublished;
  bool get showSongDetails => widget.recording.feedType != 'direct_upload';

  @override
  void initState() {
    super.initState();
    _initializeAudio();
    _fetchLyrics();
  }

  Future<void> _initializeAudio() async {
    _audioPlayer = AudioPlayer();
    await _audioPlayer.setLoopMode(LoopMode.one);
    await _audioPlayer.setUrl(widget.recording.finalMixedAudioPath);

    _audioPlayer.playerStateStream.listen((state) {
      if (mounted) {
        logger.d('Audio player state changed: playing=${state.playing}, processingState=${state.processingState}');
        setState(() {
          _isPlaying = state.playing;
        });
      }
    });

    _audioPlayer.positionStream.listen((position) {
      if (mounted) {
        setState(() {
          _playbackPositionMillis = position.inMilliseconds;
        });
      }
    });

    _audioPlayer.durationStream.listen((duration) {
      if (mounted && duration != null) {
        setState(() {
          _audioDurationMillis = duration.inMilliseconds;
        });
      }
    });

    unawaited(_audioPlayer.play());
  }

  Future<void> _fetchLyrics() async {
    if (_lyricsLoaded) return;

    try {
      if (widget.recording.lyricsJsonPath.isNotEmpty) {
        final response = await DI().resolve<ApiClient>().get(widget.recording.lyricsJsonPath);
        if (response != null) {
          final lyricsData = LyricsData.fromJson(response);
          if (mounted) {
            setState(() {
              _lyricsData = lyricsData;
              _lyricsLoaded = true;
            });
          }
          return;
        }
      }
    } catch (e) {
      logger.e('Failed to fetch lyrics: $e');
    }
  }

  void _togglePlay() {
    if (_isPlaying) {
      _audioPlayer.pause();
    } else {
      _audioPlayer.play();
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final lyricsList = _lyricsData?.lyrics.data.map((e) => e.text).toList() ?? [];

    // Get user profile information from ProfileBloc via DI
    final profileBloc = DI().resolve<ProfileBloc>();
    final user = profileBloc.melodyzeUser;
    final userProfileImageUrl = user.profilePicUrl ?? Config.noUserDP;
    final username = 'FT. ${user.username.split(' ').first}';

    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
      child: MeloScaffold(
        showBackground: false,
        showBackButton: true,
        extendBody: true,
        onBackPressed: () => Navigator.of(context).pop(),
        secondaryAction: (context) => RecordingPopupMenu(
          recording: widget.recording,
          isFromPlayer: true,
          onStop: () {
            unawaited(_audioPlayer.stop());
          },
        ),
        body: ColoredBox(
          color: Colors.black,
          child: Padding(
            padding: EdgeInsets.only(top: 50.0, bottom: 104.0),
            child: _lyricsLoaded
                ? _buildPlayerContent(context, lyricsList, userProfileImageUrl, username)
                : const Center(
                    child: AppCircularProgressIndicator(),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlayerContent(BuildContext context, List<String> lyrics, String userProfileImageUrl, String username) {
    return Stack(
      children: [
        // Main content - positioned naturally within the padded container
        Positioned.fill(
          child: Column(
            children: [
              // Header section with profile info and song metadata
              _buildHeaderSection(userProfileImageUrl, username),
              const SizedBox(height: 40),

              // Wave animation with centered play/pause button
              _buildWavePlayerSection(),

              // Lyrics section - positioned a bit lower
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(top: 50.0, bottom: 30.0),
                  child: _LyricsDisplay(
                    lyrics: lyrics,
                    lyricsData: _lyricsData?.lyrics,
                    playbackPositionMillis: _playbackPositionMillis,
                    audioDurationMillis: _audioDurationMillis,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Centered play/pause button overlay with fade animation
        Positioned.fill(
          child: AutoFadeWidget(
            key: ValueKey(_isPlaying), // Force rebuild when playing state changes
            stopAutoFadeOnInteraction: !_isPlaying,
            initiallyVisible: true,
            fadeOutDelay: const Duration(seconds: 3), // Slightly longer delay
            child: Center(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 250),
                transitionBuilder: (child, animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: child,
                  );
                },
                child: IconButton(
                  key: ValueKey(_isPlaying ? 'pause' : 'play'),
                  icon: ImageLoader.fromAsset(
                    _isPlaying ? AssetPaths.pauseReel : AssetPaths.playReel,
                  ),
                  onPressed: _togglePlay,
                ),
              ),
            ),
          ),
        ),

        // Song details positioned at bottom (above nav bar)
        Positioned(
          bottom: 28,
          left: 20,
          right: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showSongDetails)
                Row(
                  children: [
                    const SizedBox(width: 10),
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Image.asset(
                                AssetPaths.musicNote,
                                width: 16,
                                height: 22,
                                color: AppColors.white,
                              ),
                              const SizedBox(width: 6),
                              SizedBox(
                                width: MediaQuery.sizeOf(context).width * 0.5,
                                child: Marqueee(
                                  text: "$songtitle · ${widget.recording.genre}",
                                  style: AppTextStyles.text16regular.copyWith(
                                    fontFamily: AppFonts.inter,
                                  ),
                                  width: 250,
                                ),
                              ),
                            ],
                          ),
                          Text(
                            "${Config.keyMapShort[widget.recording.scale] ?? widget.recording.scale} · ${widget.recording.tempo} bpm",
                            style: AppTextStyles.text14regular.copyWith(fontFamily: AppFonts.iceland),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),

        // App slider positioned at bottom
        Positioned(
          bottom: 0,
          width: MediaQuery.sizeOf(context).width,
          child: AppSlider(
            value: _playbackPositionMillis.toDouble(),
            max: _audioDurationMillis > 0 ? _audioDurationMillis.toDouble() : 100.0,
            onChanged: (value) {
              _audioPlayer.seek(Duration(milliseconds: value.toInt()));
            },
            gestureHitAreaHeight: 12,
            activeColor: AppColors.white,
            isRounded: false,
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderSection(String userProfileImageUrl, String username) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(36.0, 65.0, 32.0, 28.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Profile image stack
          _buildProfileImage(userProfileImageUrl),
          const SizedBox(width: 20),
          // Recording info with song metadata
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        songtitle,
                        style: AppTextStyles.textEthnocentricStyle.copyWith(
                          fontSize: 14,
                          color: Colors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.clip,
                      ),
                    ),
                  ],
                ),
                if (singer.isNotEmpty)
                  Text(
                    singer,
                    style: AppTextStyles.text14regular.copyWith(
                      fontFamily: AppFonts.iceland,
                      color: Colors.grey[400],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 10),
                Text(
                  username,
                  style: AppTextStyles.text16regular.copyWith(
                    fontFamily: AppFonts.iceland,
                    color: Colors.grey[400],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileImage(String userProfileImageUrl) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        SizedBox(
          width: 95,
          height: 95,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                AssetPaths.profileBorder,
                fit: BoxFit.cover,
              ),
              ClipOval(
                child: SizedBox(
                  width: 90,
                  height: 90,
                  child: ImageLoader.network(
                    userProfileImageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[800],
                        child: const Icon(Icons.person, color: Colors.white, size: 48),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          right: -4, // Shift 4px more to the right
          bottom: -4, // Shift 4px more to the bottom
          child: SizedBox(
            width: 34,
            height: 34,
            child: Image.asset(
              AssetPaths.catComplete,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWavePlayerSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 6.0),
      child: SizedBox(
        height: 100,
        child: _isPlaying
            ? Image.asset(
                '${AssetPaths.gifPath}/wave_video.gif',
                fit: BoxFit.fill,
                width: double.infinity,
                height: 100,
              )
            : Image.asset(
                '${AssetPaths.pngPath}/wave_static.png',
                fit: BoxFit.fill,
                width: double.infinity,
                height: 100,
              ),
      ),
    );
  }
}

class _LyricsDisplay extends StatefulWidget {
  final List<String> lyrics;
  final LyricsList? lyricsData;
  final int playbackPositionMillis;
  final int audioDurationMillis;

  const _LyricsDisplay({
    required this.lyrics,
    required this.lyricsData,
    required this.playbackPositionMillis,
    required this.audioDurationMillis,
  });

  @override
  State<_LyricsDisplay> createState() => _LyricsDisplayState();
}

class _LyricsDisplayState extends State<_LyricsDisplay> {
  late ScrollController _scrollController;
  int _previousLyricIndex = 0;
  static const double _lyricItemHeight = 72.0; // Height of each lyric line

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  int _parseTimeToMillis(String time) {
    final parts = time.split(':');
    if (parts.length == 3) {
      final min = int.tryParse(parts[0]) ?? 0;
      final sec = int.tryParse(parts[1]) ?? 0;
      final ms = int.tryParse(parts[2]) ?? 0;
      return min * 60000 + sec * 1000 + ms;
    }
    return 0;
  }

  int _getCurrentLyricIndex() {
    if (widget.lyricsData == null || widget.lyrics.isEmpty) return 0;

    final data = widget.lyricsData!.data;
    if (data.isEmpty) return 0;

    // Handle looping: get position within the lyrics duration
    int adjustedPosition = widget.playbackPositionMillis;
    if (widget.audioDurationMillis > 0) {
      // Calculate lyrics duration
      final lastLyricEnd = data.isNotEmpty ? _parseTimeToMillis(data.last.endTime) : 0;
      final lyricsDuration = lastLyricEnd > 0 ? lastLyricEnd : widget.audioDurationMillis;

      // Get position within one loop cycle
      adjustedPosition = widget.playbackPositionMillis % lyricsDuration;
    }

    // Find current lyric based on timing
    for (int i = 0; i < data.length; i++) {
      final start = _parseTimeToMillis(data[i].startTime);
      final end = _parseTimeToMillis(data[i].endTime);

      if (end == 0) {
        // No end time specified, check if we're past start
        if (adjustedPosition >= start) {
          // Check if this is the last lyric or if next lyric hasn't started
          if (i == data.length - 1) {
            _scrollToLyric(i);
            return i;
          }
          final nextStart = _parseTimeToMillis(data[i + 1].startTime);
          if (adjustedPosition < nextStart) {
            _scrollToLyric(i);
            return i;
          }
        }
      } else {
        // End time specified
        if (adjustedPosition >= start && adjustedPosition < end) {
          _scrollToLyric(i);
          return i;
        }
      }
    }

    // Default fallback
    final defaultIndex = adjustedPosition < _parseTimeToMillis(data[0].startTime) ? 0 : data.length - 1;
    _scrollToLyric(defaultIndex);
    return defaultIndex;
  }

  void _scrollToLyric(int index) {
    if (_previousLyricIndex != index && _scrollController.hasClients) {
      _previousLyricIndex = index;

      // With our ListView structure:
      // Index 0: Empty placeholder
      // Index 1: First lyric (lyricIndex 0)
      // Index 2: Second lyric (lyricIndex 1)
      // ...
      // Index N+1: Last lyric (lyricIndex N-1)
      // Index N+2: Empty placeholder

      // To achieve the layout we want:
      // - Lyric 0: [Empty] [Current] [Next] -> scroll to position 0
      // - Lyric 1: [Previous] [Current] [Next] -> scroll to position 1*72 = 72
      // - Lyric 2: [Previous] [Current] [Next] -> scroll to position 2*72 = 144
      // - Last lyric: [Previous] [Current] [Empty] -> scroll to show it in middle

      double targetScrollOffset = index * _lyricItemHeight;

      // Ensure we don't scroll beyond bounds
      // Maximum scroll should show the last lyric in the middle position
      final maxScroll = (widget.lyrics.length - 1) * _lyricItemHeight;
      targetScrollOffset = targetScrollOffset.clamp(0.0, maxScroll);

      // Smooth scroll to the target position
      _scrollController.animateTo(
        targetScrollOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.lyrics.isEmpty) {
      return MediaQuery(
        data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
        child: Center(
          child: Text(
            'No lyrics available',
            style: AppTextStyles.text16regular.copyWith(
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    final currentIndex = _getCurrentLyricIndex();

    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
      child: SizedBox(
        height: _lyricItemHeight * 3, // Match exactly 3 lines
        width: 240,
        child: Stack(
          children: [
            // Clipped container to show only 3 lines
            ClipRect(
              child: SizedBox(
                height: _lyricItemHeight * 3, // Exactly 3 lines visible
                child: ListView.builder(
                  controller: _scrollController,
                  itemCount: widget.lyrics.length + 2, // Add 2 items: one empty at start, one at end
                  physics: const NeverScrollableScrollPhysics(), // Disable manual scrolling
                  padding: EdgeInsets.zero, // No padding, we'll control positioning manually
                  itemBuilder: (context, index) {
                    // First item: empty placeholder for proper spacing
                    if (index == 0) {
                      return SizedBox(
                        height: _lyricItemHeight,
                        child: const SizedBox.shrink(), // Empty space
                      );
                    }
                    // Last item: empty placeholder for proper spacing
                    else if (index == widget.lyrics.length + 1) {
                      return SizedBox(
                        height: _lyricItemHeight,
                        child: const SizedBox.shrink(), // Empty space
                      );
                    }
                    // Actual lyrics (adjusted index)
                    else {
                      final lyricIndex = index - 1;
                      return SizedBox(
                        height: _lyricItemHeight,
                        child: _buildLyricLine(
                          text: widget.lyrics[lyricIndex],
                          isCurrent: lyricIndex == currentIndex,
                          isVisible: true,
                        ),
                      );
                    }
                  },
                ),
              ),
            ),

            // Top backdrop mask - positioned over the top lyric line
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: _lyricItemHeight,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.9),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),

            // Bottom backdrop mask - positioned over the bottom lyric line
            Positioned(
              bottom: 50,
              left: 0,
              right: 0,
              height: _lyricItemHeight + 15,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.9),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLyricLine({
    required String text,
    required bool isCurrent,
    required bool isVisible,
  }) {
    if (!isVisible || text.isEmpty) {
      return const SizedBox(height: 72);
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: AppTextStyles.textEthnocentricStyle.copyWith(
            fontSize: isCurrent ? 14 : 10,
            color: isCurrent ? const Color(0xFFEBC0E8) : Colors.white,
            fontWeight: FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
