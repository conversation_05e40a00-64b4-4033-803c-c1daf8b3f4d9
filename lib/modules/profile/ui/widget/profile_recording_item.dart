import 'package:flutter/material.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/ui/atom/marquee.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/ui/widget/recording_popup_menu.dart';

enum RecordingType { draft, published }

class RecordingListItem extends StatelessWidget {
  final RecordingModel recording;
  final ValueChanged<RecordingModel>? onAction;
  final RecordingType type;

  const RecordingListItem({
    super.key,
    required this.recording,
    this.onAction,
    required this.type,
  });

  String get recordingInfo => '${recording.genre} · ${Config.keyMapShort[recording.scale] ?? recording.scale} · ${recording.tempo} bpm';
  String get title => FileUtils.fromSnakeCase(recording.title.split('-').first);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: SizedBox(
            height: 44,
            width: 44,
            child: recording.thumbnailPath.isEmpty
                ? Image.asset(
                    'assets/logo/logo.png',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: Colors.grey[800],
                      child: Icon(Icons.music_note, color: Colors.white, size: 24),
                    ),
                  )
                : ImageLoader.cachedNetworkImage(recording.thumbnailPath),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Marqueee(
                text: type == RecordingType.draft ? recordingInfo : title,
                style: AppTextStyles.text16semiBold.copyWith(
                  fontFamily: AppFonts.inter,
                ),
                width: 320,
              ),
              const SizedBox(height: 8),
              Marqueee(
                text: type == RecordingType.draft ? TimeUtils.formatEpochMilliseconds(recording.createdAt) : recordingInfo,
                style: AppTextStyles.text12regular.copyWith(
                  fontFamily: AppFonts.iceland,
                  color: AppColors.greySlider,
                ),
                width: 300,
              ),
            ],
          ),
        ),
        RecordingPopupMenu(
          recording: recording,
          onAction: onAction,
        ),
      ],
    );
  }
}