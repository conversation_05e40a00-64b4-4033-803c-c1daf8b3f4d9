import 'package:melodyze/core/generic_arch/base_repository.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/modules/feed/serivce/feed_service.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';

class FeedRepo extends BaseRepo {
  final FeedService feedService;
  FeedRepo({required this.feedService});

  Future<RepoResult> getPersonalizedFeed({
    String? pageName,
    String? clickedSource,
    String? masterSongId,
    String? genre,
    String? genreId,
    String? lang
  }) async {
    return await executeAndReturnResult(
      () => feedService.getPersonalizedFeed(
        pageName: pageName,
        clickedSource: clickedSource,
        masterSongId: masterSongId,
        genre: genre,
        genreId: genreId,
        lang: lang
      ),
      (json) async {
        final data = json['data'] as Map<String, dynamic>;
        final feeds = (data['feeds'] as List<dynamic>).map((e) => RecordingModel.fromJson(e)).toList();
        final noContent = data['no_content'] as bool? ?? false;

        return RepoResult.success({
          'feeds': feeds,
          'no_content': noContent,
          'master_song_id': masterSongId,
        });
      },
    );
  }
}
