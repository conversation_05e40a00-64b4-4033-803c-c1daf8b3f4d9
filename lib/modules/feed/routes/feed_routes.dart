import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/helper/initial_tab_helper.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class FeedRoutesPath {
  static const String feed = 'feed';
}

class FeedRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: FeedRoutesPath.feed,
          page: FeedRoute.page,
          initial: InitialTabHelper.isInitialTab(1),
        ),
      ];
}
