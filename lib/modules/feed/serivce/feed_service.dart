import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_arch/base_service.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class FeedService extends BaseService {
  Future<Map<String, dynamic>?> getPersonalizedFeed({
    String? pageName,
    String? clickedSource,
    String? masterSongId,
    String? genre,
    String? genreId,
    String? lang,
  }) async {
    return await DI().resolve<ApiClient>().post(
      Endpoints.feedsV2,
      body: {
        if (pageName != null) "page_name": pageName,
        if (clickedSource != null) "clicked_source": clickedSource,
        if (masterSongId != null) "master_song_id": masterSongId,
        if (genre != null) "genre": genre,
        if (genreId != null) "genre_id": genreId,
        if (lang != null) "lang": lang,
      },
    );
  }
}
