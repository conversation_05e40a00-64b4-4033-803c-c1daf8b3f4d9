import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class FeedSkeleton extends StatefulWidget {
  const FeedSkeleton({super.key});

  @override
  State<FeedSkeleton> createState() => _FeedSkeletonState();
}

class _FeedSkeletonState extends State<FeedSkeleton>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _pulseAnimation = Tween<double>(begin: 0.6, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_fadeAnimation, _pulseAnimation]),
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Scaffold(
            backgroundColor: Colors.black,
            body: Stack(
              children: [
                // Main video area skeleton
                Positioned.fill(
                  child: _buildVideoSkeleton(),
                ),
                // Top gradient overlay
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  height: 100,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withValues(alpha: 0.6),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                ),
                // Bottom gradient overlay
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: 200,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.8),
                        ],
                      ),
                    ),
                  ),
                ),
                // Right side controls skeleton
                Positioned(
                  right: 20,
                  bottom: 120,
                  child: _buildRightControlsSkeleton(),
                ),
                // Bottom song info skeleton
                Positioned(
                  bottom: 36,
                  left: 20,
                  right: 100,
                  child: _buildSongInfoSkeleton(),
                ),
                // Center play button skeleton
                Center(
                  child: AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        // child: _buildShimmerBox(
                        //   height: 80,
                        //   width: 80,
                        //   isCircular: true,
                        // ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildVideoSkeleton() {
    return _buildShimmerBox(
      height: double.infinity,
      width: double.infinity,
      borderRadius: 0,
    );
  }

  Widget _buildRightControlsSkeleton() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildShimmerBox(height: 48, width: 48, isCircular: true),
        const SizedBox(height: 8),
        _buildShimmerBox(height: 12, width: 24),
        const SizedBox(height: 24),
        _buildShimmerBox(height: 48, width: 48, isCircular: true),
        const SizedBox(height: 8),
        _buildShimmerBox(height: 12, width: 24),
        const SizedBox(height: 40),

        _buildShimmerBox(height: 36, width: 90, borderRadius: 24),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildSongInfoSkeleton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Song title
        _buildShimmerBox(height: 22, width: 180),
        const SizedBox(height: 8),
        // Artist name
        _buildShimmerBox(height: 16, width: 120),
        const SizedBox(height: 80),
      ],
    );
  }

  Widget _buildShimmerBox({
    required double height,
    required double width,
    double borderRadius = 4,
    bool isCircular = false,
  }) {
    return Shimmer.fromColors(
      baseColor: const Color.fromARGB(255, 21, 21, 21),
      highlightColor: const Color.fromARGB(255, 29, 29, 29),
      period: const Duration(milliseconds: 1200),
      direction: ShimmerDirection.ltr,
      child: Container(
        height: height,
        width: width == double.infinity ? null : width,
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 21, 21, 21),
          borderRadius: isCircular ? null : BorderRadius.circular(borderRadius),
          shape: isCircular ? BoxShape.circle : BoxShape.rectangle,
        ),
        child: width == double.infinity ? Container(width: double.infinity) : null,
      ),
    );
  }
}
