import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/feed/cubit/feed_cubit.dart';
import 'package:melodyze/modules/feed/widgets/feed_skeleton_loader.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/share/cubit/fcm_notification_data_cubit.dart';
import 'package:melodyze/modules/song/repo/song_repo.dart';
import 'package:melodyze/modules/song/service/song_service.dart';
import 'package:melodyze/modules/video_player/video_player_reel_screen.dart';

@RoutePage()
class FeedScreen extends StatefulWidget {
  final String? pageName;
  final String? clickedSource;
  final String? masterSongId;
  final String? genre;
  final String? genreId;
  final String? lang;

  const FeedScreen({
    super.key,
    this.pageName,
    this.clickedSource,
    this.masterSongId,
    this.genre,
    this.genreId,
    this.lang,
  });

  @override
  State<FeedScreen> createState() => _FeedScreenState();
}

class _FeedScreenState extends State<FeedScreen> {
  @override
  void initState() {
    super.initState();
    context.read<FeedCubit>().loadPersonalizedFeed(
          pageName: widget.pageName,
          clickedSource: widget.clickedSource,
          masterSongId: widget.masterSongId,
          genre: widget.genre,
          genreId: widget.genreId,
          lang: widget.lang,
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<FeedCubit, BlocState>(
      listener: (context, state) {
        if (state is NoContentState) {
          _handleNoContent(context, state.masterSongId);
        }
      },
      builder: (context, state) {
        if (state is LoadingState) {
          return const FeedSkeleton();
        }
        if (state is BlocFailureState) {
          return Center(child: Text(state.error.message));
        }
        if (state is BlocSuccessState) {
          return VideoPlayerReelScreen(
            recordings: state.data,
            showAppBar: false,
            showPersonalizationButton: true,
            isReelScreen: true,
            autoPlay: DI().resolve<FcmNotificationDataCubit>().notificationData == null,
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  void _handleNoContent(BuildContext context, String masterSongId) async {
    // Get the song details using the SongRepo
    final songRepo = SongRepo(songService: SongService());
    final response = await songRepo.getSongByMasterId(songId: masterSongId);

    if (response.isSuccess) {
      final song = response.data as SongModel;

      // Navigate to the song personalization screen

      if (context.mounted) {
        await context.pushRoute(
          SongPersonalizationRoute(
            song: song,
            defaultGenre: song.defaultGenre,
          ),
        );
      }
    }
  }
}
