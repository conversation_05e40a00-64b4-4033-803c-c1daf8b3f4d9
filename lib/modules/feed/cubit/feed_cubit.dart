import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/modules/feed/repo/feed_repo.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';

class FeedCubit extends Cubit<BlocState> {
  FeedRepo feedRepo;

  FeedCubit({required this.feedRepo}) : super(InitialState());

  void loadPersonalizedFeed({
    String? pageName,
    String? clickedSource,
    String? masterSongId,
    String? genre,
    String? genreId,
    String? lang
  }) async {
    emit(LoadingState());
    final response = await feedRepo.getPersonalizedFeed(
      pageName: pageName,
      clickedSource: clickedSource,
      masterSongId: masterSongId,
      genre: genre,
      genreId: genreId,   
      lang: lang,
    );

    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }

    final responseData = response.data as Map<String, dynamic>;
    final feeds = responseData['feeds'] as List<RecordingModel>;
    final noContent = responseData['no_content'] as bool;
    final songId = responseData['master_song_id'] as String? ?? '';

    if (noContent) {
      // Emit a state that will trigger redirection to song personalization screen
      emit(NoContentState(songId));
    } else {
      emit(BlocSuccessState<List<RecordingModel>>(feeds));
    }
  }
}
