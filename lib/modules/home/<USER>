import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/molecules/app_search_bar.dart';
import 'package:melodyze/core/ui/molecules/section_label.dart';
import 'package:melodyze/core/wrappers/analytics/user_event.dart';
import 'package:melodyze/modules/feed/model/feed_request_type.dart';
import 'package:melodyze/modules/home/<USER>/home_bloc.dart';
import 'package:melodyze/modules/home/<USER>/home_skeleton_loaders.dart';
import 'package:melodyze/modules/home/<USER>/home_grid_list/home_grid_list.dart';
import 'package:melodyze/modules/home/<USER>/home_tiles_list/home_tiles_list.dart';
import 'package:melodyze/modules/home/<USER>/fav_grid.dart';
import 'package:melodyze/modules/home/<USER>/tiles_widget.dart';

@RoutePage()
class HomeScreen extends StatelessWidget {
  HomeScreen({super.key});
  static const _yourFavourites = 'Your Favourites';

  final ValueNotifier<bool> _showSearchBar = ValueNotifier(true);
  final ScrollController _scrollController = ScrollController();

  void _handleScroll() {
    if (_scrollController.position.userScrollDirection == ScrollDirection.reverse) {
      _showSearchBar.value = false;
    } else if (_scrollController.position.userScrollDirection == ScrollDirection.forward) {
      _showSearchBar.value = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    _scrollController.addListener(_handleScroll);

    return SafeArea(
      bottom: false,
      child: Column(
        children: [
          ValueListenableBuilder<bool>(
            valueListenable: _showSearchBar,
            builder: (context, isVisible, child) {
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: isVisible ? 44 : 0,
                margin: EdgeInsets.only(top: isVisible ? 28 : 0),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 2),
                child: isVisible
                    ? InkWell(
                        onTap: () {
                          UserEvent.shared.home_search_clicked();
                          context.pushRoute(SearchRoute());
                        },
                        child: const IgnorePointer(child: AppSearchBar()),
                      )
                    : null,
              );
            },
          ),
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
                    child: BlocBuilder<HomeGridBloc, BlocState>(
                      builder: (context, state) {
                        if (state is LoadingState) {
                          return const Column(
                            children: [
                              SectionLabel(
                                sectionText: _yourFavourites,
                              ),
                              SizedBox(
                                height: 240,
                                width: double.infinity,
                                child: HomeGridSkeleton(),
                              ),
                            ],
                          );
                        }
                        if (state is BlocSuccessState<HomeGridList>) {
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const SectionLabel(
                                sectionText: _yourFavourites,
                              ),
                              const SizedBox(height: 16),
                              FavGrid(
                                homeGridList: state.data,
                                onItemTap: (song) {
                                  UserEvent.shared.home_grid_item_clicked("${song.genreName ?? song.artistName}");
                                  context.pushRoute(
                                    SearchRoute(
                                      gridId: song.id,
                                      title: song.artistName,
                                    ),
                                  );
                                },
                              ),
                            ],
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                  BlocBuilder<HomeTilesBloc, BlocState>(builder: (context, state) {
                    if (state is LoadingState) {
                      return const SizedBox(
                        height: 250,
                        width: double.infinity,
                        child: HomeTilesSkeleton(
                          padding: EdgeInsets.symmetric(horizontal: 20),
                        ),
                      );
                    }

                    if (state is BlocSuccessState<HomeTilesList>) {
                      return TilesWidget(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        homeTilesList: state.data,
                        onTap: (songModel, genre) {
                          context.pushRoute(
                            FeedRoute(
                              pageName: FeedPageSourceType.home.name,
                              clickedSource: FeedClickedSourceType.tile.name,
                              masterSongId: songModel.id,
                              genre: genre,
                              lang: songModel.lang
                            ),
                          );
                        },
                      );
                    }
                    return const SizedBox.shrink();
                  }),
                  const SizedBox(height: 108)
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
