import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_arch/base_service.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class HomeService extends BaseService {
  Future<Map<String, dynamic>?> getHomeFeedGrids() async {
    return await DI().resolve<ApiClient>().post(Endpoints.homeFeedGrids);
  }

  
  Future<Map<String, dynamic>?> getHomeFeedTiles() async {
    return await DI().resolve<ApiClient>().post(Endpoints.homeFeedTiles);
  }
}
