import 'package:freezed_annotation/freezed_annotation.dart';

part 'home_grid_model.freezed.dart';
part 'home_grid_model.g.dart';

@freezed
class HomeGridModel with _$HomeGridModel {
  factory HomeGridModel({
    // @<PERSON><PERSON><PERSON><PERSON>(name: 'genre_id') String? genreId,
    // @<PERSON><PERSON><PERSON><PERSON>(name: 'artist_id') String? artistId,
    @<PERSON><PERSON><PERSON><PERSON>(name: '_id') required String id,
    @JsonKey(name: 'grid_type') required String gridType,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'genre_name') String? genreName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'artist_name') String? artistName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active') @Default(false) bool isActive,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'thumbnail_path') @Default('') String thumbnailPath,
  }) = _HomeGridModel;

  factory HomeGridModel.fromJson(Map<String, dynamic> payload) => _$HomeGridModelFromJson(payload);
}
