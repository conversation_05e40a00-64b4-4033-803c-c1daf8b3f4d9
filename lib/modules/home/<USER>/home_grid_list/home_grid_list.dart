// write flutter freezed class for  List<HomeGridList> HomeGridList;

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/modules/home/<USER>/home_grid_list/home_grid_model/home_grid_model.dart';

part 'home_grid_list.freezed.dart';
part 'home_grid_list.g.dart';

@freezed
class HomeGridList with _$HomeGridList {
  factory HomeGridList({
    @JsonKey(name: 'data') @Default([]) List<HomeGridModel> data,
  }) = _HomeGridList;

  factory HomeGridList.fromJson(Map<String, dynamic> payload) => _$HomeGridListFromJson(payload);
}
