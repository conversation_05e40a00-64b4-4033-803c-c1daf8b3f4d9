import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/wrappers/analytics/user_event.dart';
import 'package:melodyze/modules/home/<USER>/home_grid_list/home_grid_list.dart';
import 'package:melodyze/modules/home/<USER>/home_tiles_list/home_tiles_list.dart';
import 'package:melodyze/modules/home/<USER>/home_repo.dart';

class HomeGridBloc extends SafeBloc {
  final HomeRepo homeRepo;
  HomeGridBloc({required this.homeRepo}) : super(InitialState()) {
    on<LoadDataEvent>(_loadData);
    add(LoadDataEvent());
  }

  FutureOr<void> _loadData(LoadDataEvent event, Emitter<BlocState> _) async {
    emit(LoadingState());
    UserEvent.shared.home_grid_api_called();
    final response = await homeRepo.getHomeFeedGrids();
    if (response.isSuccess) {
      UserEvent.shared.home_grid_api_success();
    } else {
      UserEvent.shared.home_grid_api_failed();
      emit(BlocFailureState(response.error));
      return;
    }
    emit(BlocSuccessState<HomeGridList>(response.data!));
  }
}

class HomeTilesBloc extends SafeBloc {
  final HomeRepo homeRepo;
  HomeTilesBloc({required this.homeRepo}) : super(InitialState()) {
    on<LoadDataEvent>(_loadData);
    add(LoadDataEvent());
  }

  FutureOr<void> _loadData(LoadDataEvent event, Emitter<BlocState> _) async {
    emit(LoadingState());
    final response = await homeRepo.getHomeFeedTiles();
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }
    emit(BlocSuccessState<HomeTilesList>(response.data!));
  }
}
