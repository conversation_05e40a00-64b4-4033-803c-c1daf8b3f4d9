import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class HomeGridSkeleton extends StatefulWidget {
  const HomeGridSkeleton({super.key});

  @override
  State<HomeGridSkeleton> createState() => _HomeGridSkeletonState();
}

class _HomeGridSkeletonState extends State<HomeGridSkeleton> with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // NEW: appear animation
  late AnimationController _appearController;
  late Animation<double> _appearOpacity;
  late Animation<double> _appearScale;

  @override
  void initState() {
    super.initState();

    // shimmer pulse (unchanged)
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.6, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _pulseController.repeat(reverse: true);

    // smooth ease-in on first appear
    _appearController = AnimationController(
      duration: const Duration(milliseconds: 420),
      vsync: this,
    );
    _appearOpacity = CurvedAnimation(
      parent: _appearController,
      curve: Curves.easeInOut,
    );
    _appearScale = Tween<double>(begin: 0.98, end: 1.0).animate(
      CurvedAnimation(parent: _appearController, curve: Curves.easeInOut),
    );

    // kick off appear animation after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) _appearController.forward();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _appearController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _appearOpacity,
      child: ScaleTransition(
        scale: _appearScale,
        child: GridView.builder(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 18,
            mainAxisSpacing: 18,
          ),
          itemCount: 6,
          itemBuilder: (context, index) {
            return TweenAnimationBuilder<double>(
              duration: Duration(milliseconds: 600 + (index * 100)),
              tween: Tween(begin: 0.0, end: 1.0),
              curve: Curves.easeOutCubic,
              builder: (context, value, child) {
                return Opacity(
                  opacity: value,
                  child: Transform.scale(
                    scale: 0.8 + (0.2 * value),
                    child: _buildGridShimmerTile(),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildGridShimmerTile() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 10),
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: AspectRatio(
                  aspectRatio: 1.2,
                  child: Shimmer.fromColors(
                    baseColor: Color.lerp(
                      const Color(0xFF2A2A2A),
                      const Color(0xFF404040),
                      _pulseAnimation.value,
                    )!,
                    highlightColor: Color.lerp(
                      const Color(0xFF505050),
                      const Color(0xFF606060),
                      _pulseAnimation.value,
                    )!,
                    period: const Duration(milliseconds: 1200),
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFF2A2A2A),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: const Color(0xFF333333),
                          width: 1,
                        ),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Color(0xFF2A2A2A),
                              Color(0xFF404040),
                              Color(0xFF2A2A2A),
                            ],
                            stops: [0.0, 0.5, 1.0],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF404040).withValues(alpha: 0.3),
                              blurRadius: 3,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 6),
            Shimmer.fromColors(
              baseColor: const Color(0xFF2A2A2A),
              highlightColor: const Color(0xFF404040),
              child: Container(
                height: 8,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2A2A),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: const Color(0xFF333333),
                    width: 0.5,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

class HomeTilesSkeleton extends StatefulWidget {
  final EdgeInsetsGeometry? padding;

  const HomeTilesSkeleton({
    super.key,
    this.padding,
  });

  @override
  State<HomeTilesSkeleton> createState() => _HomeTilesSkeletonState();
}

class _HomeTilesSkeletonState extends State<HomeTilesSkeleton> with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.6,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _pulseController.repeat(reverse: true);
    _slideController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 500),
      switchInCurve: Curves.easeInOut,
      switchOutCurve: Curves.easeInOut,
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: Tween<double>(begin: 0.95, end: 1.0).animate(animation),
            child: child,
          ),
        );
      },
      child: SlideTransition(
        key: const ValueKey("home-tiles-skeleton"),
        position: _slideAnimation,
        child: ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: 2, // Use 2 nos placeholder as requested
          itemBuilder: (context, index) {
            return TweenAnimationBuilder<double>(
              duration: Duration(milliseconds: 800 + (index * 200)),
              tween: Tween(begin: 0.0, end: 1.0),
              curve: Curves.easeInOut, // 🔑 eased
              builder: (context, value, child) {
                return Opacity(
                  opacity: value,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Section title shimmer
                            _buildShimmerBox(height: 22, width: 120),
                            // Explore more button shimmer
                            _buildShimmerBox(height: 18, width: 80),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20.0),
                      _buildHorizontalListSkeleton(),
                    ],
                  ),
                );
              },
            );
          },
          separatorBuilder: (context, index) => const SizedBox(height: 2),
        ),
      ),
    );
  }

  Widget _buildHorizontalListSkeleton() {
    return SizedBox(
      height: 232,
      child: ListView.separated(
        padding: widget.padding,
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        itemCount: 4, // Show 4 skeleton items
        itemBuilder: (BuildContext context, int index) {
          return TweenAnimationBuilder<double>(
            duration: Duration(milliseconds: 600 + (index * 150)),
            tween: Tween(begin: 0.0, end: 1.0),
            curve: Curves.easeOutCubic,
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(20 * (1 - value), 0),
                child: Opacity(
                  opacity: value,
                  child: _buildHorizontalShimmerTile(),
                ),
              );
            },
          );
        },
        separatorBuilder: (BuildContext context, int index) => const SizedBox(
          width: 18,
        ),
      ),
    );
  }

  Widget _buildHorizontalShimmerTile() {
    const width = 90.0;

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Image shimmer
            SizedBox(
              width: width,
              height: width * 16 / 9,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Shimmer.fromColors(
                  baseColor: Color.lerp(
                    const Color(0xFF2A2A2A),
                    const Color(0xFF404040),
                    _pulseAnimation.value,
                  )!,
                  highlightColor: Color.lerp(
                    const Color(0xFF505050),
                    const Color(0xFF606060),
                    _pulseAnimation.value,
                  )!,
                  period: const Duration(milliseconds: 1200),
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF2A2A2A),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: const Color(0xFF333333),
                        width: 1,
                      ),
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            const Color(0xFF2A2A2A),
                            const Color(0xFF404040),
                            const Color(0xFF2A2A2A),
                          ],
                          stops: const [0.0, 0.5, 1.0],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF404040).withValues(alpha: 0.3),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 10.0),
            // Title shimmer
            _buildShimmerBox(height: 10, width: width),
            const SizedBox(height: 6.0),
            // Singer shimmer
            _buildShimmerBox(height: 10, width: width * 0.7),
          ],
        );
      },
    );
  }

  Widget _buildShimmerBox({
    required double height,
    required double width,
    double borderRadius = 4,
  }) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Shimmer.fromColors(
          baseColor: Color.lerp(
            const Color(0xFF2A2A2A),
            const Color(0xFF404040),
            _pulseAnimation.value,
          )!,
          highlightColor: Color.lerp(
            const Color(0xFF505050),
            const Color(0xFF606060),
            _pulseAnimation.value,
          )!,
          child: Container(
            height: height,
            width: width,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF2A2A2A),
                  const Color(0xFF404040),
                  const Color(0xFF2A2A2A),
                ],
                stops: const [0.0, 0.5, 1.0],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: const Color(0xFF333333),
                width: 0.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF404040).withValues(alpha: 0.2),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
