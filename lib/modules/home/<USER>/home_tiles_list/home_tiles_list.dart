import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/modules/home/<USER>/home_tiles_list/home_tiles_model/home_tiles_model.dart';

part 'home_tiles_list.freezed.dart';
part 'home_tiles_list.g.dart';

@freezed
class HomeTilesList with _$HomeTilesList {
  factory HomeTilesList({
    @JsonKey(name: 'tiles') @Default([]) List<HomeTilesModel> data,
  }) = _HomeTilesList;

  factory HomeTilesList.fromJson(Map<String, dynamic> payload) => _$HomeTilesListFromJson(payload);
}
