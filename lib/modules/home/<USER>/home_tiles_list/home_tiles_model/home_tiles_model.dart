import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';

part 'home_tiles_model.freezed.dart';
part 'home_tiles_model.g.dart';

@freezed
class HomeTilesModel with _$HomeTilesModel {
  factory HomeTilesModel({
    @Json<PERSON>ey(name: '_id') required String id,
    @Json<PERSON>ey(name: 'tile_name') required String tileName,
    @JsonKey(name: 'thumbnail_path') @Default('') String thumbnailPath,
    @Json<PERSON>ey(name: 'song_preview_list') @Default([]) List<SongModel> songPreviewList,
  }) = _HomeTilesModel;

  factory HomeTilesModel.fromJson(Map<String, dynamic> payload) => _$HomeTilesModelFromJson(payload);
}
