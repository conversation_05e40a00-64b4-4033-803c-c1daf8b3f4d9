import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/molecules/network_image.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/modules/home/<USER>/home_grid_list/home_grid_list.dart';
import 'package:melodyze/modules/home/<USER>/home_grid_list/home_grid_model/home_grid_model.dart';

class FavGrid extends StatelessWidget {
  final HomeGridList homeGridList;
  final Function(HomeGridModel)? onItemTap;
  const FavGrid({
    super.key,
    required this.homeGridList,
    this.onItemTap,
  });

  static const _aspectRatio = 1.2;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 18,
        mainAxisSpacing: 18,
      ),
      itemCount: homeGridList.data.length,
      itemBuilder: (context, index) {
        final HomeGridModel item = homeGridList.data[index];
        return InkWell(
          onTap: () {
            onItemTap?.call(item);
          },
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                // Ensures it doesn't overflow
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: AspectRatio(
                    aspectRatio: _aspectRatio,
                    child: NetworkImageWidget(
                      imageUrl: item.thumbnailPath,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 2),
              Text(
                '${item.genreName ?? item.artistName}',
                style: AppTextStyles.text12regular.copyWith(
                  fontFamily: AppFonts.iceland,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                textScaler: TextScaler.noScaling,
              ),
            ],
          ),
        );
      },
    );
  }
}
