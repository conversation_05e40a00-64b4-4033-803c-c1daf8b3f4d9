import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/molecules/big_h_list.dart';
import 'package:melodyze/core/ui/molecules/butons/app_text_button.dart';
import 'package:melodyze/core/ui/molecules/section_label.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/wrappers/analytics/user_event.dart';
import 'package:melodyze/modules/home/<USER>/home_tiles_list/home_tiles_list.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';

class TilesWidget extends StatelessWidget {
  final HomeTilesList homeTilesList;
  final EdgeInsetsGeometry? padding;
  final Function(SongModel song, String? genre)? onTap;

  const TilesWidget({
    super.key,
    this.padding,
    required this.homeTilesList,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (context, index) {
        final item = homeTilesList.data[index];
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: padding ?? const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: SectionLabel(
                      sectionText: item.tileName,
                    ),
                  ),
                  Flexible(
                    child: AppTextButton(
                        text: 'Explore more',
                        style: AppTextStyles.text18regular.copyWith(
                          fontFamily: AppFonts.iceland,
                          color: AppColors.brightPurple,
                        ),
                        onPressed: () {
                          UserEvent.shared.home_tile_explore_clicked(item.tileName);
                          context.pushRoute(
                            SearchRoute(
                              tileId: item.id,
                              title: item.tileName,
                            ),
                          );
                        }),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16.0),
            BigHorizontalList(
              padding: padding,
              songs: item.songPreviewList,
              onTap: (song) {
                onTap?.call(song, item.tileName);
              },
            ),
          ],
        );
      },
      separatorBuilder: (context, index) => const SizedBox(height: 2),
      itemCount: homeTilesList.data.length,
    );
  }
}
