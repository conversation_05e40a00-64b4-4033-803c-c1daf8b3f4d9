import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/helper/initial_tab_helper.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class HomeRoutesPath {
  static const String home = 'home';
}

class HomeRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: HomeRoutesPath.home,
          page: HomeRoute.page,
          initial: InitialTabHelper.isInitialTab(0),
        ),
      ];
}
