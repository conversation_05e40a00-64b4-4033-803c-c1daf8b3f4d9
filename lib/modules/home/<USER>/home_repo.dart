import 'package:melodyze/core/generic_arch/base_repository.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/modules/home/<USER>/home_grid_list/home_grid_list.dart';
import 'package:melodyze/modules/home/<USER>/home_tiles_list/home_tiles_list.dart';
import 'package:melodyze/modules/home/<USER>/home_service.dart';

class HomeRepo extends BaseRepo {
  final HomeService homeService;

  HomeRepo({required this.homeService});

  Future<RepoResult> getHomeFeedGrids() async {
    return await executeAndReturnResult(
      () => homeService.getHomeFeedGrids(),
      (json) async => RepoResult.success(HomeGridList.fromJson(json)),
    );
  }

  Future<RepoResult> getHomeFeedTiles() async {
    return await executeAndReturnResult(
      () => homeService.getHomeFeedTiles(),
      (json) async => RepoResult.success(HomeTilesList.fromJson(json['data'])),
    );
  }
}
