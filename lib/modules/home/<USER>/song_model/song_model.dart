import 'package:freezed_annotation/freezed_annotation.dart';

part 'song_model.freezed.dart';
part 'song_model.g.dart';

@freezed
class SongModel with _$SongModel {
  factory SongModel({
    @<PERSON><PERSON><PERSON><PERSON>(name: '_id') required String id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'title') @Default('') String title,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'singer') @Default('') String singer,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'lang') String? lang,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'default_genre') @Default('') String defaultGenre,
    @<PERSON><PERSON><PERSON>ey(name: 'default_genre_id') @Default('') String defaultGenreId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'time_signature') @Default('') String timeSignature,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'thumbnail_path') @Default('') String thumbnailPath,
  }) = _SongModel;

  factory SongModel.fromJson(Map<String, dynamic> payload) => _$SongModelFromJson(payload);
}
