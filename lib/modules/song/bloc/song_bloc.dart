import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/song/repo/song_repo.dart';

class SongBloc extends SafeBloc {
  final String songId;
  final SongRepo songRepo;
  SongBloc({
    required this.songId,
    required this.songRepo,
  }) : super(InitialState()) {
    on<LoadDataEvent>(_onSongFetchEvent);
  }

  Future<void> _onSongFetchEvent(LoadDataEvent event, _) async {
    final response = await songRepo.getSongByMasterId(songId: songId);

    if (response.isSuccess) {
      emit(BlocSuccessState<SongModel>(response.data));
    } else {
      emit(BlocFailureState(RepoError(response.toString())));
    }
  }
}
