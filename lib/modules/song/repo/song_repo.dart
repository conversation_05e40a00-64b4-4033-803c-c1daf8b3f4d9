import 'dart:async';

import 'package:melodyze/core/generic_arch/base_repository.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/song/service/song_service.dart';

class SongRepo extends BaseRepo {
  final SongService songService;

  SongRepo({required this.songService});

  Future<RepoResult> getSongByMasterId({required String songId}) async {
    return await executeAndReturnResult(
      () => songService.getSongByMasterId(songId: songId),
      (json) async => RepoResult.success(SongModel.fromJson(json['data'])),
    );
  }
}
