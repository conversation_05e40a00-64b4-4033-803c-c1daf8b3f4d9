import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_arch/base_service.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class SongService extends BaseService {
  Future<Map<String, dynamic>?> getSongByMasterId({required String songId}) async {
    return await DI().resolve<ApiClient>().get(
          '${Endpoints.getMasterSongById}/$songId',
        );
  }
}
