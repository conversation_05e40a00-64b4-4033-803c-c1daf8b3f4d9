import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class SongPersonalizationRoutesPath {
  static const String songPersonalization = '/songPersonalization';
}

class SongPersonalizationRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: SongPersonalizationRoutesPath.songPersonalization,
          page: SongPersonalizationRoute.page,
        ),
      ];
}
