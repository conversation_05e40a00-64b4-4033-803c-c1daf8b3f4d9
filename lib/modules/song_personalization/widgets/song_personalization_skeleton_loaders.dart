import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class SongPersonalizationMainSkeleton extends StatelessWidget {
  const SongPersonalizationMainSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 500),
      switchInCurve: Curves.easeInOut,
      switchOutCurve: Curves.easeInOut,
      transitionBuilder: (child, animation) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: Tween<double>(begin: 0.95, end: 1.0).animate(animation),
            child: child,
          ),
        );
      },
      child: Column(
        key: const ValueKey("personalization-skeleton"),
        children: [
          // Top section - Lyrics viewer area
          Expanded(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Stack(
                children: [
                  const LyricsViewerSkeleton(),
                  const IgnorePointer(
                    child: Align(
                      alignment: Alignment.bottomCenter,
                      child: _BottomGradientSkeleton(),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Middle section - Player cards
          AspectRatio(
            aspectRatio: 16 / 9,
            child: _buildPlayerCardsSkeleton(),
          ),
          // Bottom section - Control buttons
          _buildBottomButtonsSkeleton(),
        ],
      ),
    );
  }

  Widget _buildPlayerCardsSkeleton() {
    return PageView.builder(
      scrollDirection: Axis.horizontal,
      controller: PageController(viewportFraction: 0.85),
      itemCount: 3,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: _buildPlayerCardSkeleton(),
        );
      },
    );
  }

  Widget _buildPlayerCardSkeleton() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      height: 220,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(36),
        color: const Color(0xFF1A1A1A),
        border: Border.all(color: const Color(0xFF333333), width: 3),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: Stack(
          children: [
            // Background
            Positioned.fill(
              child: _buildShimmerBox(
                height: double.infinity,
                width: double.infinity,
                borderRadius: 30,
              ),
            ),
            // Overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 18),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildShimmerBox(height: 26, width: 180), // Genre
                          const SizedBox(height: 4),
                          _buildShimmerBox(height: 16, width: 140), // Song + Artist
                        ],
                      ),
                    ),
                    const SizedBox(height: 6),

                    // Play button row
                    Row(
                      children: [
                        const SizedBox(width: 18),
                        _buildShimmerBox(height: 40, width: 40, isCircular: true),
                        const SizedBox(width: 14),
                        _buildShimmerBox(height: 2, width: 200),
                      ],
                    ),

                    // Repeated option rows
                    ...List.generate(3, (_) => _buildOptionRow()),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Reusable shimmer row for options
  Widget _buildOptionRow() {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        children: [
          const SizedBox(width: 76),
          _buildShimmerBox(height: 25, width: 66, borderRadius: 6),
          const SizedBox(width: 24),
          _buildShimmerBox(height: 25, width: 66, borderRadius: 6),
        ],
      ),
    );
  }

  Widget _buildBottomButtonsSkeleton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 38, vertical: 16),
      child: Row(
        children: [
          Flexible(
            flex: 25,
            child: _buildShimmerBox(height: 34, width: double.infinity, borderRadius: 28),
          ),
          const SizedBox(width: 16),
          Flexible(
            flex: 40,
            child: _buildShimmerBox(height: 34, width: double.infinity, borderRadius: 28),
          ),
        ],
      ),
    );
  }

  /// Generic shimmer box
  Widget _buildShimmerBox({
    required double height,
    required double width,
    double borderRadius = 4,
    bool isCircular = false,
  }) {
    return Shimmer.fromColors(
      baseColor: const Color(0xFF2A2A2A),
      highlightColor: const Color(0xFF404040),
      period: const Duration(milliseconds: 1200),
      direction: ShimmerDirection.ltr,
      child: Container(
        height: height,
        width: width == double.infinity ? null : width,
        decoration: BoxDecoration(
          color: const Color(0xFF2A2A2A),
          borderRadius: isCircular ? null : BorderRadius.circular(borderRadius),
          shape: isCircular ? BoxShape.circle : BoxShape.rectangle,
        ),
        child: width == double.infinity ? Container(width: double.infinity) : null,
      ),
    );
  }
}

class _BottomGradientSkeleton extends StatelessWidget {
  const _BottomGradientSkeleton();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withValues(alpha: 0.8),
          ],
        ),
      ),
    );
  }
}

class LyricsViewerSkeleton extends StatefulWidget {
  const LyricsViewerSkeleton({super.key});

  @override
  State<LyricsViewerSkeleton> createState() => _LyricsViewerSkeletonState();
}

class _LyricsViewerSkeletonState extends State<LyricsViewerSkeleton> with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _shimmerController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.6,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
    _shimmerController.repeat();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 500),
      switchInCurve: Curves.easeInOut,
      switchOutCurve: Curves.easeInOut,
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: Tween<double>(begin: 0.95, end: 1.0).animate(animation),
            child: child,
          ),
        );
      },
      child: Stack(
        key: const ValueKey("lyrics-skeleton"), // important for switching
        children: [
          LayoutBuilder(
            builder: (context, constraints) {
              return ListView.separated(
                padding: EdgeInsets.symmetric(
                  vertical: constraints.maxHeight / 2,
                ),
                itemCount: 15,
                separatorBuilder: (context, index) => const SizedBox(height: 24),
                itemBuilder: (context, index) {
                  return Center(
                    child: _buildLyricLineSkeleton(index),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLyricLineSkeleton(int index) {
    final widthFactors = [0.85, 0.6, 0.9, 0.7, 0.8, 0.65, 0.95, 0.75, 0.8, 0.6, 0.85, 0.7];
    final widthFactor = widthFactors[index % widthFactors.length];

    return Center(
      child: LayoutBuilder(
        builder: (context, constraints) {
          return TweenAnimationBuilder<double>(
            duration: Duration(milliseconds: 800 + (index * 100)),
            tween: Tween(begin: 0.0, end: 1.0),
            curve: Curves.easeOutCubic,
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Transform.scale(
                  scale: 0.8 + (0.2 * value),
                  child: _buildAnimatedShimmerBox(
                    height: 24,
                    width: constraints.maxWidth * widthFactor * 0.8,
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildAnimatedShimmerBox({
    required double height,
    required double width,
    double borderRadius = 4,
    bool isCircular = false,
  }) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Shimmer.fromColors(
          baseColor: Color.lerp(
            const Color(0xFF2A2A2A),
            const Color(0xFF404040),
            _pulseAnimation.value,
          )!,
          highlightColor: Color.lerp(
            const Color(0xFF505050),
            const Color(0xFF606060),
            _pulseAnimation.value,
          )!,
          period: const Duration(milliseconds: 1000),
          direction: ShimmerDirection.ltr,
          child: Container(
            height: height,
            width: width,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF2A2A2A),
                  const Color(0xFF404040),
                  const Color(0xFF2A2A2A),
                ],
                stops: const [0.0, 0.5, 1.0],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: isCircular ? null : BorderRadius.circular(borderRadius),
              shape: isCircular ? BoxShape.circle : BoxShape.rectangle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF404040).withValues(alpha: 0.3),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
