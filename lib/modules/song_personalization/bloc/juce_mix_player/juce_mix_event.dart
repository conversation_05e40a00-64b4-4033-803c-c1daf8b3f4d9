part of 'juce_mix_bloc.dart';

abstract class JuceMixEvent extends BaseEvent {
  const JuceMixEvent();
}

// Audio
class JuceMixAudioInitEvent extends JuceMixEvent {
  // final String annotationId;
  // final String songId;
  // final String audioPath;
  final bool autoPlay;

  const JuceMixAudioInitEvent({
    // required this.annotationId,
    // required this.songId,
    // required this.audioPath,
    this.autoPlay = false,
  });

  @override
  List<Object> get props => [autoPlay];
}

class JuceMixLoadEvent extends JuceMixEvent {
  @override
  List<Object> get props => [];
}

class JuceMixAudioPlayEvent extends JuceMixEvent {
  @override
  List<Object> get props => [];
}

class JuceMixAudioPauseEvent extends JuceMixEvent {
  @override
  List<Object> get props => [];
}

class JuceMixAudioTogglePlayPauseEvent extends Ju<PERSON><PERSON>ixEvent {
  @override
  List<Object> get props => [];
}

class JuceMixAudioStopEvent extends Juce<PERSON>ixEvent {
  @override
  List<Object> get props => [];
}

class JuceMixAudioProgressUpdateEvent extends JuceMixEvent {
  final Duration position;
  const JuceMixAudioProgressUpdateEvent({required this.position});

  @override
  List<Object> get props => [position];
}

class JuceMixAudioSeekEvent extends JuceMixEvent {
  final double milliseconds;
  const JuceMixAudioSeekEvent({required this.milliseconds});

  @override
  List<Object> get props => [milliseconds];
}

class JuceMixToggleMetronomeEvent extends JuceMixEvent {
  final bool enable;
  final double volume;
  final bool showToast;
  const JuceMixToggleMetronomeEvent({
    required this.enable,
    required this.volume,
    this.showToast = true,
  });

  @override
  List<Object> get props => [enable, volume];
}

class JuceMixToggleGuideEvent extends JuceMixEvent {
  final bool enable;
  final double volume;
  final bool showToast;
  const JuceMixToggleGuideEvent({
    required this.enable,
    required this.volume,
    this.showToast = true,
  });
  @override
  List<Object> get props => [enable, volume];
}

// Recorder
class JuceMixRecordInitEvent extends JuceMixEvent {
  final String recordingFilePath;

  const JuceMixRecordInitEvent({
    required this.recordingFilePath,
  });

  @override
  List<Object> get props => [recordingFilePath];
}

class JuceMixRecordStartEvent extends JuceMixEvent {
  @override
  List<Object> get props => [];
}

class JuceMixRecordStopEvent extends JuceMixEvent {
  @override
  List<Object> get props => [];
}

class JuceMixRecord extends JuceMixEvent {
  @override
  List<Object> get props => [];
}

class JuceSetVocalFilterEvent extends JuceMixEvent {
  final String vocalFilterId;
  const JuceSetVocalFilterEvent({required this.vocalFilterId});

  @override
  List<Object> get props => [vocalFilterId];
}

class JuceSetMasterFilterEvent extends JuceMixEvent {
  final String masterFilterId;
  const JuceSetMasterFilterEvent({required this.masterFilterId});

  @override
  List<Object> get props => [masterFilterId];
}
