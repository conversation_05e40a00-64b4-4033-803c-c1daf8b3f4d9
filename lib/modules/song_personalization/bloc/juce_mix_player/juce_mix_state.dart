part of 'juce_mix_bloc.dart';

abstract class <PERSON>ceMixState extends BlocState {
  const JuceMixState();
}

//  Commons
class JuceMixInit extends JuceMixState {
  @override
  List<Object> get props => [];
}

// class JuceMixLoading extends JuceMixState {
//   @override
//   List<Object> get props => [];
// }

// class JuceMixLoaded extends JuceMixState {
//   @override
//   List<Object> get props => [];
// }

class JuceMixError extends JuceMixState {
  final Object error;
  const JuceMixError({required this.error});

  @override
  List<Object> get props => [];
}

// Audio
class JuceMixAudioLoading extends JuceMixState {
  @override
  List<Object> get props => [];
}

class JuceMixAudioLoaded extends JuceMixState {
  @override
  List<Object> get props => [];
}

class JuceMixAudioPlaying extends JuceMixState {
  @override
  List<Object> get props => [];
}

class JuceMixAudioPaused extends JuceMixState {
  @override
  List<Object> get props => [];
}

class JuceMixAudioStopped extends JuceMixState {
  @override
  List<Object> get props => [];
}

class JuceMixAudioProgressUpdated extends JuceMixState {
  final double position;
  const JuceMixAudioProgressUpdated({required this.position});

  @override
  List<Object> get props => [position];
}

class JuceMixAudioError extends JuceMixState {
  final Object error;
  const JuceMixAudioError({required this.error});

  @override
  List<Object> get props => [];
}

//
class JuceMixRecordPreparing extends JuceMixState {
  @override
  List<Object> get props => [];
}

class JuceMixRecordPrepared extends JuceMixState {
  @override
  List<Object> get props => [];
}

class JuceMixRecordStarted extends JuceMixState {
  @override
  List<Object> get props => [];
}

class JuceMixRecordProgressUpdateState extends JuceMixState {
  final int progressInMillis;
  const JuceMixRecordProgressUpdateState({required this.progressInMillis});

  @override
  List<Object> get props => [progressInMillis];
}

class JuceMixRecordLevelUpdateState extends JuceMixState {
  final double level;
  const JuceMixRecordLevelUpdateState({required this.level});

  @override
  List<Object> get props => [level];
}

class JuceMixRecordInProgress extends JuceMixState {
  @override
  List<Object> get props => [];
}

class JuceMixRecordStopped extends JuceMixState {
  @override
  List<Object> get props => [];
}

class JuceMixRecordError extends JuceMixState {
  final Object error;
  const JuceMixRecordError({required this.error});

  @override
  List<Object> get props => [];
}
