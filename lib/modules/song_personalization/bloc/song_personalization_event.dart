import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/modules/song_personalization/bloc/audio_player_bloc/audio_player_bloc.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';

class ChangeSettingsEvent extends BaseEvent {
  final String? scale;
  final String? tempo;
  final String? genre;
  final AudioPlaySource source;

  const ChangeSettingsEvent({this.scale, this.tempo, this.genre, required this.source});

  @override
  List<Object?> get props => [scale, tempo, genre, source];
}

class ResetSettingsEvent extends BaseEvent {
  final String genre;
  const ResetSettingsEvent({required this.genre});

  @override
  List<Object?> get props => [genre];
}

// New event for download completion
class DownloadAudioFilesEvent extends BaseEvent {
  final AnnotatedData annotatedData;
  const DownloadAudioFilesEvent({required this.annotatedData});
  
  @override
  List<Object?> get props => [annotatedData];
}
