import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/ui/utilities/screen_utils.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/song_personalization/bloc/audio_player_bloc/audio_player_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/song_personalization_event.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';
import 'package:melodyze/modules/song_personalization/repo/song_personalization_repo.dart';



class SongPersonalizationBloc extends SafeBloc {
  static const tag = 'SongPersonalizationBloc';
  final SongModel song;
  final SongPersonalizationRepo songPersonalizationRepo;
  final AudioPlayerBloc audioPlayerBloc;
  final LyricsViewerBloc lyricsViewerBloc;
  late final List<AnnotatedData> allAnnotatedData;
  late final List<String> scales;
  late final List<List<String>> tempos;
  late final List<AnnotatedData> _generedSongs;
  final List<AnnotatedData> _defaultGeneredSongs = [];
  int currentGenreIndex = 0;
  AnnotatedData? currentAnnotatedData;
  final String? defaultGenre;

  SongPersonalizationBloc({
    required this.song,
    required this.songPersonalizationRepo,
    required this.audioPlayerBloc,
    required this.lyricsViewerBloc,
    this.defaultGenre,
  }) : super(InitialState()) {
    ScreenUtils.keepScreenOn();

    on<LoadSongEvent>(_loadSong);
    on<ChangeSettingsEvent>(_changeSettings);
    add(LoadSongEvent());
  }

  @override
  Future<void> close() {
    ScreenUtils.keepScreenOff();
    return super.close();
  }

  /// Load annotated data for the given song
  ///
  /// This method will first load all annotated data for the given song.
  /// Then it will filter the annotated data which has isDefault = true and
  /// add them to _defaultGeneredSongs list.
  /// After that, it will get the current selected genre index by
  /// comparing the default genre of the song with the genre of the
  /// annotated data.
  /// Finally, it will sort the scales and tempos list and initialize
  /// the _generedSongs list with the _defaultGeneredSongs list.
  /// The method will emit the BlocSuccessState with the _generedSongs list
  /// if the operation is successful.
  ///
  /// [event] is the event that triggered this method
  FutureOr<void> _loadSong(LoadSongEvent event, _) async {
    emit(LoadingState());
    final response = await songPersonalizationRepo.getAllAnnotation(songId: song.id);
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }
    try {
      allAnnotatedData = response.data;
      final Set<String> scalesSet = {};
      final Map<String, Set<String>> temposMap = {};

      for (final annotatedData in allAnnotatedData) {
        if (annotatedData.isDefault) {
          _defaultGeneredSongs.add(annotatedData);
          if ((defaultGenre ?? song.defaultGenre).toLowerCase() == annotatedData.genre.toLowerCase()) {
            currentGenreIndex = _defaultGeneredSongs.length - 1;
          }
        }
        scalesSet.add(annotatedData.scale);
        temposMap.putIfAbsent(annotatedData.genreId, () => {}).add(annotatedData.tempo);
      }

      scales = scalesSet.toList()..sort();
      tempos = temposMap.values.map((list) => list.toList()..sort((a, b) => int.parse(a).compareTo(int.parse(b)))).toList();
      _generedSongs = [..._defaultGeneredSongs];
      _updateAudioAndLyrics(_generedSongs[currentGenreIndex], AudioPlaySource.def, autoPlay: true);
      emit(BlocSuccessState<List<AnnotatedData>>(_generedSongs));
    } catch (e, s) {
      emit(BlocFailureState(RepoError(e.toString())));
      logger.e(tag, error: e, stackTrace: s, fatal: true);
    }
  }

  FutureOr<void> _changeSettings(ChangeSettingsEvent event, Emitter<BlocState> _) async {
    emit(LoadingState());
    try {
      final currentAnnotatedData = _generedSongs[currentGenreIndex];
      final scale = event.scale ?? currentAnnotatedData.scale;
      final tempo = event.tempo ?? currentAnnotatedData.tempo;
      final genre = event.genre ?? currentAnnotatedData.genre;
      final filteredAnnotatedData = allAnnotatedData.where((element) => scale == element.scale && tempo == element.tempo && genre == element.genre);
      
      if (filteredAnnotatedData.isNotEmpty) {
        final AnnotatedData annotatedData = filteredAnnotatedData.first;
        _generedSongs[currentGenreIndex] = annotatedData;
        _updateAudioAndLyrics(annotatedData, event.source, autoPlay: false);
        emit(BlocSuccessState<List<AnnotatedData>>(_generedSongs));
      } else {
        unawaited(DI().resolve<AppToast>().showToast('Song is not available for this combination'));
        emit(BlocSuccessState<List<AnnotatedData>>(_generedSongs));
      }
    } catch (e, s) {
      emit(BlocFailureState(RepoError(e.toString())));
      logger.e(tag, error: e, stackTrace: s, fatal: true);
    }
  }

  void _updateAudioAndLyrics(AnnotatedData annotatedData, AudioPlaySource source, {bool autoPlay = false}) {
    currentAnnotatedData = annotatedData;

    audioPlayerBloc.setPosition(Duration(milliseconds: 0));

    audioPlayerBloc.add(AudioPlayerLoadEvent(
      song: song,
      type: AudioType.url,
      autoPlay: autoPlay,
      annotatedData: annotatedData,
    ));
    if (source != AudioPlaySource.scaleKnob) {
      lyricsViewerBloc.setLyricsPath(annotatedData.lyricsTimelineFilePath);
    }
  }

  void changeGenreByIndex(int index) {
    currentGenreIndex = index;
    add(ChangeSettingsEvent(genre: _generedSongs[index].genre, source: AudioPlaySource.genreSlider));
  }
}

class LyricsViewerBloc extends SafeBloc {
  String? _lyricsPath;
  LyricsData? lyricsData;
  final SongPersonalizationRepo songPersonalizationRepo;

  LyricsViewerBloc({
    required this.songPersonalizationRepo,
  }) : super(InitialState()) {
    on<LoadLyricsEvent>(_loadLyrics);
    add(LoadLyricsEvent());
  }

  FutureOr<void> _loadLyrics(LoadLyricsEvent event, Emitter<BlocState> _) async {
    if (_lyricsPath.isNullOrEmpty) {
      emit(EmptyDataState());
      return;
    }
    emit(LoadingState());
    final response = await songPersonalizationRepo.getLyrics(lyricsPath: _lyricsPath!);
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }
    lyricsData = response.data;
    emit(BlocSuccessState<LyricsData>(response.data));
  }

  void setLyricsPath(String newLyricsPath) {
    _lyricsPath = newLyricsPath;
    add(LoadLyricsEvent());
  }
}
