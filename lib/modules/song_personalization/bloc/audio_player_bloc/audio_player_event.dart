part of 'audio_player_bloc.dart';

enum AudioType { url, file }

enum AudioPlaySource { def, scaleKnob, tempoKnob, genreSlider }

abstract class AudioPlayerEvent extends BaseEvent {
  const AudioPlayerEvent();
}

class AudioPlayerLoadEvent extends AudioPlayerEvent {
  final AudioType type;
  final bool autoPlay;
  final AnnotatedData annotatedData;
  final SongModel song;

  const AudioPlayerLoadEvent({
    required this.song,
    required this.type,
    this.autoPlay = false,
    required this.annotatedData,
  });

  @override
  List<Object> get props => [annotatedData, song];
}

class AudioPlayerPlayEvent extends AudioPlayerEvent {
  @override
  List<Object> get props => [];
}

class AudioPlayerPauseEvent extends AudioPlayerEvent {
  @override
  List<Object> get props => [];
}

class AudioPlayerStopEvent extends AudioPlayerEvent {
  @override
  List<Object> get props => [];
}

class AudioPlayerProgressUpdateEvent extends AudioPlayerEvent {
  final Duration position;
  const AudioPlayerProgressUpdateEvent({required this.position});

  @override
  List<Object> get props => [position];
}

class AudioPlayerSeekEvent extends AudioPlayerEvent {
  final double milliseconds;
  const AudioPlayerSeekEvent({required this.milliseconds});

  @override
  List<Object> get props => [milliseconds];
}

class ToggleMetronomeEvent extends AudioPlayerEvent {
  final bool enable;
  final double volume;
  final bool showToast;
  const ToggleMetronomeEvent({
    required this.enable,
    required this.volume,
    this.showToast = true,
  });

  @override
  List<Object> get props => [enable, volume];
}

class ToggleGuideEvent extends AudioPlayerEvent {
  final bool enable;
  final double volume;
  final bool showToast;
  const ToggleGuideEvent({
    required this.enable,
    required this.volume,
    this.showToast = true,
  });
  @override
  List<Object> get props => [enable, volume];
}

class DownloadFilesEvent extends AudioPlayerEvent {
  final AnnotatedData annotatedData;
  final String timeSign;
  final double duration;

  const DownloadFilesEvent({
    required this.annotatedData,
    required this.timeSign,
    required this.duration,
  });
  @override
  List<Object> get props => [annotatedData, timeSign, duration];
}

class PrepareMixerEvent extends AudioPlayerEvent {
  final AnnotatedData annotatedData;
  final String timeSign;
  final double duration;

  const PrepareMixerEvent({
    required this.annotatedData,
    required this.timeSign,
    required this.duration,
  });
  @override
  List<Object> get props => [annotatedData, timeSign, duration];
}




// class EnableMetronomeEvent extends AudioPlayerEvent {
//   final String bgmUrl;
//   final String timeSignature;
//   final int tempo;
//   final double metVol;

//   const EnableMetronomeEvent({
//     required this.bgmUrl,
//     required this.timeSignature,
//     required this.tempo,
//     this.metVol = 1.0,
//   });

//   @override
//   List<Object?> get props => [bgmUrl, timeSignature, tempo, metVol];
// }

// class DisableMetronomeEvent extends AudioPlayerEvent {
//   @override
//   List<Object?> get props => [];
// }

// class EnableGuideTrackEvent extends AudioPlayerEvent {
//   final String guideUrl;
//   final String bgmUrl;
//   final double guideVol;

//   const EnableGuideTrackEvent({
//     required this.guideUrl,
//     required this.bgmUrl,
//     this.guideVol = 1.0,
//   });

//   @override
//   List<Object?> get props => [guideUrl, bgmUrl, guideVol];
// }

// class DisableGuideTrackEvent extends AudioPlayerEvent {
//   @override
//   List<Object?> get props => [];
// }
