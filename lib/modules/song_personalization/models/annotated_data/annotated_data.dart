import 'package:freezed_annotation/freezed_annotation.dart';

part 'annotated_data.freezed.dart'; // Auto-generated file
part 'annotated_data.g.dart'; // Auto-generated file

@freezed
class AnnotatedData with _$AnnotatedData {
  const factory AnnotatedData({
    @<PERSON><PERSON><PERSON><PERSON>(name: '_id') required String id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'master_song_id') required String masterSongId,
    @<PERSON>son<PERSON>ey(name: 'lyrics_timeline_file_path') required String lyricsTimelineFilePath,
    required String genre,
    @JsonKey(name: 'genre_id') required String genreId,
    required String scale,
    required String tempo,
    @Json<PERSON>ey(name: 'song_path') required String songPath,
    @Json<PERSON>ey(name: 'guide_vocal_path') String? guideVocalPath,
    @<PERSON><PERSON><PERSON>ey(name: 'is_default') @Default(false) bool isDefault,
  }) = _AnnotatedData;

  factory AnnotatedData.fromJson(Map<String, dynamic> json) => _$AnnotatedDataFromJson(json);
}
