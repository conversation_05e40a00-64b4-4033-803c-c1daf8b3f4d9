import 'dart:async';

import 'package:melodyze/core/generic_arch/base_repository.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';
import 'package:melodyze/modules/song_personalization/service/song_personalization_service.dart';

class SongPersonalizationRepo extends BaseRepo {
  final SongPersonalizationService songPersonalizationService;

  SongPersonalizationRepo({required this.songPersonalizationService});

  Future<RepoResult> getAllAnnotation({required String songId}) async {
    return await executeAndReturnResult(
      () => songPersonalizationService.getAllAnnotation(songId: songId),
      (json) async =>
          RepoResult.success((json['data'] as List<dynamic>).map((e) => AnnotatedData.fromJson(e)).toList()),
    );
  }

  
  Future<RepoResult> getLyrics({required String lyricsPath}) async {
    return await executeAndReturnResult(
      () => songPersonalizationService.getLyrics(lyricsPath: lyricsPath),
      (json) async => RepoResult.success(LyricsData.fromJson(json)),
    );
  }
}
