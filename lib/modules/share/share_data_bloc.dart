import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';

class ShareDataBloc extends SafeBloc<ShareDataEvent, ShareDataState> {
  AnnotatedData? annotatedData;
  SongModel? song;
  String? downloadedGuidePath;
  String? downloadedBgmPath;
  String? inputMic;

  ShareDataBloc() : super(ShareDataState()) {
    on<ShareDataEvent>(((event, _) {
      annotatedData = event.annotatedData ?? annotatedData;
      song = event.song ?? song;
      downloadedGuidePath = event.guidePath ?? downloadedGuidePath;
      downloadedBgmPath = event.bgmPath ?? downloadedBgmPath;
      inputMic = event.inputMic ?? inputMic;
      emit(ShareDataState());
    }));
  }
}

class ShareDataState extends BlocState {
  @override
  List<Object?> get props => [];
}

class ShareDataEvent extends BaseEvent {
  final AnnotatedData? annotatedData;
  final SongModel? song;
  final String? guidePath;
  final String? bgmPath;
  final String? inputMic;

  const ShareDataEvent({
    this.annotatedData,
    this.song,
    this.guidePath,
    this.bgmPath,
    this.inputMic,
  });

  @override
  List<Object?> get props => [annotatedData, song];
}
