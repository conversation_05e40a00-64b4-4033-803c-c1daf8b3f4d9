import 'package:flutter_bloc/flutter_bloc.dart';

class FcmNotificationDataCubit extends Cubit<Map<String, dynamic>?> {
  FcmNotificationDataCubit() : super(null);
  Map<String, dynamic>? notificationData;

  void setNotificationData(Map<String, dynamic> data) {
    notificationData = data;
    emit(data);
  }

  void setNotificationDataWithoutEmit(Map<String, dynamic> data) {
    notificationData = data;
  }

  void clearNotificationData() {
    notificationData = null;
    emit(null);
  }
}
