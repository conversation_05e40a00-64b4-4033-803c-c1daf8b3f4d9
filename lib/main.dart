import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/utils_base.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';
import 'package:melodyze/modules/auth/bloc/authentication_bloc.dart';
import 'package:melodyze/modules/share/cubit/fcm_notification_data_cubit.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/core/ui/widgets/splash_screen_video.dart';
import 'package:melodyze/core/services/screenshot_manager.dart';

void main() async {
  JuceKitWrapper().enableLogs(kDebugMode);
  await initialiseConfig();
  await initialiseDependencies();
  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle.dark.copyWith(
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
    ),
  );
  await DI().resolve<FirstLoginHelper>().fetchFirstTime();
  await ScreenshotManager.instance.initialize();
  runApp(MelodyzeApp());

  // initilize the vocal/master filters
  final vocalConfigPath = await AssetPaths.extractAsset('assets/juce_configs/config.json');
  final masterConfigPath = await AssetPaths.extractAsset('assets/juce_configs/config_master.json');
  JuceKitWrapper.shared.initJuceMixPlayerAudioFilters(vocalConfigPath, masterConfigPath);
}

// class TestApp extends StatelessWidget {
//   const TestApp({super.key});
//   @override
//   Widget build(BuildContext context) {
//     return const MaterialApp(
//       home: Scaffold(
//         body: Center(
//           child:
//           VocalFiltersScreen(
//             outputFilePath: "s3://user-final-recording/raw_vocal_audio/66d08d966b6469b29027df7d/Rolling_android_8u6YGE_1725164259042.wav",
//             songPath: "s3://annotated-songs/66d044cf163911f63e58f7d4/Funk_1724925135/66d044cf163911f63e58f7d5_98_A.mp3",
//             isAudioOnly: true,
//           ),
//         ),
//       ),
//     );
//   }
// }

class MelodyzeApp extends StatelessWidget {
  const MelodyzeApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: DI().resolve<AuthenticationBloc>()),
        BlocProvider(create: (context) => ShareDataBloc()),
        BlocProvider<FcmNotificationDataCubit>(create: (context) => DI().resolve<FcmNotificationDataCubit>()),
      ],
      child: MaterialApp.router(
        debugShowCheckedModeBanner: false,
        routerConfig: DI().resolve<AppRouter>().config(),
        theme: ThemeData(
          textTheme: Theme.of(context).textTheme.apply(
                fontFamily: GoogleFonts.outfit().fontFamily,
                bodyColor: AppColors.white,
                displayColor: AppColors.white,
              ),
          colorScheme: ColorScheme.fromSeed(
            seedColor: AppColors.black010101,
            primary: AppColors.primary100,
            surface: AppColors.black010101,
          ),
          scaffoldBackgroundColor: AppColors.black010101,
          useMaterial3: true,
          dialogTheme: DialogTheme(
            backgroundColor: AppColors.black010101,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(14.0),
            ),
            contentTextStyle: AppTextStyles.text18regular.copyWith(color: AppColors.white),
          ),
        ),
        builder: (context, child) {
          return SplashScreenVideo(child: child);
        },
      ),
    );
  }
}
