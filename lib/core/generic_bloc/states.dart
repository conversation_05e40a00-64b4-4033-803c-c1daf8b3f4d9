import 'package:equatable/equatable.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';

abstract class BlocState extends Equatable {
  const BlocState();
}

class LoadingState extends BlocState {
  @override
  List<Object?> get props => [];
}

class InitialState extends BlocState {
  @override
  List<Object?> get props => [];
}

class LoadingMoreState extends BlocState {
  const LoadingMoreState();

  @override
  List<Object?> get props => [];
}

class BlocSuccessState<T> extends BlocState {
  final T data;

  const BlocSuccessState(
    this.data,
  );

  @override
  List<Object?> get props => [data];
}

class BlocFailureState extends BlocState {
  final RepoError error;

  BlocFailureState(this.error) {
    try {
      DI().resolve<AppToast>().showToast(error.message);
    } catch (e) {
      logger.e('Error in Toast', error: e);
    }
  }

  @override
  List<Object?> get props => [error];
}

class EmptyDataState extends BlocState {
  @override
  List<Object?> get props => [];
}

class NoContentState extends BlocState {
  final String masterSongId;

  const NoContentState(this.masterSongId);

  @override
  List<Object?> get props => [masterSongId];
}
