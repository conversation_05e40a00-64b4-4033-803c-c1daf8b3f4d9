import 'package:equatable/equatable.dart';

abstract class <PERSON>Event extends Equatable {
  const BaseEvent();
}

class LoadDataEvent extends BaseEvent {
  final String? searchQuery;
  const LoadDataEvent({this.searchQuery});

  @override
  List<Object?> get props => [searchQuery];
}

class LoadMoreEvent extends BaseEvent {
  final String? searchQuery;
  const LoadMoreEvent({this.searchQuery});

  @override
  List<Object?> get props => [];
}

class ForceRefreshEvent extends BaseEvent {
  @override
  List<Object?> get props => [];
}

class LoadLyricsEvent extends BaseEvent {
  @override
  List<Object?> get props => [];
}

class LoadSongEvent extends BaseEvent {
  @override
  List<Object?> get props => [];
}
