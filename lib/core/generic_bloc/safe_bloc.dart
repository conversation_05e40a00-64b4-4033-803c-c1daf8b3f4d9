import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

class SafeBloc<T extends BaseEvent, S extends BlocState> extends Bloc<T, S> {
  SafeBloc(super.state);

  @override
  void add(T event) {
    if (isClosed) {
      logger.e('Event $event dropped due to bloc closed.');
      return;
    }
    super.add(event);
  }

  @override
  void emit(S state) {
    if (isClosed) {
      logger.e('State $state dropped due to bloc closed.');
      return;
    }
    // ignore: invalid_use_of_visible_for_testing_member
    super.emit(state);
  }

  @override
  Future<void> close() async {
    if (isClosed) {
      return;
    }
    await super.close();
  }
}
