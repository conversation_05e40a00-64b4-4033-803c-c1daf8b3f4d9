import 'dart:io';

import 'package:dio/dio.dart';
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/api_client/api_exception.dart';
import 'package:melodyze/core/api_client/auth_interceptor.dart';
import 'package:melodyze/core/config/dynamic_config.dart';

class ApiClientImpl implements ApiClient {
  static final ApiClientImpl _instance = ApiClientImpl._internal();
  static Dio? _dio;

  ApiClientImpl._internal();

  factory ApiClientImpl() => _instance;

  final CancelToken _cancelToken = CancelToken();

  Future<Dio> _getDio() async {
    if (_dio != null) return _dio!;
    final baseUrl = await DynamicConfig.getBaseApi();
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
      ),
    )..interceptors.add(AuthInterceptor());
    return _dio!;
  }

  @override
  Future<Map<String, dynamic>?> get(
    url, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final dio = await _getDio();
      Response response = await dio.get(
        url,
        options: Options(headers: headers),
        cancelToken: _cancelToken,
        queryParameters: queryParameters,
      );
      return response.data;
    } catch (error) {
      return _handleError(url, error);
    }
  }

  @override
  Future<Map<String, dynamic>?> post(
    url, {
    Map<String, dynamic>? body,
    Map<String, dynamic>? headers,
  }) async {
    try {
      final dio = await _getDio();
      Response response = await dio.post(
        url,
        data: body,
        options: Options(headers: headers),
        cancelToken: _cancelToken,
      );

      return response.data;
    } catch (error) {
      return _handleError(url, error);
    }
  }

  @override
  Future<Map<String, dynamic>?> put(
    String url, {
    required Map<String, dynamic> body,
    Map<String, dynamic>? headers,
  }) async {
    try {
      final dio = await _getDio();
      Response response = await dio.put(
        url,
        data: body,
        options: Options(headers: headers),
        cancelToken: _cancelToken,
      );
      return response.data;
    } catch (error) {
      return _handleError(url, error);
    }
  }

  @override
  Future<Map<String, dynamic>?> patch(
    String url, {
    required Map<String, dynamic> body,
    Map<String, dynamic>? headers,
  }) async {
    try {
      final dio = await _getDio();
      Response response = await dio.patch(
        url,
        data: body,
        options: Options(headers: headers),
        cancelToken: _cancelToken,
      );
      return response.data;
    } catch (error) {
      return _handleError(url, error);
    }
  }

  @override
  Future<Map<String, dynamic>?> delete(
    String url, {
    required Map<String, dynamic> body,
    Map<String, dynamic>? headers,
  }) async {
    try {
      final dio = await _getDio();
      Response response = await dio.delete(
        url,
        data: body,
        options: Options(headers: headers),
        cancelToken: _cancelToken,
      );
      return response.data;
    } catch (error) {
      return _handleError(url, error);
    }
  }

  @override
  Future<Map<String, dynamic>?> uploadMediaRequest(
    String url, {
    required imagePath,
    required filename,
    Map<String, dynamic>? headers,
  }) async {
    //Sample multipart request
    FormData formData = FormData.fromMap({
      "image": await MultipartFile.fromFile(
        imagePath,
        filename: filename,
      ),
    });

    try {
      final dio = await _getDio();
      Response response = await dio.put(
        url,
        data: formData,
        options: Options(headers: headers),
        cancelToken: _cancelToken,
      );
      return response.data;
    } catch (error) {
      return _handleError(url, error);
    }
  }

  @override
  Future<String?> download(
    String url,
    String savePath, {
    Map<String, dynamic>? headers,
    ApiProgressCallback? onReceiveProgress,
  }) async {
    try {
      final dio = await _getDio();
      Response response = await dio.download(
        url,
        savePath,
        options: Options(headers: headers),
        cancelToken: _cancelToken,
        onReceiveProgress: onReceiveProgress,
      );

      if (response.statusCode == 200) {
        return savePath;
      }
    } catch (error) {
      await _handleError(url, error);
    }
    return null;
  }

  Future<Map<String, dynamic>> _handleError(String path, Object error) {
    if (error is DioException) {
      final method = error.requestOptions.method;
      final response = error.response;

      final data = response?.data;
      int? statusCode = response?.statusCode;

      if (error.type == DioExceptionType.connectionTimeout ||
          error.type == DioExceptionType.sendTimeout ||
          error.type == DioExceptionType.receiveTimeout) {
        statusCode = HttpStatus.requestTimeout; //Set the error code to 408 in case of timeout
      }
      throw ApiException(
        path: path,
        message: 'received server error $statusCode while $method data',
        response: data.toString(),
        statusCode: statusCode,
        method: method,
      );
    } else {
      int errorCode = 0; //We will send a default error code as 0

      throw ApiException(
        path: path,
        message: 'received server error $errorCode',
        response: error.toString(),
        statusCode: errorCode,
        method: '',
      );
    }
  }
}
