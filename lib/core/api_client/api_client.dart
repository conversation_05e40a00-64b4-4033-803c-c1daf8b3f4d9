typedef ApiProgressCallback = void Function(int count, int total);

abstract class ApiClient {
  Future<Map<String, dynamic>?> get(
    String url, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParameters,
  });

  Future<Map<String, dynamic>?> post(
    String url, {
    Map<String, dynamic>? body,
    Map<String, dynamic>? headers,
  });

  Future<Map<String, dynamic>?> put(
    String url, {
    required Map<String, dynamic> body,
    Map<String, dynamic>? headers,
  });

  Future<Map<String, dynamic>?> delete(
    String url, {
    required Map<String, dynamic> body,
    Map<String, dynamic>? headers,
  });

  Future<Map<String, dynamic>?> uploadMediaRequest(
    String url, {
    required imagePath,
    required filename,
    Map<String, dynamic>? headers,
  });

  Future<String?> download(
    String url,
    String savePath, {
    Map<String, dynamic>? headers,
    ApiProgressCallback? onReceiveProgress,
  });

  Future<Map<String, dynamic>?> patch(
    String url, {
    required Map<String, dynamic> body,
    Map<String, dynamic>? headers,
  });
}
