import 'package:dio/dio.dart';
import 'package:melodyze/core/services/user_data_manager.dart';
import 'package:melodyze/core/wrappers/event_bus.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';
import 'package:melodyze/modules/auth/bloc/authentication_event.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AuthInterceptor extends Interceptor {
  @override
  Future<void> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    options.headers['accept'] = options.headers['accept'] ?? 'application/json';
    options.headers['Content-Type'] = options.headers['Content-Type'] ?? 'application/json';
    options.headers['x-auth-token'] = await DI().resolve<AccessTokenHelper>().accessToken;

    final userDataManager = DI().resolve<UserDataManager>();
    final user = await userDataManager.getUserData();

    if (user != null) {
      options.headers['x-user-email'] = user.email;
      options.headers['x-user-platform'] = '${user.deviceInfo.platform}_${user.deviceInfo.osVersion}';
    }

    final pkg = await PackageInfo.fromPlatform();
    options.headers['x-app-version'] = '${pkg.version}_${pkg.buildNumber}';
    handler.next(options);
  }

  @override
  Future<void> onResponse(
      Response response, ResponseInterceptorHandler handler) async {
    final String? authToken = response.headers['x-access-token']?.first;
    if (authToken != null) {
      await DI().resolve<AccessTokenHelper>().setAccessToken(authToken);
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      DI().resolve<EventBus>().fire(SignOut());
    }
    return super.onError(err, handler);
  }
}
