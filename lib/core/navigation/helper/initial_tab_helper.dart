import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/remote_config_helper.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';

class InitialTabHelper {
  static bool isInitialTab(int tabIndex) {
    final requiredIndex = DI().resolve<FirstLoginHelper>().isFirstTimeSync ? 0 : RemoteConfigHelper.getInt(RemoteConfigKeys.initialTabIndex);
    return tabIndex == requiredIndex;
  }
}
