import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';

class PreferenceGuard extends AutoRouteGuard {
  final SecureStorageHelper _secureStorageHelper;

  PreferenceGuard({required SecureStorageHelper secureStorageHelper}) : _secureStorageHelper = secureStorageHelper;

  @override
  Future<void> onNavigation(NavigationResolver resolver, StackRouter router) async {
    final preferencesKey = await _secureStorageHelper.read(SecureStorageKeys.isPreferenceSelected);
    final hasPreferences = preferencesKey != null && preferencesKey.isNotEmpty;

    if (hasPreferences) {
      resolver.next(true);
    } else {
      await router.replace(const PreferenceRoute());
      resolver.next(false);
    }
  }
}
