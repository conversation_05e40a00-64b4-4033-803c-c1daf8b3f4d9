import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';

class AuthGuard extends AutoRouteGuard {
  final SecureStorageHelper _secureStorageHelper;

  AuthGuard({
    required SecureStorageHelper secureStorageHelper,
  }) : _secureStorageHelper = secureStorageHelper;

  @override
  Future<void> onNavigation(NavigationResolver resolver, StackRouter router) async {
    final userData = await _secureStorageHelper.read(SecureStorageKeys.user);
    if (userData != null && userData.isNotEmpty) {
      resolver.next();
    } else {
      await router.navigate(const LoginRoute());
    }
  }
}
