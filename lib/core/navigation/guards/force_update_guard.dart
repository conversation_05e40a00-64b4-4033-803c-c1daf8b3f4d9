import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/modules/app_update/models/app_version_config.dart';
import 'package:melodyze/modules/app_update/service/app_update_service.dart';

class ForceUpdateGuard extends AutoRouteGuard {
  const ForceUpdateGuard();

  @override
  Future<void> onNavigation(NavigationResolver resolver, StackRouter router) async {
    final shouldShowForceUpdate = await AppUpdateService.isForceUpdate();
    if (shouldShowForceUpdate && AppVersionConfig.getConfig() != null) {
      await router.replace(ForceUpdateRoute(config: AppVersionConfig.getConfig()!));
      resolver.next(false);
    } else {
      return resolver.next();
    }
  }
}
