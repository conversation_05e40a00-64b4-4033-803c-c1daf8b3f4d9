import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:video_player/video_player.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/ui/widgets/screenshot_toggle_gesture.dart';

/// Enhanced version with additional customization options
class SplashScreenVideo extends StatefulWidget {
  final Widget? child;
  final Duration minDuration;
  final Duration maxDuration;
  final Duration fadeInDuration;
  final Duration fadeOutDuration;
  final Color backgroundColor;
  final VoidCallback? onVideoLoadFailed;
  final VoidCallback? onSplashComplete;

  const SplashScreenVideo({
    super.key,
    this.child,
    this.minDuration = const Duration(seconds: 2),
    this.maxDuration = const Duration(milliseconds: 2600),
    this.fadeInDuration = const Duration(milliseconds: 500),
    this.fadeOutDuration = const Duration(milliseconds: 300),
    this.backgroundColor = Colors.black,
    this.onVideoLoadFailed,
    this.onSplashComplete,
  });

  @override
  State<SplashScreenVideo> createState() => _SplashScreenVideoState();
}

class _SplashScreenVideoState extends State<SplashScreenVideo> with TickerProviderStateMixin {
  VideoPlayerController? _videoController;
  late AnimationController _fadeController;
  late AnimationController _transitionController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _transitionAnimation;

  bool _isVideoReady = false;
  bool _showVideo = false;
  bool _showApp = false;
  bool _disposed = false;
  bool _isTransitioning = false;
  bool _shouldLoadNextScreen = false; // Add this to control when next screen loads
  
  // Add this to track video start time
  DateTime? _videoStartTime;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupFullScreen();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: widget.fadeInDuration,
      vsync: this,
    );

    _transitionController = AnimationController(
      duration: widget.fadeOutDuration,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _transitionAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _transitionController,
      curve: Curves.easeInOut,
    ));
  }

  void _setupFullScreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  }

  void _startSplashSequence() {
    _initializeVideo();
    // Only schedule the minimum duration check initially
    Future.delayed(widget.minDuration, _checkVideoTransition);
    
    // Schedule next screen loading after 2.5 seconds
    Future.delayed(const Duration(milliseconds: 2450), _enableNextScreenLoading);
  }

  Future<void> _initializeVideo() async {
    try {
      _videoController = VideoPlayerController.asset(AssetPaths.splashScreenVdo);
      await _videoController!.initialize();

      if (!_disposed && mounted) {
        await _videoController!.setLooping(true);
        setState(() => _isVideoReady = true);
        _checkVideoTransition();
      }
    } catch (e) {
      logger.e('Video initialization failed: $e');
      widget.onVideoLoadFailed?.call();
      // If video fails, still proceed with transition after min duration
      Future.delayed(widget.minDuration, _transitionToApp);
    }
  }

  void _checkVideoTransition() {
    if (!_disposed && mounted && _isVideoReady && !_showVideo && !_showApp && !_isTransitioning) {
      _showVideoWithTransition();
    }
  }

  Future<void> _showVideoWithTransition() async {
    if (_disposed || _isTransitioning) return;

    setState(() => _showVideo = true);
    _videoStartTime = DateTime.now(); // Record when video actually starts
    await _videoController?.play();
    await _fadeController.forward();
    
    // Now schedule the app transition based on when video actually started
    _scheduleAppTransition();
  }

  void _scheduleAppTransition() {
    if (_videoStartTime == null || _videoController == null) return;
    
    // Use actual video duration if available, otherwise fall back to maxDuration
    final videoDuration = _videoController!.value.duration;
    final targetDuration = videoDuration.inMilliseconds > 0 
        ? videoDuration 
        : widget.maxDuration;
    
    // Calculate remaining time from when video started
    final elapsed = DateTime.now().difference(_videoStartTime!);
    final remainingTime = targetDuration - elapsed;
    
    // Ensure we don't have negative duration and add small buffer
    final transitionDelay = remainingTime.isNegative 
        ? Duration.zero 
        : remainingTime + const Duration(milliseconds: 200); // Small buffer
    
    Future.delayed(transitionDelay, _transitionToApp);
  }

  void _enableNextScreenLoading() {
    if (!_disposed && mounted && !_shouldLoadNextScreen) {
      setState(() => _shouldLoadNextScreen = true);
    }
  }

  Future<void> _transitionToApp() async {
    if (_disposed || _showApp || _isTransitioning) return;

    setState(() => _isTransitioning = true);

    // Start the transition animation
    await _transitionController.forward();

    if (!_disposed && mounted) {
      setState(() => _showApp = true);
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      widget.onSplashComplete?.call();
    }
  }

  @override
  void dispose() {
    _disposed = true;
    _videoController?.dispose();
    _fadeController.dispose();
    _transitionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.backgroundColor,
      body: ScreenshotToggleGesture(
        child: ColoredBox(
          color: widget.backgroundColor,
          child: Stack(
            children: [
              // App content (loads after 2.5 seconds, invisible until splash completes)
              if (widget.child != null && _shouldLoadNextScreen)
                Positioned.fill(
                  child: Opacity(
                    opacity: _showApp ? 1.0 : 0.0, // Invisible but still rendered
                    child: IgnorePointer(
                      ignoring: !_showApp, // Prevent interactions until splash completes
                      child: widget.child!,
                    ),
                  ),
                ),

              // Splash overlay (fades out when transitioning)
              if (!_showApp)
                AnimatedBuilder(
                  animation: _transitionAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _transitionAnimation.value,
                      child: ColoredBox(
                        color: widget.backgroundColor,
                        child: _buildSplashContent(),
                      ),
                    );
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSplashContent() {
    if (_showVideo && _videoController != null && _isVideoReady) {
      return AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return Stack(
            children: [
              // Static background
              ColoredBox(
                color: widget.backgroundColor,
                child: const SizedBox.expand(),
              ),

              // Video content with fade animation
              Opacity(
                opacity: _fadeAnimation.value,
                child: SizedBox.expand(
                  child: FittedBox(
                    fit: BoxFit.cover,
                    child: SizedBox(
                      width: _videoController!.value.size.width,
                      height: _videoController!.value.size.height,
                      child: VideoPlayer(_videoController!),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      );
    }

    // Static splash (shown while video loads or if video fails)
    return ColoredBox(
      color: widget.backgroundColor,
      child: const SizedBox.expand(),
    );
  }
}