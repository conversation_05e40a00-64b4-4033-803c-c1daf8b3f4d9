import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:melodyze/core/services/screenshot_manager.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';

/// A hidden gesture detector that enables/disables screenshots with a secret tap sequence
class ScreenshotToggleGesture extends StatefulWidget {
  final Widget child;
  final int requiredTaps;
  final Duration tapTimeout;
  final VoidCallback? onSecretActivated;

  const ScreenshotToggleGesture({
    super.key,
    required this.child,
    this.requiredTaps = 6,
    this.tapTimeout = const Duration(seconds: 3),
    this.onSecretActivated,
  });

  @override
  State<ScreenshotToggleGesture> createState() => _ScreenshotToggleGestureState();
}

class _ScreenshotToggleGestureState extends State<ScreenshotToggleGesture> {
  int _tapCount = 0;
  DateTime? _firstTapTime;
  bool _isProcessing = false;

  void _handleTap() {
    if (_isProcessing) return;

    final now = DateTime.now();

    // Reset if timeout exceeded or first tap
    if (_firstTapTime == null) {
      _firstTapTime = now;
      _tapCount = 1;
    } else if (now.difference(_firstTapTime!) > widget.tapTimeout) {
      _firstTapTime = now;
      _tapCount = 1;
    } else {
      _tapCount++;
    }

    logger.d('Hidden gesture tap count: $_tapCount/${widget.requiredTaps}');
    if (_tapCount == widget.requiredTaps) {
      _activateSecretFeature();
    }
  }

  void _resetTapCount() {
    _tapCount = 0;
    _firstTapTime = null;
  }

  Future<void> _activateSecretFeature() async {
    if (_isProcessing) return;
    _isProcessing = true;
    try {
      unawaited(HapticFeedback.mediumImpact());
      await ScreenshotManager.instance.toggleScreenshots();

      final isEnabled = await ScreenshotManager.instance.getScreenshotEnabled();

      if (mounted) {
        final message = isEnabled ? 'Screenshots Enabled' : 'Screenshots Disabled';
        unawaited(DI().resolve<AppToast>().showToast(message));
      }
      widget.onSecretActivated?.call();
    } catch (e) {
      logger.e('Error activating hidden gesture: $e');
    } finally {
      _resetTapCount();
      _isProcessing = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      behavior: HitTestBehavior.translucent,
      child: widget.child,
    );
  }
}
