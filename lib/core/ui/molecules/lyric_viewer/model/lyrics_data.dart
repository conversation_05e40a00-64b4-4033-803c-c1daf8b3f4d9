import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/core/utilities/freezed_utils.dart';

part 'lyrics_data.freezed.dart';
part 'lyrics_data.g.dart';

@freezed
abstract class LyricsData with _$LyricsData {
  factory LyricsData({
    @JsonKey(name: 'countdown') required CountdownData countdown,
    @JsonKey(name: 'lyrics') required LyricsList lyrics,
    @JsonKey(name: 'tempo') @JsonStringToInt() required int tempo,
  }) = _LyricsData;

  factory LyricsData.fromJson(Map<String, dynamic> json) => _$LyricsDataFromJson(json);

  static Map<String, dynamic>? lyricsDataToJson(LyricsData? data) => data?.toJson();
}

@freezed
abstract class CountdownData with _$CountdownData {
  factory CountdownData({
    @JsonKey(name: 'data') required List<CountdownLineData> data,
  }) = _CountdownData;

  factory CountdownData.fromJson(Map<String, dynamic> json) => _$CountdownDataFromJson(json);
}

@freezed
abstract class CountdownLineData with _$CountdownLineData {
  factory CountdownLineData({
    @JsonKey(name: 'line_number') required int lineNumber,
    @JsonKey(name: 'text') required String text,
    @JsonKey(name: 'start_time') required String startTime,
    @JsonKey(name: 'end_time') required String endTime,
  }) = _CountdownLineData;

  factory CountdownLineData.fromJson(Map<String, dynamic> json) => _$CountdownLineDataFromJson(json);
}

@freezed
abstract class LyricsList with _$LyricsList {
  factory LyricsList({
    @JsonKey(name: 'data') required List<LyricLineData> data,
  }) = _LyricsList;

  factory LyricsList.fromJson(Map<String, dynamic> json) => _$LyricsListFromJson(json);
}

@freezed
abstract class LyricLineData with _$LyricLineData {
  factory LyricLineData({
    @JsonKey(name: 'line_number') required int lineNumber,
    @JsonKey(name: 'text') required String text,
    @JsonKey(name: 'start_time') required String startTime,
    @JsonKey(name: 'end_time') required String endTime,
  }) = _LyricLineData;

  factory LyricLineData.fromJson(Map<String, dynamic> json) => _$LyricLineDataFromJson(json);
}
