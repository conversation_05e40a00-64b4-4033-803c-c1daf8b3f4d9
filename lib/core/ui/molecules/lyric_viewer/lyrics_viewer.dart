import 'dart:math';

import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

typedef OnLyricTapped = void Function(int lineIndex, int timestampMillis);

class LyricsViewer extends StatefulWidget {
  final LyricsData lyricsData;
  final int? position;
  final OnLyricTapped? onLyricTapped;
  final bool isMixPage;

  const LyricsViewer({
    super.key,
    required this.lyricsData,
    this.position,
    this.onLyricTapped,
    this.isMixPage = false,
  });

  static void resetPosition(GlobalKey key) {
    final state = key.currentState as State<LyricsViewer>;
    if (state is _LyricsViewerState) {
      state.resetToInitialPosition();
    }
  }

  @override
  State<LyricsViewer> createState() => _LyricsViewerState();
}

class _LyricsViewerState extends State<LyricsViewer> {
  final ItemScrollController _itemScrollController = ItemScrollController();
  final ItemPositionsListener _itemPositionsListener = ItemPositionsListener.create();
  int _currentLyricLineIndex = 0;
  int _currentCountDownLineIndex = -1;

  /// Reset lyrics viewer to initial position (same as when first landing on page)
  void resetToInitialPosition() {
    setState(() {
      _currentLyricLineIndex = 0;
      _currentCountDownLineIndex = -1;
    });

    // Scroll to the beginning of the lyrics
    if (_itemScrollController.isAttached) {
      _itemScrollController.scrollTo(
        index: 0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        alignment: 0.40,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _updateCurrentIndices();
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToCurrentLyricLine());
  }

  @override
  void didUpdateWidget(LyricsViewer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.position != widget.position) {
      _updateCurrentIndices();
      _scrollToCurrentLyricLine();
    }
  }

  void _updateCurrentIndices() {
    setState(() {
      _currentLyricLineIndex = findCurrentLyricLine(widget.position ?? 0);
      _currentCountDownLineIndex = findCurrentCountDownLine(widget.position ?? 0);
    });
  }

  int findCurrentLyricLine(int timestampMillis) {
    for (int i = 0; i < widget.lyricsData.lyrics.data.length; i++) {
      final line = widget.lyricsData.lyrics.data[i];
      if (isCurrentTimestampInRange(timestampMillis: timestampMillis, startTime: line.startTime, endTime: line.endTime)) {
        return i;
      }
    }
    return -1;
  }

  int findCurrentCountDownLine(int timestampMillis) {
    for (int i = 0; i < widget.lyricsData.countdown.data.length; i++) {
      final line = widget.lyricsData.countdown.data[i];
      if (isCurrentTimestampInRange(timestampMillis: timestampMillis, startTime: line.startTime, endTime: line.endTime)) {
        return i;
      }
    }
    return -1;
  }

  bool isCurrentTimestampInRange({required int timestampMillis, required String startTime, required String endTime}) {
    final startTimeMillis = _convertTimestampToMillis(startTime);
    final int endTimeMillis;
    if (endTime == "00:00:000") {
      endTimeMillis = _convertTimestampToMillis(startTime, 10000);
    } else {
      endTimeMillis = _convertTimestampToMillis(endTime);
    }
    return timestampMillis >= startTimeMillis && timestampMillis < endTimeMillis;
  }

  int _convertTimestampToMillis(String timestamp, [int offset = 0]) {
    return timestamp.parseAsDuration().inMilliseconds + offset;
  }

  void _scrollToCurrentLyricLine() {
    if (_itemScrollController.isAttached && _currentLyricLineIndex >= 0) {
      final index = _currentLyricLineIndex;
      final itemCount = widget.lyricsData.lyrics.data.length;

      // Ensure index is within bounds
      if (index >= 0 && index < itemCount) {
        _itemScrollController.scrollTo(
          index: index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          alignment: 0.40, //50% of the screen
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            return ShaderMask(
              shaderCallback: (bounds) => AppGradients.gradientPinkPurpleBackground.createShader(bounds),
              child: ScrollablePositionedList.builder(
                padding: EdgeInsets.symmetric(
                  vertical: (constraints.maxHeight / 2),
                  horizontal: widget.isMixPage ? (constraints.maxWidth / 100) * 10 : 0,
                ),
                itemCount: widget.lyricsData.lyrics.data.length,
                itemBuilder: (context, index) => _buildLyricItem(index),
                itemScrollController: _itemScrollController,
                itemPositionsListener: _itemPositionsListener,
              ),
            );
          },
        ),
        if (_currentCountDownLineIndex != -1)
          Center(
            child: Text(
              widget.lyricsData.countdown.data[_currentCountDownLineIndex].text,
              style: AppTextStyles.text48Bold,
            ),
          ),
        // for top shadow
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.black, Colors.transparent],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLyricItem(int index) {
    final line = widget.lyricsData.lyrics.data[index];
    final isCurrentLine = index == _currentLyricLineIndex;
    final distanceFromCurrent = (index - _currentLyricLineIndex).abs();

    // Calculate font size based on distance from current line
    double fontSize = 14.0;
    if (distanceFromCurrent > 1) {
      fontSize = max(8.0, 12.0 - (distanceFromCurrent - 1));
    }

    final textWidget = Center(
      child: Text(
        line.text,
        style: AppTextStyles.textEthnocentricStyle.copyWith(
          fontSize: fontSize,
          color: AppColors.white,
        ),
        textAlign: TextAlign.center,
        textScaler: TextScaler.noScaling,
      ),
    );

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16, horizontal: isCurrentLine ? 16 : 48),
      child: GestureDetector(
        onTap: () {
          widget.onLyricTapped?.call(index, _convertTimestampToMillis(line.startTime));
        },
        child: isCurrentLine
            ? AppGradientContainer(
                borderGradient: AppGradients.gradientBluePinkBorder,
                borderRadius: BorderRadius.circular(36.0),
                margin: const EdgeInsets.symmetric(horizontal: 24),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: textWidget,
              )
            : textWidget,
      ),
    );
  }
}
