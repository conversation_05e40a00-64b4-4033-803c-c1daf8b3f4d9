import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/network_image.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

class CheckedTile extends StatefulWidget {
  final VoidCallback? onTap;
  final bool isSelected;
  final String s3ImageUrl;
  final String title;
  final bool showLoading;

  const CheckedTile({
    super.key,
    this.onTap,
    this.isSelected = false,
    required this.s3ImageUrl,
    required this.title,
    this.showLoading = false,
  });

  @override
  State<CheckedTile> createState() => _CheckedTileState();
}

class _CheckedTileState extends State<CheckedTile> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  GestureDetector(
                    onTapDown: _handleTapDown,
                    onTapUp: _handleTapUp,
                    onTapCancel: _handleTapCancel,
                    onTap: widget.showLoading ? null : widget.onTap,
                    child: AppGradientContainer(
                      margin: const EdgeInsets.only(top: 4.0),
                      borderRadius: BorderRadius.circular(36),
                      borderWidth: 2.6,
                      borderGradient: widget.isSelected ? AppGradients.gradientPurpleBorder : null,
                      child: AspectRatio(
                        aspectRatio: 1,
                        child: AnimatedPadding(
                          duration: const Duration(milliseconds: 200),
                          padding: EdgeInsets.all(widget.isSelected ? 4 : 0),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(32),
                            child: NetworkImageWidget(
                              imageUrl: widget.s3ImageUrl,
                              fadeInDuration: const Duration(milliseconds: 400),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  if (widget.isSelected)
                    AnimatedPositioned(
                      duration: const Duration(milliseconds: 500),
                      top: -4,
                      right: -10,
                      child: AnimatedScale(
                        scale: widget.isSelected ? 1.0 : 0.0,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.elasticOut,
                        child: AppGradientContainer(
                          width: 28,
                          height: 28,
                          margin: const EdgeInsets.only(right: 8),
                          gradient: AppGradients.gradientPinkBorder,
                          borderGradient: AppGradients.gradientPinkBorder,
                          child: Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: ImageLoader.fromAsset(AssetPaths.check),
                          ),
                        ),
                      ),
                    ),
                  if (widget.showLoading)
                    const Positioned.fill(
                      child: Center(
                        child: AppCircularProgressIndicator(),
                      ),
                    )
                ],
              ),
              const SizedBox(height: 4),
              Flexible(
                child: MediaQuery(
                  data: MediaQuery.of(context).copyWith(textScaler: TextScaler.linear(1.0)),
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 300),
                    opacity: 1.0,
                    child: Text(
                      widget.title,
                      style: AppTextStyles.text14regular.copyWith(
                        fontFamily: AppFonts.iceland,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
