import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_border.dart';
import 'package:melodyze/core/ui/molecules/butons/app_button.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:permission_handler/permission_handler.dart';

Future<bool> showYesNoDialog({
  required BuildContext context,
  required String title,
  String subTitle = 'Are you sure?', // Default
}) async {
  bool? decision;

  await showDialog<bool>(
    context: context,
    builder: (context) => AlertDialog(
      surfaceTintColor: AppColors.darkNavyBlue,
      title: Text(
        title,
        style: AppTextStyles.text32Bold.copyWith(fontFamily: AppFonts.iceland),
      ),
      content: Text(
        subTitle,
        style: AppTextStyles.text24semiBold.copyWith(fontFamily: AppFonts.iceland),
      ), // No longer optional
      shape: AppGradientBoxBorder(
        gradient: AppGradients.gradientPinkBlueBorder,
        borderRadius: BorderRadius.circular(26),
      ),
      actions: [
        TextButton(
          onPressed: () {
            decision = false;
            Navigator.pop(context); // Close dialog
          },
          child: Text(
            'No',
            style: AppTextStyles.bodySmall.copyWith(color: AppColors.white),
          ),
        ),
        SizedBox(
          height: 38,
          child: AppButton(
            text: 'Yes',
            gradient: AppGradients.gradientPurpleTeal,
            innerPadding: EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            onPressed: () {
              decision = true;
              Navigator.pop(context); // Close dialog
            },
          ),
        ),
      ],
    ),
  );
  // If no choice was made (e.g., dialog dismissed without pressing buttons), default to false
  return decision ?? false;
}

Future<bool> showOkDialog({
  required BuildContext context,
  required String title,
  String subTitle = '',
}) async {
  bool? decision;

  await showDialog<bool>(
    context: context,
    builder: (context) => AlertDialog(
      surfaceTintColor: AppColors.darkNavyBlue,
      title: Text(
        title,
        style: AppTextStyles.text32Bold.copyWith(fontFamily: AppFonts.iceland),
      ),
      content: Text(
        subTitle,
        style: AppTextStyles.text24semiBold.copyWith(fontFamily: AppFonts.iceland),
      ),
      shape: AppGradientBoxBorder(
        gradient: AppGradients.gradientPinkBlueBorder,
        borderRadius: BorderRadius.circular(26),
      ),
      actions: [
        SizedBox(
          height: 40,
          child: AppButton(
            text: 'Ok',
            gradient: AppGradients.gradientPurpleTeal,
            innerPadding: EdgeInsets.symmetric(horizontal: 32, vertical: 8),
            onPressed: () {
              decision = true;
              Navigator.pop(context); // Close dialog, we can add a callback also
            },
          ),
        )
      ],
    ),
  );
  // If no choice was made (e.g., dialog dismissed without pressing buttons), default to false
  return decision ?? false;
}

Future<bool> showGivePermissionDialog({
  required BuildContext context,
  required String title,
  String subTitle = "To record a audio, Melodyze needs access to your phone's microphone. Tap settings and turn on Microphone.", // Default
}) async {
  bool? decision;

  await showDialog<bool>(
    context: context,
    builder: (context) => AlertDialog(
      surfaceTintColor: AppColors.darkNavyBlue,
      title: Text(
        title,
        style: AppTextStyles.text32Bold.copyWith(fontFamily: AppFonts.iceland),
      ),
      content: Text(
        subTitle,
        style: AppTextStyles.text24semiBold.copyWith(fontFamily: AppFonts.iceland),
      ), // No longer optional
      shape: AppGradientBoxBorder(
        gradient: AppGradients.gradientPinkBlueBorder,
        borderRadius: BorderRadius.circular(26),
      ),
      actions: [
        TextButton(
          onPressed: () {
            decision = false;
            Navigator.pop(context); // Close dialog
          },
          child: Text(
            'Cancel',
            style: AppTextStyles.bodySmall.copyWith(color: AppColors.white),
          ),
        ),
        SizedBox(
          height: 38,
          child: AppButton(
            text: 'Settings',
            gradient: AppGradients.gradientPurpleTeal,
            innerPadding: EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            onPressed: () async {
              decision = true;
              Navigator.pop(context); // Close dialog
              await openAppSettings();
            },
          ),
        ),
      ],
    ),
  );
  // If no choice was made (e.g., dialog dismissed without pressing buttons), default to false
  return decision ?? false;
}
