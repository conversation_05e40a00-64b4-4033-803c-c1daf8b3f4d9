import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:shimmer/shimmer.dart';

class NetworkImageWidget extends StatelessWidget {
  final String imageUrl;
  final BoxFit fit;
  final String? cacheKey;
  final Duration fadeInDuration;
  final Duration placeholderFadeOutDuration;

  const NetworkImageWidget({
    super.key,
    required this.imageUrl,
    this.fit = BoxFit.cover,
    this.cacheKey,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.placeholderFadeOutDuration = const Duration(milliseconds: 200),
  });

  @override
  Widget build(BuildContext context) {
    return ImageLoader.cachedNetworkImage(
      imageUrl,
      fit: fit,
      cacheKey: cacheKey,
      placeholderWidgetBuilder: (context, url) => AnimatedSwitcher(
        duration: placeholderFadeOutDuration,
        child: _buildShimmerPlaceholder(),
      ),
      errorBuilder: (context, url, error) => AnimatedContainer(
        duration: fadeInDuration,
        child: _buildErrorWidget(),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.black010101_75,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Opacity(
          opacity: 0.3,
          child: ImageLoader.fromAsset(
            AssetPaths.logoPng,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerPlaceholder() {
    return Shimmer.fromColors(
      baseColor: AppColors.darkBlack.withValues(alpha: 0.3),
      highlightColor: AppColors.darkCyan.withValues(alpha: 0.6),
      period: const Duration(milliseconds: 1200),
      child: AnimatedContainer(
        duration: placeholderFadeOutDuration,
        decoration: BoxDecoration(
          color: AppColors.darkCyan.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.darkBlack.withValues(alpha: 0.1),
                const Color.fromARGB(255, 8, 46, 50).withValues(alpha: 0.05),
                AppColors.darkBlack.withValues(alpha: 0.1),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}
