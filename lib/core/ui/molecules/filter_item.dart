import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

class FilterItem extends StatelessWidget {
  final String thumbnailPath;
  final String title;
  final bool isSelected;
  final VoidCallback onTap;
  final bool showLoading;

  const FilterItem({
    super.key,
    required this.thumbnailPath,
    required this.title,
    required this.isSelected,
    required this.onTap,
    this.showLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                height: 70,
                width: 70,
                clipBehavior: Clip.antiAlias,
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: isSelected
                      ? [
                          for (final blur in <double>[0.5, 1, 1.4, 2, 9.5, 11])
                            BoxShadow(
                              color: AppColors.cyanA400_100,
                              offset: Offset(0, 0),
                              blurRadius: blur,
                            ),
                        ]
                      : null,
                ),
                child: AspectRatio(
                  aspectRatio: 1,
                  child: ImageLoader.fromAsset(thumbnailPath),
                ),
              ),
              showLoading
                  ? const Positioned.fill(
                      child: Center(
                        child: AppCircularProgressIndicator(),
                      ),
                    )
                  : const SizedBox.shrink()
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              title,
              style: AppTextStyles.text12semiBold,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
