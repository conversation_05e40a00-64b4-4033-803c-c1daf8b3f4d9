import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/marquee.dart';
import 'package:melodyze/core/ui/molecules/network_image.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';

class BigHorizontalList extends StatelessWidget {
  //TODO: make a abstract list model for this list
  final List<SongModel> songs;
  final EdgeInsetsGeometry? padding;
  final Function(SongModel song)? onTap;
  static const _width = 90.0;

  const BigHorizontalList({
    super.key,
    this.padding,
    required this.songs,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 232,
      child: ListView.separated(
        padding: padding,
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        itemCount: songs.length,
        itemBuilder: (BuildContext context, int index) {
          final item = songs[index];
          return InkWell(
            onTap: () {
              onTap?.call(item);
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: _width,
                  height: _width * 16 / 9,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: NetworkImageWidget(imageUrl: item.thumbnailPath),
                  ),
                ),
                const SizedBox(height: 12.0),
                Marqueee(
                  text: item.title,
                  style: AppTextStyles.text12medium,
                  width: _width,
                ),
                const SizedBox(height: 2.0),
                SizedBox(
                  width: _width,
                  child: Marqueee(
                    text: item.singer,
                    width: _width,
                    style: AppTextStyles.text10regular.copyWith(
                      color: AppColors.greyText,
                      fontFamily: AppFonts.iceland,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
        separatorBuilder: (BuildContext context, int index) => const SizedBox(
          width: 18,
        ),
      ),
    );
  }
}
