import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

class MeloAppBar extends StatelessWidget implements PreferredSizeWidget {
  final WidgetBuilder? secondaryAction;
  final bool showBackButton;
  final String title;
  final VoidCallback? onBackPressed;
  final String? popScopeDescription;

  /// Called when the pop scope is invoked and yes pressed after showYesNoDialog
  final VoidCallback? onPopScopeDiscard;
  final VoidCallback? onPopInvoked;

  bool get hasActionButtons => showBackButton || secondaryAction != null;

  const MeloAppBar({
    super.key,
    this.secondaryAction,
    this.showBackButton = true,
    this.title = '',
    this.onBackPressed,
    this.popScopeDescription,
    this.onPopScopeDiscard,
    this.onPopInvoked,
  });

  @override
  Widget build(BuildContext context) {
    final description = popScopeDescription;
    if ((description != null && description.isNotEmpty) || onPopInvoked != null) {
      return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, _) async {
            if (didPop) {
              return;
            }

            if (description != null && description.isNotEmpty) {
              final result = await showYesNoDialog(
                context: context,
                title: 'Discard',
                subTitle: description,
              );
              if (result && context.mounted) {
                onPopScopeDiscard?.call();
                Navigator.pop(context);
              }
              return;
            }
            if (onPopInvoked != null) {
              onPopInvoked?.call();
              return;
            }
          },
          child: _buildWidget(context));
    }

    return _buildWidget(context);
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  Widget _buildWidget(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: hasActionButtons ? MediaQuery.of(context).padding.top + 16 : 0, left: 16, right: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Visibility(
                visible: showBackButton,
                maintainAnimation: title.isEmpty,
                maintainSize: title.isEmpty,
                maintainState: title.isEmpty,
                child: IconButton(
                  onPressed: () {
                    final tempOnBackPressed = onBackPressed;
                    tempOnBackPressed != null ? tempOnBackPressed.call() : context.maybePop();
                  },
                  icon: ImageLoader.fromAsset(
                    AssetPaths.backButton,
                    height: 24,
                  ),
                ),
              ),
              if (title.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: Text(title),
                ),
            ],
          ),
          if (secondaryAction != null) secondaryAction!(context),
        ],
      ),
    );
  }
}
