import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/top_gradient.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_app_bar.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';

class MeloScaffold extends StatelessWidget {
  final Widget? background;
  final Widget? body;
  final WidgetBuilder? secondaryAction;
  final bool showAppBar;
  final bool showBackground;
  final Widget? bottomNavigationBar;
  final bool showBackButton;
  final String title;
  final VoidCallback? onBackPressed;
  final String? popScopeDescription;
  final VoidCallback? onPopScopeDiscard;
  final VoidCallback? onPopInvoked;
  final bool? extendBody;
  final Widget? overlayAction;

  const MeloScaffold({
    super.key,
    this.background,
    this.body,
    this.secondaryAction,
    this.showAppBar = true,
    this.showBackground = true,
    this.showBackButton = true,
    this.bottomNavigationBar,
    this.title = '',
    this.onBackPressed,
    this.popScopeDescription,
    this.onPopScopeDiscard,
    this.onPopInvoked,
    this.extendBody,
    this.overlayAction,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: showAppBar
          ? MeloAppBar(
              secondaryAction: secondaryAction,
              showBackButton: showBackButton,
              title: title,
              onBackPressed: onBackPressed,
              popScopeDescription: popScopeDescription,
              onPopScopeDiscard: onPopScopeDiscard,
              onPopInvoked: onPopInvoked,
            )
          : null,
      bottomNavigationBar: bottomNavigationBar,
      extendBody: extendBody ?? true,
      extendBodyBehindAppBar: true,
      backgroundColor: AppColors.black010101,
      body: Stack(
        alignment: Alignment.center,
        children: [
          if (showBackground) (background != null) ? background! : const TopGradient(),
          if (body != null) body!,
          if (overlayAction != null) overlayAction!,
        ],
      ),
    );
  }
}
