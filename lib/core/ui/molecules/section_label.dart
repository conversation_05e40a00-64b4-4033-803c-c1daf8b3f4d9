import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/molecules/butons/app_text_button.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';

class SectionLabel extends StatelessWidget {
  final String sectionText;
  final String? actionText;
  final VoidCallback? actionOnClick;

  const SectionLabel({
    super.key,
    required this.sectionText,
    this.actionText,
    this.actionOnClick,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: Text(
            sectionText,
            style: AppTextStyles.text24semiBold.copyWith(
              fontFamily: AppFonts.inter,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (actionText != null)
          AppTextButton(
            onPressed: actionOnClick,
            text: actionText ?? '',
          ),
      ],
    );
  }
}
