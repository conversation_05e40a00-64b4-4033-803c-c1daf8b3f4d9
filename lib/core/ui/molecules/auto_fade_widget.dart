import 'dart:async';

import 'package:flutter/widgets.dart';

class AutoFadeWidget extends StatefulWidget {
  final Widget child;
  final Duration fadeOutDelay;
  final Duration fadeDuration;
  final bool initiallyVisible;
  final bool stopAutoFadeOnInteraction;

  const AutoFadeWidget({
    super.key,
    required this.child,
    this.fadeOutDelay = const Duration(seconds: 2),
    this.fadeDuration = const Duration(milliseconds: 300),
    this.initiallyVisible = true,
    this.stopAutoFadeOnInteraction = false,
  });

  @override
  State<AutoFadeWidget> createState() => _AutoFadeWidgetState();
}

class _AutoFadeWidgetState extends State<AutoFadeWidget> {
  bool _visible = true;
  Timer? _fadeTimer;

  @override
  void initState() {
    super.initState();
    _visible = widget.initiallyVisible;
    if (_visible && !widget.stopAutoFadeOnInteraction) {
      _startFadeTimer();
    }
  }

  @override
  void didUpdateWidget(covariant AutoFadeWidget oldWidget) {
    if (widget.stopAutoFadeOnInteraction != oldWidget.stopAutoFadeOnInteraction) {
      if (widget.stopAutoFadeOnInteraction) {
        _fadeTimer?.cancel();
      }
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _fadeTimer?.cancel();
    super.dispose();
  }

  void _startFadeTimer() {
    _fadeTimer?.cancel();
    _fadeTimer = Timer(widget.fadeOutDelay, () {
      if (mounted) {
        setState(() => _visible = false);
      }
    });
  }

  void _handleInteraction() {
    if (mounted) {
      setState(() => _visible = true);
      _startFadeTimer();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTapDown: (_) => _handleInteraction(),
      onPanDown: (_) => _handleInteraction(),
      child: AnimatedOpacity(
        opacity: _visible ? 1.0 : 0.0,
        duration: widget.fadeDuration,
        child: widget.child,
      ),
    );
  }
}
