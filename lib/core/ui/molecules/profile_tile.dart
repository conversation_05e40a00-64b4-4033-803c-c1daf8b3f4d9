import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/molecules/network_image.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';

class ProfileTile extends StatelessWidget {
  final String thumbnailPath;
  final String title;
  final String? subTitle1;
  final String? subTitle2;
  final String? subTitle3;
  final String? createdAt;
  final double imageAspectRatio;
  final VoidCallback? onPressed;

  const ProfileTile(
      {super.key,
      required this.thumbnailPath,
      required this.title,
      required this.subTitle1,
      required this.subTitle2,
      required this.subTitle3,
      this.imageAspectRatio = 1,
      this.onPressed,
      this.createdAt});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            AspectRatio(
              aspectRatio: imageAspectRatio,
              child: NetworkImageWidget(
                imageUrl: thumbnailPath,
              ),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                height: 150,
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.transparent, AppColors.black010101],
                  ),
                ),
                child: Align(
                  alignment: Alignment.bottomLeft,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.text12medium.copyWith(
                          fontFamily: AppFonts.inter,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Wrap(
                        spacing: 4,
                        children: [
                          if (subTitle1 != null && subTitle1!.isNotEmpty)
                            Text(
                              "$subTitle1 $subTitle2 $subTitle3",
                              style: AppTextStyles.text10regular.copyWith(
                                fontFamily: AppFonts.iceland,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                            ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      if (createdAt != null && createdAt!.isNotEmpty)
                        Text(
                          createdAt ?? "",
                          style: TextStyle(color: AppColors.greyText, fontSize: 10),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
