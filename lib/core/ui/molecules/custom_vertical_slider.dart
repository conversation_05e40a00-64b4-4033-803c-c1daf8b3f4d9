import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

enum SliderAlignment { left, right }

class CustomVerticalSlider extends StatefulWidget {
  final double value;
  final double max;
  final double? width;
  final ValueChanged<double>? onChanged;
  final VoidCallback? onChangeStart;
  final ValueChanged<double>? onChangeEnd;
  final String? label;
  final Color color;
  final SliderAlignment alignment;

  const CustomVerticalSlider({
    super.key,
    required this.value,
    required this.max,
    this.onChanged,
    this.onChangeStart,
    this.onChangeEnd,
    this.width,
    this.label,
    this.color = AppColors.greySlider,
    this.alignment = SliderAlignment.left,
  });

  @override
  State<CustomVerticalSlider> createState() => _CustomVerticalSliderState();
}

class _CustomVerticalSliderState extends State<CustomVerticalSlider> {
  late double _currentSliderValue;
  double _lastDragY = 0;

  bool _isDisabled = false;

  void toggleDisabled() {
    setState(() {
      _isDisabled = !_isDisabled;
      if (_isDisabled) {
        widget.onChanged?.call(0.0);
        widget.onChangeEnd?.call(0.0);
      } else {
        widget.onChanged?.call(_currentSliderValue);
        widget.onChangeEnd?.call(_currentSliderValue);
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _currentSliderValue = (widget.value > widget.max) ? widget.max : widget.value;
  }

  Widget buildAlignedWidget(Widget child, bool isTop) {
    return switch (widget.alignment) {
      SliderAlignment.left => Positioned(
          left: 12,
          top: isTop ? 0 : null,
          bottom: isTop ? null : 0,
          child: child,
        ),
      SliderAlignment.right => Positioned(
          right: 12,
          top: isTop ? 0 : null,
          bottom: isTop ? null : 0,
          child: child,
        ),
    };
  }

  Color get sliderColor => _isDisabled ? AppColors.greySlider : widget.color;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onVerticalDragStart: (details) {
          _lastDragY = details.localPosition.dy;
          if (!_isDisabled) {
            widget.onChangeStart?.call();
          }
        },
        onVerticalDragUpdate: (details) {
          setState(() {
            final dragDelta = _lastDragY - details.localPosition.dy;
            _lastDragY = details.localPosition.dy;
            final valueDelta = (dragDelta / constraints.maxHeight) * widget.max;
            _currentSliderValue = (_currentSliderValue + valueDelta).clamp(0.0, widget.max);
            widget.onChanged?.call(_currentSliderValue);
          });
        },
        onVerticalDragEnd: (details) {
          if (!_isDisabled) {
            widget.onChangeEnd?.call(_currentSliderValue);
          }
        },
        child: SizedBox(
          width: (MediaQuery.sizeOf(context).width / 2) - 8,
          child: Column(
            crossAxisAlignment: switch (widget.alignment) {
              SliderAlignment.left => CrossAxisAlignment.start,
              SliderAlignment.right => CrossAxisAlignment.end,
            },
            children: [
              Column(
                children: [
                  IconButton(
                    onPressed: toggleDisabled,
                    icon: ImageLoader.fromAsset(_isDisabled ? AssetPaths.filterVolumeOff : AssetPaths.filterVolume),
                  ),
                  Text(
                    (_currentSliderValue * 100).toStringAsFixed(0),
                    style: AppTextStyles.text12semiBold.copyWith(
                      fontFamily: AppFonts.iceland,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: SizedBox(
                      width: 40.0,
                      height: constraints.maxHeight - 100,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 19.0),
                        child: Container(
                          width: 2.0,
                          height: double.maxFinite,
                          decoration: BoxDecoration(
                            color: sliderColor.withAlpha((0.2 * 255).toInt()),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.bottomCenter,
                            heightFactor: _currentSliderValue / widget.max,
                            child: Container(
                              decoration: BoxDecoration(
                                color: sliderColor,
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  if (widget.label != null)
                    Text(
                      widget.label!,
                      style: AppTextStyles.text12semiBold.copyWith(
                        fontFamily: AppFonts.iceland,
                      ),
                    ),
                ],
              )
            ],
          ),
        ),
      );
    });
  }

  double calculateValue(double dy, double height) {
    double newPosition = dy / height;
    double value = (1 - newPosition) * widget.max;
    return value.clamp(0.0, widget.max);
  }
}
