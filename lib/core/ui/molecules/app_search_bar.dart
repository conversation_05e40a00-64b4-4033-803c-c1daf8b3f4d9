import 'dart:async';

import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

class AppSearchBar extends StatefulWidget {
  final ValueChanged<String>? onTextChanged;
  final String? searchBarHintText;
  final bool showPrefixIcon;
  final double textHeight;

  ///Default time is 400 ms
  final int? debounceTime;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool? enabled;
  final bool autofocus;
  final Color backgroundColor;

  const AppSearchBar({
    super.key,
    this.onTextChanged,
    this.searchBarHintText,
    this.debounceTime,
    this.keyboardType,
    this.showPrefixIcon = true,
    this.textInputAction,
    this.enabled,
    this.autofocus = false,
    this.backgroundColor = AppColors.black010101,
    this.textHeight = 1.1,
  });

  @override
  State<AppSearchBar> createState() => _AppSearchBarState();
}

class _AppSearchBarState extends State<AppSearchBar> {
  final TextEditingController _searchController = TextEditingController();

  Timer? _debounceTimer;

  void _onChanged(String value) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    _debounceTimer = Timer(Duration(milliseconds: widget.debounceTime ?? 400), () {
      widget.onTextChanged?.call(value);
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppGradientContainer(
      height: 36,
      borderGradient: AppGradients.gradientPinkBlueBorder,
      borderWidth: 1,
      borderRadius: BorderRadius.circular(32),
      child: TextField(
        onTapOutside: (event) => FocusManager.instance.primaryFocus?.unfocus(),
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        controller: _searchController,
        onChanged: _onChanged,
        enabled: widget.enabled,
        cursorColor: AppColors.white,
        enableSuggestions: false,
        autocorrect: false,
        style: AppTextStyles.text14regular,
        autofocus: widget.autofocus,
        decoration: InputDecoration(
          hintText: widget.searchBarHintText ?? 'Search by song name',
          hintStyle: AppTextStyles.text14regular.copyWith(
            color: AppColors.greyText,
            fontFamily: AppFonts.iceland,
            height: widget.textHeight,
          ),
          prefixIcon: widget.showPrefixIcon
              ? Padding(
                  padding: const EdgeInsets.only(top: 8, bottom: 8),
                  child: ImageLoader.fromAsset(
                    AssetPaths.search,
                  ),
                )
              : null,
          fillColor: widget.backgroundColor,
          filled: true,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(32.0),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(32.0),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(32.0),
            borderSide: BorderSide.none,
          ),
        ),
      ),
    );
  }
}
