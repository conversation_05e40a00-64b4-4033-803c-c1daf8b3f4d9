// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:melodyze/core/services/aws_client.dart';
// import 'package:melodyze/core/ui/tokens/app_colors.dart';
// import 'package:melodyze/core/ui/tokens/asset_paths.dart';
// import 'package:melodyze/core/wrappers/image_loader.dart';
// import 'package:melodyze/core/wrappers/injector.dart';

// class S3ImageWidget extends StatelessWidget {
//   final String imagePath;

//   const S3ImageWidget({super.key, required this.imagePath});

//   @override
//   Widget build(BuildContext context) {
//     return BlocProvider(
//       create: (context) => _S3ImageCubit(imagePath)..getSignedUrl(),
//       child: _S3ImageWidget(),
//     );
//   }
// }

// class _S3ImageWidget extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<_S3ImageCubit, String>(
//       builder: (context, imageUrl) {
//         return imageUrl.isNotEmpty
//             ? ImageLoader.cachedNetworkImage(
//                 imageUrl,
//                 fit: BoxFit.cover,
//                 cacheKey: context.read<_S3ImageCubit>().imagePath,
//                 errorBuilder: (context, url, error) => Stack(
//                   children: [
//                     Center(child: ImageLoader.fromAsset(AssetPaths.logoPng)),
//                     Container(
//                       color: AppColors.black010101_75,
//                     ),
//                   ],
//                 ),
//               )
//             : const SizedBox.shrink();
//       },
//     );
//   }
// }

// class _S3ImageCubit extends Cubit<String> {
//   final String imagePath;
//   _S3ImageCubit(this.imagePath) : super('');
//   Future<void> getSignedUrl() async => emit(await DI().resolve<AwsClient>().getSignedUrl(imagePath));
// }
