import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';

class Knob extends StatefulWidget {
  final List<String> data;
  final Function(String)? onChanged;
  final String? initialValue;
  final Map<String, String>? labels;
  const Knob({
    super.key,
    required this.data,
    this.onChanged,
    this.initialValue,
    this.labels,
  });

  @override
  State<Knob> createState() => _KnobState();
}

class _KnobState extends State<Knob> {
  int _currentIndex = 0;
  final Debouncer _debouncer = Debouncer();
  late final PageController _controller;

  @override
  void initState() {
    super.initState();
    if (widget.initialValue != null) {
      _currentIndex = widget.data.indexOf(widget.initialValue!);
    }
    _controller = PageController(viewportFraction: 0.45, initialPage: _currentIndex);
  }

  @override
  void didUpdateWidget(Knob oldWidget) {
    if (oldWidget.initialValue != widget.initialValue) {
      _currentIndex = widget.data.indexOf(widget.initialValue!);
      _controller.animateToPage(_currentIndex, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _debouncer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 74,
      child: ShaderMask(
        shaderCallback: (Rect bounds) {
          return LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: const [Colors.transparent, Colors.white, Colors.white, Colors.transparent],
            stops: const [0.0, 0.1, 0.9, 1.0],
          ).createShader(bounds);
        },
        blendMode: BlendMode.dstIn,
        child: PageView.builder(
          scrollDirection: Axis.vertical,
          itemCount: widget.data.length,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
            _debouncer.debounce(() => widget.onChanged?.call(widget.data[index]));
          },
          controller: _controller,
          itemBuilder: (context, index) {
            final item = widget.labels?[widget.data[index]] ?? widget.data[index];
            final color = _currentIndex == index ? AppColors.white : AppColors.grey848484;
            final scaleText = Center(
              child: Text(
                item,
                style: AppTextStyles.text10medium.copyWith(color: color),
              ),
            );
            return Padding(
              padding: const EdgeInsets.all(4),
              child: _currentIndex == index
                  ? AppGradientContainer(
                      borderRadius: BorderRadius.circular(8),
                      borderGradient: AppGradients.gradientPinkBorder,
                      borderWidth: 2,
                      enableBlur: true,
                      blurSigma: 6,
                      child: scaleText,
                    )
                  : Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.grey848484),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: scaleText,
                    ),
            );
          },
        ),
      ),
    );
  }
}
