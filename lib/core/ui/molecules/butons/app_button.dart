import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final VoidCallback? onPressedWhenDisabled;
  final String? iconRight;
  final String? iconLeft;
  final EdgeInsetsGeometry? innerPadding;
  final LinearGradient gradient;
  final LinearGradient gradientDisabled;
  final LinearGradient borderGradient;
  final double borderSize;
  final Color? backgroundColor;
  final double textFontSize;
  final TextStyle? textStyle;

  ///Expands the button to full width
  final bool? isExpanded;

  final bool enabled;
  final bool? isLoading;
  final bool isSecondary;

  const AppButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.onPressedWhenDisabled,
    this.isExpanded,
    this.iconRight,
    this.iconLeft,
    this.enabled = true,
    this.isLoading, // Add isLoading parameter with a default value of false
    this.isSecondary = false,
    this.innerPadding = const EdgeInsets.symmetric(horizontal: 40.0, vertical: 15.0),
    this.gradient = AppGradients.gradientPurpleBackground,
    this.borderGradient = AppGradients.gradientPurpleBorder,
    this.gradientDisabled = AppGradients.commonButtonDisabled,
    this.borderSize = 1,
    this.backgroundColor = const Color(0x1A19B2C2),
    this.textFontSize = 14,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final Color textColor = enabled ? AppColors.white : Colors.grey[400]!;

    return GestureDetector(
      onTap: isLoading ?? false
          ? null
          : enabled
              ? onPressed
              : onPressedWhenDisabled,
      child: AppGradientContainer(
        padding: innerPadding,
        gradient: enabled ? gradient : gradientDisabled,
        borderGradient: borderGradient,
        borderWidth: borderSize,
        backgroundColor: backgroundColor,
        enableBlur: true,
        child: (isLoading ?? false)
            ? Center(
                child: SizedBox(
                  height: 20,
                  width: 20,
                  child: AppCircularProgressIndicator(
                    enabled: enabled,
                  ),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (iconLeft != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ImageLoader.fromAsset(
                        iconLeft!,
                        height: 18,
                        width: 18,
                        color: enabled ? null : textColor.withAlpha(128), // fade icon
                      ),
                    ),
                  Flexible(
                    child: Text(
                      text,
                      textAlign: TextAlign.center,
                      style: (textStyle ?? AppTextStyles.text14semiBold).copyWith(
                        color: enabled ? textColor : textColor.withAlpha(128),
                        fontFamily: textStyle != null ? null : AppFonts.inter,
                        fontSize: textFontSize,
                      ),
                    ),
                  ),
                  if (iconRight != null)
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: ImageLoader.fromAsset(
                        iconRight!,
                        height: 18,
                        width: 18,
                        color: enabled ? null : textColor.withAlpha(128), // fade icon
                      ),
                    ),
                ],
              ),
      ),
    );
  }
}
