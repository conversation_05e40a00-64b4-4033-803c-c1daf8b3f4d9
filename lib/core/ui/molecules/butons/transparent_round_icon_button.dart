import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

class TransParentRoundIconButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final TransParentRoundIconButtonSize? size;
  final IconData? icon;
  final bool isDisabled;
  final Color? backgroundColor;
  final String? assetIcon;
  final Color? iconColor;
  final bool isLoading;

  const TransParentRoundIconButton({
    this.icon,
    super.key,
    this.onPressed,
    this.size,
    this.isDisabled = false,
    this.isLoading = false,
    this.backgroundColor,
    this.assetIcon,
    this.iconColor,
  }) : assert(icon != null || assetIcon != null, 'Either icon or assetIcon must be provided');

  @override
  Widget build(BuildContext context) {
    final size = this.size ?? TransParentRoundIconButtonSize.small;
    return IconButton(
      padding: EdgeInsets.zero,
      icon: Container(
        height: size.size,
        width: size.size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isDisabled ? AppColors.white20 : (backgroundColor ?? AppColors.white26), // Color of the circle
        ),
        child: isLoading
            ? Padding(
                padding: const EdgeInsets.all(4.0),
                child: const AppCircularProgressIndicator(
                  strokeWidth: 2,
                ),
              )
            : assetIcon != null
                ? Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: ImageLoader.fromAsset(
                      assetIcon!,
                      color: iconColor,
                    ),
                  )
                : Icon(
                    icon,
                    color: isDisabled ? Colors.grey : AppColors.white,
                    size: size.iconSize,
                  ), // Back button icon
      ),
      onPressed: isDisabled || isLoading ? null : onPressed,
    );
  }
}

enum TransParentRoundIconButtonSize {
  small(size: 40, iconSize: 18),
  large(size: 54, iconSize: 24);

  final double size;
  final double iconSize;
  const TransParentRoundIconButtonSize({required this.size, required this.iconSize});
}
