import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';

class ExpandableVolumeButton extends StatefulWidget {
  final Function(bool isEnabled)? onPressed;
  final ValueChanged<double>? onVolumeChanged;
  final IconData icon;
  final Color? backgroundColor;
  final bool isDisabled;
  final double initialVolume;
  final bool isLoading;
  final bool isVertical;

  const ExpandableVolumeButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.onVolumeChanged,
    this.backgroundColor,
    this.isDisabled = true,
    this.initialVolume = 1.0,
    this.isLoading = false,
    this.isVertical = true,
  });

  @override
  State<ExpandableVolumeButton> createState() => _ExpandableVolumeButtonState();
}

class _ExpandableVolumeButtonState extends State<ExpandableVolumeButton> {
  bool isExpanded = false;
  late bool isDisabled;
  late double currentVolume;

  @override
  void initState() {
    super.initState();
    isDisabled = widget.isDisabled;
    currentVolume = widget.initialVolume;
  }

  @override
  void didUpdateWidget(covariant ExpandableVolumeButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (isDisabled != widget.isDisabled) {
      isDisabled = widget.isDisabled;
    }
    if (oldWidget.initialVolume != widget.initialVolume) {
      currentVolume = widget.initialVolume;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return AppCircularProgressIndicator();
    }
    return GestureDetector(
      onTap: () {
        setState(() {
          isDisabled = !isDisabled;
        });
        widget.onPressed?.call(isDisabled);
      },
      onLongPressStart: (_) {
        if (!isDisabled) {
          setState(() {
            isExpanded = true;
          });
        }
      },
      onLongPressMoveUpdate: (details) {
        if (isExpanded) {
          final RenderBox box = context.findRenderObject() as RenderBox;
          final localPosition = box.globalToLocal(details.globalPosition);

          final newVolume = widget.isVertical
              ? (1.0 - (localPosition.dy / 200)).clamp(0.0, 1.0)
              : (localPosition.dx / 200).clamp(0.0, 1.0);

          setState(() {
            currentVolume = newVolume;
          });
        }
      },
      onLongPressEnd: (_) {
        if (isExpanded) {
          widget.onVolumeChanged?.call(double.parse(currentVolume.toStringAsFixed(2)));
          setState(() {
            isExpanded = false;
          });
        }
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          AnimatedContainer(
            padding: EdgeInsets.symmetric(
              vertical: widget.isVertical ? 12 : 8,
              horizontal: widget.isVertical ? 8 : 12,
            ),
            duration: const Duration(milliseconds: 300),
            width: widget.isVertical ? 40 : (isExpanded ? 200 : 40),
            height: widget.isVertical ? (isExpanded ? 200 : 40) : 40,
            decoration: BoxDecoration(
              color: isDisabled ? AppColors.white20 : (widget.backgroundColor ?? AppColors.white26),
              borderRadius: BorderRadius.circular(30),
            ),
            alignment: widget.isVertical ? Alignment.bottomCenter : Alignment.centerLeft,
            child: isExpanded
                ? Container(
                    width: widget.isVertical ? 30 : 200 * currentVolume,
                    height: widget.isVertical ? 200 * currentVolume : 20,
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                  )
                : null,
          ),
          AnimatedOpacity(
            duration: const Duration(milliseconds: 300),
            opacity: isExpanded ? 0.0 : 1.0,
            child: Stack(
              alignment: Alignment.center,
              children: [
                CircularProgressIndicator(
                  value: currentVolume,
                  strokeWidth: 4,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isDisabled ? Colors.grey : AppColors.white,
                  ),
                  backgroundColor: isDisabled ? AppColors.white20 : (widget.backgroundColor ?? AppColors.white26),
                ),
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: isExpanded ? 60 : 40,
                  width: isExpanded ? 60 : 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDisabled ? AppColors.white20 : (widget.backgroundColor ?? AppColors.white26),
                  ),
                  child: Icon(
                    widget.icon,
                    color: isDisabled ? Colors.grey : AppColors.white,
                    size: isExpanded ? 28 : 18,
                  ),
                ),
              ],
            ),
          ),
          if (isExpanded)
            Text(
              '${(currentVolume * 100).toInt()}',
              style: TextStyle(
                fontSize: 16,
                color: currentVolume < 0.4 ? AppColors.white : AppColors.black010101,
              ),
            ),
        ],
      ),
    );
  }
}
