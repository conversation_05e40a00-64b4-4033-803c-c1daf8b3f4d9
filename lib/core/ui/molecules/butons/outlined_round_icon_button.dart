import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';

class OutlinedRoundedRectangleButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;

  const OutlinedRoundedRectangleButton(
    this.text, {
    super.key,
    this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (!isLoading) onPressed?.call();
      },
      child: Container(
        padding: const EdgeInsets.all(4.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(32.0),
          border: Border.all(color: AppColors.primary100, width: 2.0),
        ),
        child: Container(
            constraints: const BoxConstraints(minHeight: 60, minWidth: 120), // Set minimum dimensions here
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(32.0),
            color: AppColors.primary100,
          ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                (isLoading)
                    ? const AppCircularProgressIndicator()
                    : Text(
                        text,
                        style: AppTextStyles.text18semiBold,
                      ),
              ],
            )),
      ),
    );
  }
}
