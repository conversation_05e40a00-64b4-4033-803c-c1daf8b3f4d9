import 'dart:async';
import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/atom/gradient_animation.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';

/// Video Processing Dialog with Success/Failure States
/// 
/// Usage:
/// 1. Show dialog: await showVideoProcessingDialog(context);
/// 2. On success: showVideoProcessingSuccess(); (auto-closes after 5 seconds)
/// 3. On failure: showVideoProcessingFailure(); (user must press OK to close)
/// 4. Manual dismiss: dismissVideoProcessingDialog(context);
/// 
/// Note: Uses controller-based state management to avoid GlobalKey conflicts.

enum VideoProcessingState {
  processing,
  success,
  failure,
}

class VideoProcessingDialog extends StatefulWidget {
  final VoidCallback? onComplete;
  final VideoProcessingController controller;

  const VideoProcessingDialog({
    super.key,
    this.onComplete,
    required this.controller,
  });

  @override
  State<VideoProcessingDialog> createState() => _VideoProcessingDialogState();
}

class _VideoProcessingDialogState extends State<VideoProcessingDialog> with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  Timer? _messageTimer;
  Timer? _autoCloseTimer;
  int _currentMessageIndex = 0;
  VideoProcessingState _currentState = VideoProcessingState.processing;

  final List<String> _messages = [
    'Crafting your amazing video...',
    'Adding some magic touches...',
    'Almost ready to shine...',
  ];

  @override
  void initState() {
    super.initState();
    widget.controller._setState(this);
    _initializeAnimations();
    _startMessageRotation();
  }

  void _initializeAnimations() {
    // Scale animation for entrance
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );

    // Fade animation for entrance/exit
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    );


    // Start animations
    _fadeController.forward();
    _scaleController.forward();
  }

  void _startMessageRotation() {
    _messageTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted && _currentState == VideoProcessingState.processing) {
        setState(() {
          _currentMessageIndex = (_currentMessageIndex + 1) % _messages.length;
        });
      }
    });
  }

  void showSuccess() {
    if (!mounted) return;
    
    _messageTimer?.cancel();
    _autoCloseTimer?.cancel(); // Cancel any existing timer
    
    if (mounted) {
      setState(() {
        _currentState = VideoProcessingState.success;
      });
      
      // Auto close after 5 seconds for success
      _autoCloseTimer = Timer(const Duration(seconds: 5), () {
        if (mounted) {
          _closeDialog();
        }
      });
    }
  }

  void showFailure() {
    if (!mounted) return;
    
    _messageTimer?.cancel();
    _autoCloseTimer?.cancel(); // Cancel any existing timer
    
    if (mounted) {
      setState(() {
        _currentState = VideoProcessingState.failure;
      });
    }
  }

  void _closeDialog() {
    if (mounted && Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }
  }

  Widget _buildContent() {
    switch (_currentState) {
      case VideoProcessingState.processing:
        return _buildProcessingContent();
      case VideoProcessingState.success:
        return _buildSuccessContent();
      case VideoProcessingState.failure:
        return _buildFailureContent();
    }
  }

  Widget _buildProcessingContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Video creation animation
        RippleAnimation(
          size: 120,
          primaryColor: AppColors.brightPink,
          secondaryColor: AppColors.brightPurple,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: AppGradients.gradientPurpleTeal,
            ),
            child: const Icon(
              Icons.videocam,
              color: AppColors.white,
              size: 30,
            ),
          ),
        ),
        const SizedBox(height: 40),
        // Title
        Text(
          'Publishing your recording',
          style: AppTextStyles.text32Bold.copyWith(
            fontFamily: AppFonts.iceland,
            color: AppColors.white,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        // Rotating messages
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 500),
          child: Text(
            _messages[_currentMessageIndex],
            key: ValueKey(_currentMessageIndex),
            style: AppTextStyles.text18regular.copyWith(
              fontFamily: AppFonts.inter,
              color: AppColors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 40),
        // Warning message
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.black010101.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primary100.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.primary100,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Please don\'t close the app while we are publishing your recording',
                  style: AppTextStyles.text14medium.copyWith(
                    color: AppColors.white,
                    fontFamily: AppFonts.inter,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSuccessContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Success icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: AppGradients.gradientPurpleTeal,
          ),
          child: const Icon(
            Icons.check,
            color: AppColors.white,
            size: 40,
          ),
        ),
        const SizedBox(height: 40),
        // Success title
        Text(
          'Recording Published!',
          style: AppTextStyles.text32Bold.copyWith(
            fontFamily: AppFonts.iceland,
            color: AppColors.white,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        // Success message
        Text(
          'Your recording has been successfully published and is now live!',
          style: AppTextStyles.text18regular.copyWith(
            fontFamily: AppFonts.inter,
            color: AppColors.white70,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 40),
        // OK button
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: _closeDialog,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                gradient: AppGradients.gradientPurpleTeal,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  'OK',
                  style: AppTextStyles.text16semiBold.copyWith(
                    fontFamily: AppFonts.iceland,
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFailureContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Error icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.red.withValues(alpha: 0.2),
            border: Border.all(
              color: Colors.red,
              width: 2,
            ),
          ),
          child: const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 40,
          ),
        ),
        const SizedBox(height: 40),
        // Error title
        Text(
          'Publishing Failed',
          style: AppTextStyles.text32Bold.copyWith(
            fontFamily: AppFonts.iceland,
            color: AppColors.white,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        // Error message
        Text(
          'We encountered an issue while publishing your recording. Please try again later.',
          style: AppTextStyles.text18regular.copyWith(
            fontFamily: AppFonts.inter,
            color: AppColors.white70,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 40),
        // OK button
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: _closeDialog,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                gradient: AppGradients.gradientPurpleTeal,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  'OK',
                  style: AppTextStyles.text16semiBold.copyWith(
                    fontFamily: AppFonts.iceland,
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _messageTimer?.cancel();
    _autoCloseTimer?.cancel();
    _scaleController.dispose();
    _fadeController.dispose();
    widget.controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final dialogWidth = screenSize.width * 0.75;
    final dialogHeight = screenSize.height * 0.75;

    return PopScope(
      canPop: false, // Prevent dismissal
      child: Material(
        color: Colors.transparent,
        child: AnimatedBuilder(
          animation: _fadeAnimation,
          builder: (context, child) {
            return Opacity(
              opacity: _fadeAnimation.value,
              child: ColoredBox(
                color: AppColors.black010101.withValues(alpha: 0.8),
                child: Center(
                  child: AnimatedBuilder(
                    animation: _scaleAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _scaleAnimation.value,
                        child: AppGradientContainer(
                          width: dialogWidth,
                          height: dialogHeight,
                          borderGradient: AppGradients.gradientPinkBlueBorder,
                          borderRadius: BorderRadius.circular(24),
                          gradient: AppGradients.gradientBlackTeal,
                          child: Padding(
                            padding: const EdgeInsets.all(32.0),
                            child: _buildContent(),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

// Static reference to current dialog controller
VideoProcessingController? _currentController;
bool _isDialogShowing = false;

// Controller class to manage dialog state
class VideoProcessingController {
  _VideoProcessingDialogState? _state;
  bool _isDisposed = false;
  
  void _setState(_VideoProcessingDialogState state) {
    if (!_isDisposed) {
      _state = state;
    }
  }
  
  void showSuccess() {
    if (!_isDisposed) {
      _state?.showSuccess();
    }
  }
  
  void showFailure() {
    if (!_isDisposed) {
      _state?.showFailure();
    }
  }
  
  void dispose() {
    _isDisposed = true;
    _state = null;
  }
}

// Helper function to show the dialog
Future<void> showVideoProcessingDialog(BuildContext context) {
  // Prevent multiple dialogs from being shown
  if (_isDialogShowing) {
    return Future.value();
  }
  
  _isDialogShowing = true;
  _currentController = VideoProcessingController();
  
  return showDialog(
    context: context,
    barrierDismissible: false,
    barrierColor: Colors.transparent,
    builder: (context) => VideoProcessingDialog(controller: _currentController!),
  ).then((_) {
    _currentController?.dispose();
    _currentController = null;
    _isDialogShowing = false;
  });
}

// Helper function to show success state
void showVideoProcessingSuccess() {
  if (!_isDialogShowing || _currentController == null) return;
  
  try {
    _currentController?.showSuccess();
  } catch (e) {
    // Silently handle any errors to prevent crashes
    debugPrint('Error showing video processing success: $e');
  }
}

// Helper function to show failure state
void showVideoProcessingFailure() {
  if (!_isDialogShowing || _currentController == null) return;
  
  try {
    _currentController?.showFailure();
  } catch (e) {
    // Silently handle any errors to prevent crashes
    debugPrint('Error showing video processing failure: $e');
  }
}

// Helper function to dismiss the dialog
void dismissVideoProcessingDialog(BuildContext context) {
  if (_isDialogShowing && Navigator.canPop(context)) {
    Navigator.of(context).pop();
  }
}
