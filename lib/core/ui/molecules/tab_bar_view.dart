import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';

class TabBars<T> extends StatefulWidget {
  final List<T> values;
  final Widget? child;
  final Function(int)? onTap;
  final int? initialIndex;
  final bool isBorderRound;
  final EdgeInsets? tabBarPadding;
  final Color selectedItemColor;
  final Color unselectedItemColor;
  final TextStyle? tabTextStyle;

  const TabBars({
    super.key,
    required this.values,
    required this.child,
    this.onTap,
    this.initialIndex,
    this.isBorderRound = true,
    this.tabBarPadding = EdgeInsets.zero,
    this.selectedItemColor = AppColors.primary100,
    this.unselectedItemColor = AppColors.white70,
    this.tabTextStyle = AppTextStyles.text18semiBold,
  });

  @override
  State<TabBars<T>> createState() => _TabBarsState<T>();
}

class _TabBarsState<T> extends State<TabBars<T>> {
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: widget.initialIndex ?? 0,
      length: widget.values.length,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: widget.tabBarPadding,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.isBorderRound ? 4 : 0),
            ),
            child: TabBar(
              labelColor: widget.selectedItemColor,
              indicatorColor: widget.selectedItemColor,
              labelPadding: EdgeInsets.zero,
              dividerColor: AppColors.white20.withAlpha((0.1 * 255).toInt()),
              unselectedLabelColor: widget.unselectedItemColor,
              labelStyle: widget.tabTextStyle?.copyWith(fontFamily: AppFonts.iceland),
              onTap: widget.onTap,
              tabs: [for (final tab in widget.values) TabWidget(tabName: tab.toString())],
            ),
          ),
          widget.child ?? const SizedBox(),
        ],
      ),
    );
  }
}

class TabWidget extends StatelessWidget {
  final String tabName;
  const TabWidget({super.key, required this.tabName});

  @override
  Widget build(BuildContext context) {
    return Tab(height: 34, text: tabName, iconMargin: EdgeInsets.zero);
  }
}
