import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';

class RenderingErrorWidget extends StatelessWidget {
  final FlutterErrorDetails errorDetails;

  const RenderingErrorWidget({super.key, required this.errorDetails});

  @override
  Widget build(BuildContext context) {
    return MeloScaffold(
      showAppBar: false,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Oops! Something went wrong.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              const Text(
                'Please be patient we are trying hard to fix it. Try again after some time.',
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
