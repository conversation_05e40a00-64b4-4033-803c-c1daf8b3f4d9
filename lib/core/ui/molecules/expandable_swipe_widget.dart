import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/butons/transparent_round_icon_button.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class ExpandableSwipeWidget extends StatefulWidget {
  final double collapsedHeight;
  final double expandedHeight;
  final List<ExpandableSwipeWidgetModel> items;
  final bool isLoading;

  const ExpandableSwipeWidget({
    super.key,
    this.collapsedHeight = 88,
    this.expandedHeight = 230,
    required this.items,
    this.isLoading = false,
  });

  @override
  State<ExpandableSwipeWidget> createState() => _ExpandableSwipeWidgetState();
}

class _ExpandableSwipeWidgetState extends State<ExpandableSwipeWidget> {
  bool _isExpanded = false;
  double _currentHeight = 0;
  double _dragStartY = 0.0;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _currentHeight = widget.collapsedHeight;
  }

  void _handleDragStart(DragStartDetails details) {
    setState(() {
      _dragStartY = details.globalPosition.dy;
      _isDragging = true;
    });
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;

    final dragDistance = details.globalPosition.dy - _dragStartY;
    if (dragDistance.abs() > 50) {
      setState(() {
        _isExpanded = dragDistance > 0;
        _currentHeight = _isExpanded ? widget.expandedHeight : widget.collapsedHeight;
        _isDragging = false;
      });
    }
  }

  void _handleDragEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onVerticalDragStart: (details) {
        if (_isExpanded) {
          if (details.globalPosition.dy < widget.expandedHeight) {
            return;
          }
        }
        _handleDragStart(details);
      },
      onVerticalDragUpdate: _isDragging ? _handleDragUpdate : null,
      onVerticalDragEnd: _isDragging ? _handleDragEnd : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        height: _currentHeight,
        child: AppGradientContainer(
          gradient: AppGradients.gradientBlueBackground,
          borderGradient: AppGradients.gradientBlackPinkBlueBorder,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(30),
            bottomRight: Radius.circular(30),
          ),
          enableBlur: true,
          borderWidth: 2,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(8, 16, 8, 0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      for (final item in widget.items)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: ExpandedItem(
                            item: item,
                            isExpanded: _isExpanded,
                            isLoading: widget.isLoading,
                          ),
                        ),
                    ],
                  ),
                ),
                AnimatedRotation(
                  duration: const Duration(milliseconds: 300),
                  turns: _isExpanded ? 0.5 : 0,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                    child: RotatedBox(
                      quarterTurns: -1,
                      child: ImageLoader.fromAsset(
                        AssetPaths.backButton,
                        color: AppColors.white,
                        height: 8,
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ExpandedItem extends StatefulWidget {
  final ExpandableSwipeWidgetModel item;
  final bool isExpanded;
  final bool isLoading;
  const ExpandedItem({super.key, required this.item, this.isExpanded = false, this.isLoading = false});

  @override
  State<ExpandedItem> createState() => _ExpandedItemState();
}

class _ExpandedItemState extends State<ExpandedItem> {
  double _initialVolume = 0.0;
  bool _isDisabled = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initialVolume = widget.item.initialVolume;
    _isDisabled = widget.item.isDisabled;
    _isLoading = widget.isLoading;
  }

  @override
  void didUpdateWidget(covariant ExpandedItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.item.initialVolume != widget.item.initialVolume) {
      _initialVolume = widget.item.initialVolume;
    }
    _isDisabled = widget.item.isDisabled;
    _isLoading = widget.isLoading;
  }

  @override
  Widget build(BuildContext context) {
    final itemColor = _isDisabled ? AppColors.grey565555 : AppColors.white;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: AnimatedOpacity(
            opacity: widget.isExpanded ? 1.0 : 0.0,
            duration: Duration(milliseconds: 300),
            child: Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: CustomSlider(
                initialVolume: _initialVolume,
                sliderColor: _isDisabled ? AppColors.grey565555 : AppColors.purpleD28EFF,
                trackColor: AppColors.grey5655553D,
                onChanged: _isLoading || widget.item.onVolumeChanged == null
                    ? null
                    : (value) {
                        setState(() {
                          _initialVolume = value;
                        });
                      },
                onChangedEnd: _isLoading || widget.item.onVolumeChanged == null
                    ? null
                    : (value) {
                        if (_isDisabled == false) widget.item.onVolumeChanged?.call(value);
                      },
              ),
            ),
          ),
        ),
        AppGradientContainer(
          gradient: _isDisabled ? AppGradients.gradientPinkPurpleLightTransparent : AppGradients.gradientPinkPurpleTransparent,
          borderGradient: AppGradients.gradientPinkBorder,
          borderRadius: BorderRadius.circular(32),
          borderWidth: 1,
          height: 36,
          width: 36,
          shape: BoxShape.circle,
          child: TransParentRoundIconButton(
            assetIcon: widget.item.iconPath,
            backgroundColor: Colors.transparent,
            iconColor: itemColor,
            isLoading: _isLoading,
            onPressed: () {
              if (widget.item.onPressed != null) {
                setState(() {
                  _isDisabled = !_isDisabled;
                });
                if (_initialVolume != 0 && _isDisabled == false) {
                  widget.item.onVolumeChanged?.call(_initialVolume);
                } else {
                  widget.item.onPressed?.call(_isDisabled);
                }
              } else {
                DI().resolve<AppToast>().showToast('🎵 Coming soon! This feature is still practicing its scales 🎹');
              }
            },
          ),
        ),
        Text(
          widget.item.label,
          style: AppTextStyles.text10regular.copyWith(
            fontFamily: AppFonts.iceland,
            color: itemColor,
          ),
        )
      ],
    );
  }
}

class ExpandableSwipeWidgetModel {
  final String iconPath;
  final double initialVolume;
  final void Function(double)? onVolumeChanged;
  final void Function(bool)? onPressed;
  final bool isDisabled;
  final String label;
  final String title;

  ExpandableSwipeWidgetModel({
    required this.iconPath,
    this.initialVolume = 1.0,
    required this.onVolumeChanged,
    required this.onPressed,
    this.isDisabled = false,
    required this.label,
    required this.title,
  });
}

class CustomSlider extends StatefulWidget {
  final double initialVolume;
  final double max = 1.0;
  final Color sliderColor;
  final Color trackColor;
  final ValueChanged<double>? onChanged;
  final ValueChanged<double>? onChangedEnd;

  const CustomSlider({
    super.key,
    this.initialVolume = 0.0,
    required this.sliderColor,
    required this.trackColor,
    this.onChanged,
    this.onChangedEnd,
  });

  @override
  State<CustomSlider> createState() => _CustomSliderState();
}

class _CustomSliderState extends State<CustomSlider> {
  late double _currentSliderValue;
  double _lastDragY = 0;

  @override
  void initState() {
    super.initState();
    _currentSliderValue = (widget.initialVolume > widget.max) ? widget.max : widget.initialVolume;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onVerticalDragStart: (details) {
          _lastDragY = details.localPosition.dy;
        },
        onVerticalDragUpdate: (details) {
          setState(() {
            final dragDelta = _lastDragY - details.localPosition.dy;
            _lastDragY = details.localPosition.dy;
            final valueDelta = (dragDelta / constraints.maxHeight) * widget.max;
            _currentSliderValue = (_currentSliderValue + valueDelta).clamp(0.0, widget.max);
            widget.onChanged?.call(_currentSliderValue);
          });
        },
        onVerticalDragEnd: (details) {
          widget.onChangedEnd?.call(_currentSliderValue);
        },
        child: SizedBox(
          width: 40.0,
          height: double.maxFinite,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 19.0),
            child: Container(
              width: 2.0,
              height: double.maxFinite,
              decoration: BoxDecoration(
                color: widget.trackColor,
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.bottomCenter,
                heightFactor: widget.initialVolume,
                child: Container(
                  decoration: BoxDecoration(
                    color: widget.sliderColor,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
}
