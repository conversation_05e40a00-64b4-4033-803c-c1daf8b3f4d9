import 'package:flutter/widgets.dart';
import 'package:melodyze/core/ui/atom/app_slider.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';

class SliderWithTime extends StatelessWidget {
  final double value;
  final double max;
  final Duration? currentDuration;
  final Duration? remainingDuration;
  final bool showTimer;
  final ValueChanged<double>? onChanged;
  final Color? activeColor;
  final double thumbRadius;

  const SliderWithTime({
    super.key,
    required this.value,
    required this.max,
    this.onChanged,
    this.currentDuration,
    this.remainingDuration,
    this.showTimer = true,
    this.activeColor,
    this.thumbRadius = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppSlider(
          value: value,
          max: max,
          onChanged: onChanged,
          gestureHitAreaHeight: 24,
          activeColor: activeColor,
          thumbRadius: thumbRadius,
        ),
        if (showTimer) ...[
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                currentDuration?.toMinuteSecondTimeString() ?? '--:--',
                style: AppTextStyles.text12regular,
              ),
              Text(
                remainingDuration?.toMinuteSecondTimeString() ?? '--:--',
                style: AppTextStyles.text12regular,
              ),
            ],
          ),
        ],
      ],
    );
  }
}
