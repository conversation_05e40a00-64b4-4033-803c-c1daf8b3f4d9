import 'package:keep_screen_on/keep_screen_on.dart';

class ScreenUtils {
  ScreenUtils._();

  /// Enables the screen to stay on.
  ///
  /// Returns a [Future] that resolves to `true` if the screen was successfully
  /// set to stay on, and `false` otherwise.
  static Future<bool> keepScreenOn() {
    return KeepScreenOn.turnOn();
  }

  ///
  /// Stop keeping the screen on.
  ///
  /// Returns a [Future] that resolves to `true` if the screen was previously
  /// being kept on and `false` if it was not.
  ///
  static Future<bool> keepScreenOff() {
    return KeepScreenOn.turnOff();
  }
}
