import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';

class HelloName extends StatelessWidget {
  final String name;

  const HelloName({super.key, required this.name});

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        text: 'Hello ',
        style: AppTextStyles.text28Bold,
        children: [
          TextSpan(
            text: '$name!',
            style: AppTextStyles.text28Bold.copyWith(
              color: AppColors.brightPink,
              fontFamily: AppFonts.inter,
            ),
          ),
        ],
      ),
    );
  }
}
