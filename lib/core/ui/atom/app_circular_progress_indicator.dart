import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';

class AppCircularProgressIndicator extends StatelessWidget {
  final bool? enabled;
  final double strokeWidth;
  const AppCircularProgressIndicator({
    super.key,
    this.enabled,
    this.strokeWidth = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    final Color textColor = (enabled ?? true) ? AppColors.white : Colors.grey[400]!;
    return CircularProgressIndicator(
      strokeWidth: strokeWidth,
      valueColor: AlwaysStoppedAnimation<Color>(textColor), // Use textColor for the progress indicator
    );
  }
}
