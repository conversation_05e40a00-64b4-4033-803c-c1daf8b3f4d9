import 'dart:ui' show ImageFilter;

import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_border.dart';

class AppGradientContainer extends StatelessWidget {
  final Widget? child;
  final double? height;
  final double? width;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final AlignmentGeometry? alignment;
  final BoxConstraints? constraints;
  final Matrix4? transform;
  final BorderRadius? borderRadius;
  final LinearGradient? gradient;
  final LinearGradient? borderGradient;
  final double borderWidth;
  final bool enableBlur;
  final double blurSigma;
  final Color? backgroundColor;
  final bool enableForegroundDecoration;
  final BoxShape shape;

  const AppGradientContainer({
    super.key,
    this.child,
    this.height,
    this.width,
    this.padding,
    this.margin,
    this.alignment,
    this.constraints,
    this.transform,
    this.borderRadius,
    this.gradient,
    this.borderGradient,
    this.borderWidth = 1.0,
    this.enableBlur = false,
    this.blurSigma = 10.0,
    this.backgroundColor,
    this.enableForegroundDecoration = false,
    this.shape = BoxShape.rectangle,
  });

  BorderRadius get radius => borderRadius ?? BorderRadius.circular(18);

  BoxDecoration get decoration => BoxDecoration(
        borderRadius: radius,
        gradient: gradient,
        border: borderGradient != null
            ? AppGradientBoxBorder(
                gradient: borderGradient!,
                width: borderWidth,
                shape: shape,
              )
            : null,
      );

  @override
  Widget build(BuildContext context) {
    Widget containerWidget = Container(
      height: height,
      width: width,
      padding: padding,
      margin: margin,
      alignment: alignment,
      constraints: constraints,
      transform: transform,
      foregroundDecoration: enableForegroundDecoration
          ? decoration
          : BoxDecoration(
              color: backgroundColor,
              borderRadius: radius,
              shape: shape,
            ),
      decoration: enableForegroundDecoration ? null : decoration,
      child: child,
    );

    if (enableBlur) {
      containerWidget = ClipRRect(
        borderRadius: radius,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blurSigma, sigmaY: blurSigma),
          child: containerWidget,
        ),
      );
    }

    return containerWidget;
  }
}
