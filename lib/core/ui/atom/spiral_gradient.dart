import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';

class SpiralGradient extends StatelessWidget {

  const SpiralGradient({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: RadialGradient(
          center: Alignment(0.8, 0.6),
          colors: [
            AppColors.purple2,
            AppColors.purple,
            AppColors.primary50,
          ],
          radius: 2.0,
        ),
      ),
    );
  }
}
