import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';

class AppSlider extends StatefulWidget {
  final double value;
  final double max;
  final double gestureHitAreaHeight;
  final ValueChanged<double>? onChanged;
  final ValueChanged<double>? onChangeStart;
  final ValueChanged<double>? onChangeEnd;
  final Color? activeColor;
  final double thumbRadius;
  final bool isRounded;
  final double trackHeight;

  const AppSlider({
    super.key,
    required this.value,
    this.max = 1.0,
    this.onChanged,
    this.onChangeStart,
    this.onChangeEnd,
    this.gestureHitAreaHeight = 20,
    this.activeColor,
    this.thumbRadius = 0,
    this.isRounded = true,
    this.trackHeight = 2,
  });

  @override
  State<AppSlider> createState() => _SmoothAppSliderState();
}

class _SmoothAppSliderState extends State<AppSlider> {
  bool _isDragging = false;
  double _currentValue = 0;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.value;
  }

  @override
  void didUpdateWidget(covariant AppSlider oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!_isDragging && widget.value != _currentValue) {
      _currentValue = widget.value;
    }
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Container(
        height: widget.gestureHitAreaHeight,
        alignment: Alignment.center,
        child: SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: widget.trackHeight,
            thumbShape: RoundSliderThumbShape(enabledThumbRadius: widget.thumbRadius),
            overlayShape: SliderComponentShape.noThumb,
            trackShape: _CustomSliderTrackShape(trackHeight: widget.trackHeight),
          ),
          child: Slider(
            activeColor: widget.activeColor ?? AppColors.white,
            inactiveColor: AppColors.white20,
            value: _currentValue.clamp(0, widget.max),
            max: widget.max,
            onChanged: (val) {
              setState(() => _currentValue = val);
              widget.onChanged?.call(val);
            },
            onChangeStart: (val) {
              _isDragging = true;
              widget.onChangeStart?.call(val);
            },
            onChangeEnd: (val) {
              _isDragging = false;
              widget.onChangeEnd?.call(val);
            },
          ),
        ),
      ),
    );
  }
}

class _CustomSliderTrackShape extends RoundedRectSliderTrackShape {
  final double trackHeight;

  _CustomSliderTrackShape({this.trackHeight = 2});

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
    double additionalActiveTrackHeight = 0,
  }) {
    super.paint(
      context,
      offset,
      parentBox: parentBox,
      sliderTheme: sliderTheme,
      enableAnimation: enableAnimation,
      textDirection: textDirection,
      thumbCenter: thumbCenter,
      secondaryOffset: secondaryOffset,
      isDiscrete: isDiscrete,
      isEnabled: isEnabled,
      additionalActiveTrackHeight: 0,
    );
  }

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    return Rect.fromLTWH(
      offset.dx,
      offset.dy + (parentBox.size.height - trackHeight) / 2,
      parentBox.size.width,
      trackHeight,
    );
  }
}
