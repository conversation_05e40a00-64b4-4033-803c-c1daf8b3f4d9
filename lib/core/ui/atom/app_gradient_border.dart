import 'package:flutter/material.dart';

class AppGradientBoxBorder extends BoxBorder {
  final LinearGradient gradient;
  final double width;
  final BoxShape shape;
  final BorderRadius? borderRadius;

  const AppGradientBoxBorder({
    required this.gradient,
    this.shape = BoxShape.rectangle,
    this.width = 1.0,
    this.borderRadius,
  });

  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.all(width);

  @override
  BoxBorder scale(double t) => this;

  @override
  void paint(
    Canvas canvas,
    Rect rect, {
    TextDirection? textDirection,
    BoxShape shape = BoxShape.rectangle,
    BorderRadius? borderRadius,
  }) {
    final Paint paint = Paint()
      ..shader = gradient.createShader(rect)
      ..strokeWidth = width
      ..style = PaintingStyle.stroke;

    if (this.shape == BoxShape.circle) {
      canvas.drawCircle(
        rect.center,
        (rect.width - width) / 2,
        paint,
      );
    } else {
      final Path path = Path();

      borderRadius ??= this.borderRadius;

      if (borderRadius != null) {
        if (borderRadius.topLeft == borderRadius.topRight && borderRadius.topRight == borderRadius.bottomRight && borderRadius.bottomRight == borderRadius.bottomLeft) {
          // For circular/equal border radius, use a simpler RRect path
          final RRect rrect = RRect.fromRectAndRadius(rect, borderRadius.topLeft);
          path.addRRect(rrect);
        } else {
          final bool showTop = borderRadius.topLeft.x > 0 || borderRadius.topRight.x > 0;
          final bool showRight = borderRadius.topRight.x > 0 || borderRadius.bottomRight.x > 0;
          final bool showBottom = borderRadius.bottomLeft.x > 0 || borderRadius.bottomRight.x > 0;
          final bool showLeft = borderRadius.topLeft.x > 0 || borderRadius.bottomLeft.x > 0;

          if (showTop) {
            path.moveTo(rect.left + borderRadius.topLeft.x, rect.top);
            path.lineTo(rect.right - borderRadius.topRight.x, rect.top);
            if (borderRadius.topRight.x > 0) {
              path.arcToPoint(
                Offset(rect.right, rect.top + borderRadius.topRight.y),
                radius: borderRadius.topRight,
                clockwise: true,
              );
            }
          }

          if (showRight) {
            if (!showTop) {
              path.moveTo(rect.right, rect.top + borderRadius.topRight.y);
            }
            path.lineTo(rect.right, rect.bottom - borderRadius.bottomRight.y);
            if (borderRadius.bottomRight.x > 0) {
              path.arcToPoint(
                Offset(rect.right - borderRadius.bottomRight.x, rect.bottom),
                radius: borderRadius.bottomRight,
                clockwise: true,
              );
            }
          }

          if (showBottom) {
            if (!showRight) {
              path.moveTo(rect.right - borderRadius.bottomRight.x, rect.bottom);
            }
            path.lineTo(rect.left + borderRadius.bottomLeft.x, rect.bottom);
            if (borderRadius.bottomLeft.x > 0) {
              path.arcToPoint(
                Offset(rect.left, rect.bottom - borderRadius.bottomLeft.y),
                radius: borderRadius.bottomLeft,
                clockwise: true,
              );
            }
          }

          if (showLeft) {
            if (!showBottom) {
              path.moveTo(rect.left, rect.bottom - borderRadius.bottomLeft.y);
            }
            path.lineTo(rect.left, rect.top + borderRadius.topLeft.y);
            if (borderRadius.topLeft.x > 0) {
              path.arcToPoint(
                Offset(rect.left + borderRadius.topLeft.x, rect.top),
                radius: borderRadius.topLeft,
                clockwise: true,
              );
            }
          }
        }
      } else {
        path.addRect(rect);
      }

      canvas.drawPath(path, paint);
    }
  }

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()..addRect(rect);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    return Path()..addRect(rect);
  }

  @override
  BorderSide get bottom => BorderSide(
        width: width,
        color: Colors.transparent,
      );

  @override
  bool get isUniform => true;

  @override
  BorderSide get top => BorderSide(
        width: width,
        color: Colors.transparent,
      );
}
