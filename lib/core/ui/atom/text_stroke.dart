import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';

class TextStroke extends StatelessWidget {
  final String label;
  final Color? strokeColor;
  final TextAlign? textAlign;
  final double? fontSize;
  final Color? color;

  const TextStroke(
    this.label, {
    super.key,
    this.strokeColor,
    this.textAlign,
    this.fontSize,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      label,
      style: AppTextStyles.text12semiBold.copyWith(fontSize: fontSize, color: color, shadows: [
        Shadow(
            // bottomLeft
            offset: const Offset(-0.1, -0.1),
            color: strokeColor ?? Colors.grey),
        Shadow(
            // bottomRight
            offset: const Offset(0.1, -0.1),
            color: strokeColor ?? Colors.grey),
        Shadow(
            // topRight
            offset: const Offset(0.1, 0.5),
            color: strokeColor ?? Colors.grey),
        Shadow(
            // topLeft
            offset: const Offset(-0.1, 0.5),
            color: strokeColor ?? Colors.grey),
      ]),
      textAlign: TextAlign.center,
    );
  }
}
