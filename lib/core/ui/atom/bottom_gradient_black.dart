//generate gradient color container

import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';

class BottomGradientBlack extends StatelessWidget {
  final double? height;
  const BottomGradientBlack({super.key, this.height});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 100,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            AppColors.black010101,
            Colors.transparent,
          ],
        ),
      ),
    );
  }
}
