import 'dart:ui';

class AppColors {
  static const white = Color(0xFFFFFFFF);
  static const white5 = Color(0x0DFFFFFF);
  static const white26 = Color(0x26FFFFFF);
  static const white70 = Color(0xB3FFFFFF);
  static const white100 = Color(0xC0FFFFFF);
  static const white50 = Color(0x80FFFFFF);
  static const white20 = Color(0x40FFFFFF);

  static const black = Color(0xFF000000);
  static const black010101 = Color(0xFF010101);
  static const black010101_25 = Color(0x40010101);
  static const black010101_50 = Color(0x80010101);
  static const black010101_75 = Color(0xBF010101);
  static const black1B1B1B = Color(0xFF1B1B1B);
  static const black6B6B6B = Color(0xFF6B6B6B);
  static const black272727 = Color(0xFF272727);

  static const grey100 = Color(0xFF0E0E12);
  static const grey848484 = Color(0xFF848484);
  static const greyText = Color(0xFF737373);
  static const greySlider = Color(0xFF8f8e94);

  static const primary100 = Color(0xFFED2797);
  static const primary50 = Color(0x80ED2797);
  static const primaryAccent = Color(0xFFFD81C7);
  static const pinkGlow = Color(0xFFFE34ED);

  static const purple = Color(0xFF793E70);
  static const purple2 = Color(0xFF954048);

  static const pink = Color(0xFFFF49ED);
  static const pink2 = Color(0xFF7149FF);

  // Gradient Colors
  static const gradientDarkBlue = Color(0x3B080C28);
  static const gradientBlack = Color(0x3B000000);
  static const gradientTurquoise = Color(0x3B19B2C2);

  static const brightPurple = Color(0xFFAF6DFF);
  static const brightPink = Color(0xFFE42DE4);

  static const darkCyan = Color(0xFF05272B);
  static const darkCyan2 = Color.fromARGB(175, 5, 39, 43);
  static const darkBlack = Color(0xFF020209);

  static const Color grey565555 = Color(0xFF565555);
  static const Color grey5655553D = Color(0x5655553D);
  static const Color purpleD28EFF = Color(0xFFD28EFF);

  static const Color grey7E7D7D = Color(0xFF7E7D7D);

  static const Color darkNavyBlue = Color(0xFF000F16);

  static const cyanA400_100 = Color(0xFF00E5FF); // 100% opacity
  static const cyanA400_75 = Color(0xBF00E5FF); // 75% opacity
  static const cyanA400_50 = Color(0x8000E5FF); // 50% opacity
  static const cyanA400_25 = Color(0x4000E5FF); // 25% opacity
  static const cyanA400_10 = Color(0x1A00E5FF); // 10% opacity
  static const cyanA400_5 = Color(0x0D00E5FF); // 5% opacity

  static const magenta_100 = Color(0xFFFF00D0); // 100% opacity
  static const magenta_75 = Color(0xBFFF00D0); // 75% opacity
  static const magenta_50 = Color(0x80FF00D0); // 50% opacity
  static const magenta_25 = Color(0x40FF00D0); // 25% opacity
  static const magenta_10 = Color(0x1AFF00D0); // 10% opacity
  static const magenta_5 = Color(0x0DFF00D0); // 5% opacity

  AppColors._();
}

//Opacity Reference
// 100% — FF
// 95% — F2
// 90% — E6
// 85% — D9
// 80% — CC
// 75% — BF
// 70% — B3
// 65% — A6
// 60% — 99
// 55% — 8C
// 50% — 80
// 45% — 73
// 40% — 66
// 35% — 59
// 30% — 4D
// 25% — 40
// 20% — 33
// 15% — 26
// 10% — 1A
// 5% — 0D
// 0% — 00
