import 'dart:io';

import 'package:flutter/services.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';

class AssetPaths {
  AssetPaths._();

  static const assetsPath = 'assets';
  static const svgPath = '$assetsPath/svg';
  static const pngPath = '$assetsPath/png';
  static const gifPath = '$assetsPath/gif';
  static const videoPath = '$assetsPath/video';
  static const socialButtons = '$svgPath/social_buttons';
  static const filterIcons = '$assetsPath/filter_icons';
  static const splashPath = '$assetsPath/splash';
  static const juceConfigs = '$assetsPath/juce_configs';
  static const fontsPath = '$assetsPath/fonts';

  static const logoSvg = '$svgPath/logo.svg';
  static const logoPng = '$assetsPath/logo/logo.png';

  static const facebook = '$socialButtons/facebook.svg';
  static const google = '$socialButtons/google.svg';
  static const apple = '$socialButtons/apple.svg';
  static const instagram = '$socialButtons/instagram.svg';
  static const tiktok = '$socialButtons/tiktok.svg';

  static const search = '$svgPath/search.svg';
  static const gradientdivider = '$svgPath/divider.svg';

  static const backButton = '$svgPath/back_button.svg';
  static const tickButton = '$svgPath/tick_button.svg';
  static const restartButton = '$svgPath/restart_button.svg';
  static const saveButton = '$svgPath/save_button.svg';
  static const guide = '$pngPath/guide.png';
  static const metronome = '$svgPath/metronome.svg';
  static const mic = '$svgPath/mic.svg';

  static const home = '$svgPath/home.svg';
  static const feed = '$svgPath/feed.svg';
  static const profile = '$svgPath/profile.svg';

  static const microphone = '$pngPath/microphone.png';
  static const headphone = '$pngPath/headphone.png';
  static const discard = '$svgPath/discard.svg';
  static const finalize = '$svgPath/finalize.svg';

  static const catRoundEqualizer = '$gifPath/cat_round_equalizer_bar.gif';
  static const catRoundEqualizerStatic = '$pngPath/cat_round_equalizer_bar.png';

  static const recordMicOff = '$pngPath/recording_mic_off.png';
  static const recordMicOn = '$pngPath/recording_mic_on.png';

  static const filterVolume = '$svgPath/filter_volume.svg';
  static const filterVolumeOff = '$svgPath/filter_volume_off.svg';

  static const recordingSaved = '$svgPath/recording_saved.svg';
  static const catComplete = '$pngPath/cat_complete.png';
  static const musicNote = '$pngPath/music_note.png';

  static const check = '$svgPath/check.svg';

  static const playReel = '$svgPath/play_reel.svg';
  static const pauseReel = '$svgPath/pause_reel.svg';

  static const circlePositive = '$svgPath/circle_positive.svg';
  static const circleNegative = '$svgPath/circle_negetive.svg';

  static const splashScreenLottie = '$splashPath/melo_anime_lottie.json';
  static const splashScreenVdo = '$splashPath/melo_splash.mp4';

  static const genreSequence = '$juceConfigs/genre_sequence.json';
  static const genreMap = '$juceConfigs/genre_map.json';

  // video creation assets
  static const mask1 = '$pngPath/mask_1.png';
  static const mask2 = '$pngPath/mask_2.png';
  static const profileBorder = '$pngPath/profile_border.png';
  static const contentPlay = '$pngPath/content_play_w.png';
  static const brandLogo = '$pngPath/cat_complete.png';
  static const videoWaveform = '$videoPath/content_waveform.mp4';
  static const defaultProfilePic = '$pngPath/default_profile_pic.jpeg';

  static const ethnocentricFontPath = '$fontsPath/ethnocentric_regular.otf';
  static const icelandFontPath = '$fontsPath/iceland_regular.ttf';


  static Future<String> extractAsset(String assetPath) async {
    final documentsDirpath = await PathWrapper.getApplicationDocumentsDirectoryPath();
    String path = "$documentsDirpath/$assetPath";
    ByteData data = await rootBundle.load(assetPath);
    final buffer = data.buffer;
    final file = File(path);
    if (file.existsSync()) {
      return path;
    }
    await file.create(recursive: true);
    await file.writeAsBytes(buffer.asUint8List(data.offsetInBytes, data.lengthInBytes));
    return path;
  }
}
