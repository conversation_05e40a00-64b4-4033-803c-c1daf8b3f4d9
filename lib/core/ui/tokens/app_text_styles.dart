import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';

class AppTextStyles {
  AppTextStyles._();

  static const text14semiBold = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
  );

  static const text28Bold = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.w700,
  );

  static const text32Bold = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w700,
  );

  static const text48Bold = TextStyle(
    fontSize: 48,
    fontWeight: FontWeight.w700,
  );

  static const headline5 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w700,
  );

  static const headline4 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700,
  );

  static const bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w700,
  );

  static const text20regular = TextStyle(
    fontSize: 20,
  );

  static const text18regular = TextStyle(
    fontSize: 18,
    color: AppColors.white70,
  );

  static const text18semiBold = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  static const text14regular = TextStyle(
    fontSize: 14,
  );

  static const text14medium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const text16regular = TextStyle(
    fontSize: 16,
  );

  static const text16semiBold = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static const text16medium = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const text12semiBold = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w600,
  );

  static const text12regular = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
  );
  static const text12medium = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const text24semiBold = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
  );

  static const text22semiBold = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
  );

  static const text26semiBold = TextStyle(
    fontSize: 26,
    fontWeight: FontWeight.w600,
  );

  static const text10medium = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w500,
  );

  static const text10regular = TextStyle(
    fontSize: 10,
  );

  static const text14regularGrey = TextStyle(
    fontSize: 14,
    color: AppColors.white70,
  );

  static const textEthnocentricStyle = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w700,
    fontFamily: AppFonts.ethnocentric,
  );

  static const textOutfit900 = TextStyle(
    fontWeight: FontWeight.w900,
    fontFamily: AppFonts.outfit,
  );
}
