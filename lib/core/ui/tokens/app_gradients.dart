import 'dart:math';

import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';

class AppGradients {
  AppGradients._();

  // background: linear-gradient(180deg, rgba(8, 12, 40, 0.23) 9.45%, rgba(0, 0, 0, 0.23) 69.82%, rgba(25, 178, 194, 0.23) 100%);
  static const LinearGradient gradientBlueBackground = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.gradientDarkBlue,
      AppColors.gradientBlack,
      AppColors.gradientTurquoise,
    ],
    stops: [0.0945, 0.6982, 1.0],
  );

  // background: linear-gradient(180deg, rgba(211, 85, 211, 0.24) 0%, rgba(8, 12, 40, 0.24) 51.5%, rgba(27, 183, 201, 0.24) 100%);
  static const LinearGradient gradientPurpleBackground = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x3FD355D3),
      Color(0x3F080C28),
      Color(0x3F1BB7C9),
    ],
    stops: [0.0, 0.515, 1.0],
  );

  // border-image-source: linear-gradient(180deg, #F74BF7 0%, #080C28 51.5%, #2FE4F9 100%);
  static const LinearGradient gradientPurpleBorder = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFF74BF7),
      Color(0xFF080C28),
      Color(0xFF2FE4F9),
    ],
    stops: [0.0, 0.515, 1.0],
  );

  // background: linear-gradient(180deg, rgba(198, 61, 163, 0.53) 0%, rgba(0, 0, 0, 0.53) 48%, rgba(116, 81, 245, 0.53) 100%);
  static const LinearGradient gradientPinkBackground = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x88C63DA3),
      Color(0x88000000),
      Color(0x887451F5),
    ],
    stops: [0.0, 0.48, 1.0],
  );

  //border-image-source: linear-gradient(180deg, #E339B9 0%, #000000 48%, #7048FF 100%);
  static const LinearGradient gradientPinkBorder = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFE339B9),
      Color(0xFF000000),
      Color(0xFF7048FF),
    ],
    stops: [0.0, 0.48, 1.0],
  );

  // background: linear-gradient(180deg, #421642 0%, #000000 16.5%, #11191C 69.68%, #0D2A2E 100%);
  static const LinearGradient gradientBottomBar = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF421642),
      Color(0xFF000000),
      Color(0xFF11191C),
      Color(0xFF0D2A2E),
    ],
    stops: [0.0, 0.165, 0.6968, 1.0],
  );

  // border-image-source: linear-gradient(180deg, rgba(211, 85, 211, 0.34) 0%, rgba(8, 12, 40, 0.34) 51.5%, rgba(27, 183, 201, 0.34) 100%);
  static const LinearGradient gradientBottomBarBorder = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x56D355D3),
      Color(0x56080C28),
      Color(0x561BB7C9),
    ],
    stops: [0.0, 0.515, 1.0],
  );

  // border-image-source: linear-gradient(180deg, #C63DA3 0%, #000000 48%, #7451F5 100%);
  static const LinearGradient gradientBottomBarMiddleIcon = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFC63DA3),
      Color(0xFF000000),
      Color(0xFF7451F5),
    ],
    stops: [0.0, 0.48, 1.0],
  );
  // background: linear-gradient(180deg, rgba(218, 60, 218, 0.81) 0%, rgba(8, 218, 241, 0.81) 66.96%);
  static const LinearGradient gradientPinkBg = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xCCDA3CDA),
      Color(0xCC08DAF1),
    ],
    stops: [0.0, 0.6696],
  );

  static const LinearGradient gradientPinkBgDisabled = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x4CDA3CDA),
      Color(0x4C08DAF1),
    ],
    stops: [0.0, 0.6696],
  );

    static const LinearGradient commonButtonDisabled = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x4CFFFFFF),
      Color.fromARGB(74, 104, 100, 100),
    ],
    stops: [0.0, 0.5576],
  );

  // background: linear-gradient(180deg, #ED2DED 0%, #22DCF0 100%);
  static const LinearGradient gradientPinkIcon = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFED2DED),
      Color(0xFF22DCF0),
    ],
    stops: [0.0, 1.0],
  );

  // border-image-source: linear-gradient(180deg, #D355D3 0%, #080C28 51.5%, #1BB7C9 100%);
  static const LinearGradient gradientPinkBlueBorder = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFD355D3),
      Color(0xFF080C28),
      Color(0xFF1BB7C9),
    ],
    stops: [0.0, 0.515, 1.0],
  );

  // background: linear-gradient(180deg, #FF8DFD 8.22%, #FFFFFF 39.28%, #FFFFFF 67.02%, #8284FF 92.33%);
  static const LinearGradient gradientPinkPurpleBackground = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFFF8DFD),
      Color(0xFFFFFFFF),
      Color(0xFFFFFFFF),
      Color(0xFF8284FF),
    ],
    stops: [0.0822, 0.3928, 0.6702, 0.9233],
  );

  // background: linear-gradient(180deg, rgba(241, 56, 241, 0.1) 0%, rgba(8, 12, 40, 0.1) 51.5%, rgba(34, 216, 236, 0.1) 100%);
  static const LinearGradient gradientBluePink = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x19F138F1),
      Color(0x19080C28),
      Color(0x1922D8EC),
    ],
    stops: [0.0, 0.515, 1.0],
    transform: GradientRotation(180 * pi / 180),
  );

  //border: 1px solid; border-image-source: linear-gradient(358.02deg, #1ED6FF 1.76%, #0E2323 10.81%, #381038 86.77%, #FF25E2 95.82%);
  static const LinearGradient gradientBluePinkBorder = LinearGradient(
    begin: Alignment.bottomCenter,
    end: Alignment.topCenter,
    colors: [
      Color(0xFF1ED6FF),
      Color(0xFF0E2323),
      Color(0xFF381038),
      Color(0xFFFF25E2),
    ],
    stops: [0.0176, 0.1081, 0.8677, 0.9582],
    transform: GradientRotation(358.02 * pi / 180),
  );

  // background: linear-gradient(180deg, #000000 0%, #FF34D3 45.5%, #34F8FF 100%);
  static const LinearGradient gradientBlackPinkBlue = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF000000),
      Color(0xFFFF34D3),
      Color(0xFF34F8FF),
    ],
    stops: [0.0, 0.455, 1.0],
  );

  //  border-image-source: linear-gradient(180deg, #000000 2%, #D355D3 34.5%, #080C28 68%, #1BA3C9 100%);

  static const LinearGradient gradientBlackPinkBlueBorder = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF000000),
      Color(0xFFD355D3),
      Color(0xFF080C28),
      Color(0xFF1BA3C9),
    ],
    stops: [0.02, 0.345, 0.68, 1.0],
  );

  static const LinearGradient gradientPinkCyanBorder = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFD355D3),
      Color(0xFF080C28),
      Color(0xFF080C28),
      Color(0xFF1BA3C9),
    ],
    stops: [0.0, 0.45, 0.5, 1.0],
  );

  static const RadialGradient radialGradientBlackPinkBlueBorder = RadialGradient(
    colors: [
      Color(0xFFD355D3),
      Color(0xFF080C28),
      Color(0xFF080C28),
      Color(0xFF4D00FF),
    ],
    stops: [0.5, 0.6, 0.7, 1.0],
  );

  static const LinearGradient gradientBlackPinkBlueBlackBorder = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF080C28),
      Color(0xFFD355D3),
      Color(0xFF080C28),
      Color(0xFF1BB7C9),
      Color(0xFF080C28),
    ],
    stops: [0.0, 0.1, 0.5, 0.9, 1.0],
  );

  static const LinearGradient gradientDarkPurpleBackground = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x26FF00FF),
      Color(0x26080C28),
      Color(0x261BB7C9),
    ],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient gradientBlackTeal = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF000000),
      Color(0xFF062B2F),
    ],
    stops: [0.0006, 1.0],
  );

  static const LinearGradient gradientPurpleTeal = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF992199), // #992199 (purple)
      Color(0xFF0A626B), // #0A626B (teal)
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient gradientPinkPurpleTransparent = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x80E339B9), // rgba(227, 57, 185, 0.5)
      Color(0x80000000), // rgba(0, 0, 0, 0.5)
      Color(0x807048FF), // rgba(112, 72, 255, 0.5)
    ],
    stops: [0.0, 0.48, 1.0],
  );
  static const LinearGradient gradientPinkPurpleLightTransparent = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x33E339B9), // rgba(227, 57, 185, 0.2)
      Color(0x33000000), // rgba(0, 0, 0, 0.2)
      Color(0x337048FF), // rgba(112, 72, 255, 0.2)
    ],
    stops: [0.0, 0.48, 1.0],
  );

  // background: linear-gradient(180deg, rgba(211, 85, 211, 0.52) 0%, rgba(8, 12, 40, 0.52) 51.5%, rgba(27, 183, 201, 0.52) 100%);

  static const LinearGradient gradientPurpleTealTransparent = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x85D355D3),
      Color(0x85080C28),
      Color(0x851BB7C9),
    ],
    stops: [0.0, 0.515, 1.0],
  );

  // background: linear-gradient(1.13deg, #008897 -11.41%, #000000 49.85%, #880188 114.89%);
  static const LinearGradient gradientBlackPurpleTeal = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF880188),
      Color(0xFF000000),
      Color(0xFF008897),
    ],
    stops: [0.0, 0.5, 1.0],
  );

  // background: linear-gradient(180deg, #681A68 0%, #3E1748 17.63%, #12192E 47.83%, #073F45 81.73%);

  static const LinearGradient gradientBlackPurpleTealBorder = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF681A68),
      Color(0xFF3E1748),
      Color(0xFF12192E),
      Color(0xFF073F45),
    ],
    stops: [0.0, 0.1763, 0.4783, 0.8173],
  );
}
