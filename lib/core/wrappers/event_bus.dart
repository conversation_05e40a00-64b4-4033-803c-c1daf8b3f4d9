import 'dart:async';

class EventBus {
  final StreamController _streamController = StreamController.broadcast();

  Stream get stream => _streamController.stream;

  void fire(event) {
    _streamController.add(event);
  }

  Stream<T> on<T>() {
    if (T == dynamic) {
      return _streamController.stream as Stream<T>;
    } else {
      return _streamController.stream.where((event) => event is T).cast<T>();
    }
  }

  void dispose() {
    _streamController.close();
  }
}
