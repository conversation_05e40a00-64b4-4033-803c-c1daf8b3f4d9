import 'dart:convert';

import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorageHelper {
  static LocalStorageHelper? _instance;
  SharedPreferences? _prefs;

  LocalStorageHelper._();

  static Future<LocalStorageHelper> createInstance() async {
    if (_instance == null) {
      _instance = LocalStorageHelper._();
      await _instance!._init();
    }
    return _instance!;
  }

  Future<void> _init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // String operations
  Future<bool> setString(String key, String value) async {
    try {
      return await _prefs?.setString(key, value) ?? false;
    } catch (e) {
      logger.e('Error setting string: $e');
      return false;
    }
  }

  String? getString(String key) {
    try {
      return _prefs?.getString(key);
    } catch (e) {
      logger.e('Error getting string: $e');
      return null;
    }
  }

  // Integer operations
  Future<bool> setInt(String key, int value) async {
    try {
      return await _prefs?.setInt(key, value) ?? false;
    } catch (e) {
      logger.e('Error setting int: $e');
      return false;
    }
  }

  int? getInt(String key) {
    try {
      return _prefs?.getInt(key);
    } catch (e) {
      logger.e('Error getting int: $e');
      return null;
    }
  }

  // Double operations
  Future<bool> setDouble(String key, double value) async {
    try {
      return await _prefs?.setDouble(key, value) ?? false;
    } catch (e) {
      logger.e('Error setting double: $e');
      return false;
    }
  }

  double? getDouble(String key) {
    try {
      return _prefs?.getDouble(key);
    } catch (e) {
      logger.e('Error getting double: $e');
      return null;
    }
  }

  // Boolean operations
  Future<bool> setBool(String key, bool value) async {
    try {
      return await _prefs?.setBool(key, value) ?? false;
    } catch (e) {
      logger.e('Error setting bool: $e');
      return false;
    }
  }

  bool? getBool(String key) {
    try {
      return _prefs?.getBool(key);
    } catch (e) {
      logger.e('Error getting bool: $e');
      return null;
    }
  }

  // List operations
  Future<bool> setStringList(String key, List<String> value) async {
    try {
      return await _prefs?.setStringList(key, value) ?? false;
    } catch (e) {
      logger.e('Error setting string list: $e');
      return false;
    }
  }

  List<String>? getStringList(String key) {
    try {
      return _prefs?.getStringList(key);
    } catch (e) {
      logger.e('Error getting string list: $e');
      return null;
    }
  }

  // JSON object operations
  Future<bool> setObject<T>(String key, T value) async {
    try {
      final string = json.encode(value);
      return await setString(key, string);
    } catch (e) {
      logger.e('Error setting object: $e');
      return false;
    }
  }

  T? getObject<T>(String key, T Function(Map<String, dynamic> json) fromJson) {
    try {
      final string = getString(key);
      if (string == null) return null;
      final map = json.decode(string) as Map<String, dynamic>;
      return fromJson(map);
    } catch (e) {
      logger.e('Error getting object: $e');
      return null;
    }
  }

  // Utility operations
  Future<bool> remove(String key) async {
    try {
      return await _prefs?.remove(key) ?? false;
    } catch (e) {
      logger.e('Error removing key: $e');
      return false;
    }
  }

  Future<bool> clear() async {
    try {
      return await _prefs?.clear() ?? false;
    } catch (e) {
      logger.e('Error clearing storage: $e');
      return false;
    }
  }

  bool containsKey(String key) {
    try {
      return _prefs?.containsKey(key) ?? false;
    } catch (e) {
      logger.e('Error checking key: $e');
      return false;
    }
  }

  // Session management
  Future<void> clearSession() async {
    try {
      for (final key in LocalStorageKeys.sessionKeys) {
        await remove(key);
      }
    } catch (e) {
      logger.e('Error clearing session: $e');
    }
  }
}

class LocalStorageKeys {
  LocalStorageKeys._();

  static const List<String> sessionKeys = [userSetDelay];

  static const userSetDelay = 'user_set_delay';
  static const screenshotEnabled = 'screenshot_enabled';
}
