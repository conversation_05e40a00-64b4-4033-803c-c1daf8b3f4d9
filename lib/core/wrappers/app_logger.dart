import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:melodyze/core/wrappers/analytics/crash_logger.dart';
import 'package:melodyze/core/wrappers/injector.dart';

final logger = AppLogger._();

enum LogLevel { info, debug, error, other }

class AppLogger {
  AppLogger._();
  final _logger = Logger(
    filter: _CustomLogFilter(),
    printer: PrettyPrinter(noBoxingByDefault: true),
  );

  void log(LogLevel level, dynamic message) {
    switch (level) {
      case LogLevel.info:
        i(message);
        break;
      case LogLevel.debug:
        d(message);
        break;
      case LogLevel.error:
        e(message);
        break;
      default:
        all(message);
        break;
    }
  }

  void i(dynamic message) {
    _log(Level.info, message);
  }

  /// logs error to crashlytics and console
  void e(dynamic message, {dynamic error, StackTrace? stackTrace, bool recordError = true, bool fatal = false}) {
    _log(Level.error, message, error: error, stackTrace: stackTrace);
    if (recordError) {
      final mError = message is Error || message is Exception ? message : error ?? Exception(message);
      DI().resolve<CrashLogger>().recordError(
            mError,
            stackTrace ?? ((mError is Error) ? mError.stackTrace : null),
            reason: message,
            fatal: fatal,
          );
    }
  }

  void d(dynamic message) {
    _log(Level.debug, message);
  }

  void all(dynamic message) {
    _log(Level.all, message);
  }

  void _log(Level level, dynamic message, {dynamic error, StackTrace? stackTrace}) {
    _logger.log(level, message, error: error, stackTrace: stackTrace);
  }
}

class _CustomLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return kDebugMode;
  }
}
