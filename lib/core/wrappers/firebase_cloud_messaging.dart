import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/device_info_wrapper.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/local_notification.dart';
import 'package:melodyze/modules/share/cubit/fcm_notification_data_cubit.dart';

@pragma('vm:entry-point')
Future<void> handleBackgroundMessage(RemoteMessage message) async {
  logger.i("#FCM handleBackgroundMessage: ${message.notification?.title}");
}

Future<void> handleInitialMessage(RemoteMessage? message) async {
  if (message == null) return;
  logger.i("#FCM handleInitialMessage: ${message.notification?.title}");
}

Future<void> handleMessageOpenedApp(RemoteMessage? message) async {
  if (message == null) return;
  logger.i("#FCM handleMessageOpenedApp: ${message.notification?.title}");
  logger.d("#FCM handleMessageOpenedApp.data ${message.data}");

  switch (message.data["fcm_type"]) {
    case "song_checkout_cron":
      DI().resolve<FcmNotificationDataCubit>().setNotificationData(message.data);
      break;

    default:
      logger.d("#FCM unknown type: ${message.data["type"]}");
      return;
  }
}

class DeviceFcmInfo {
  final String deviceId;
  final String deviceType;
  final String? fcmToken;

  DeviceFcmInfo({required this.deviceId, required this.deviceType, required this.fcmToken});
}

class FirebaseCloudMessaging {
  final _firebaseMessaging = FirebaseMessaging.instance;

  Future<void> init() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: true,
        badge: true,
        carPlay: false,
        criticalAlert: true,
        provisional: false,
        sound: true,
      );
      logger.i('#FCM Notification permission: ${settings.authorizationStatus}');

      await _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        logger.i('#FCM Foreground Message: ${message.notification?.title}');
        RemoteNotification? notification = message.notification;
        AndroidNotification? android = message.notification?.android;
        if (notification != null && android != null) {
          LocalNotification().showForegroundNotification(message);
        }
      });

      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        logger.i('#FCM Message Opened App: ${message.notification?.title}');
        RemoteNotification? notification = message.notification;
        AndroidNotification? android = message.notification?.android;
        if (notification != null && android != null) {
          handleMessageOpenedApp(message);
        }
      });

      unawaited(_firebaseMessaging.getInitialMessage().then(handleInitialMessage));

      _firebaseMessaging.onTokenRefresh.listen((fcmToken) {
        logger.i("#FCM onTokenRefresh: $fcmToken");
      }).onError((err) {
        logger.e("#FCM onTokenRefresh:onError $err");
      });
    } catch (e) {
      logger.e("#FCM FirebasePushNoti: ${e.toString()}");
    }
  }

  Future<DeviceFcmInfo> getFcmToken() async {
    final userDeviceInfo = await DeviceInfoWrapper.getSystemInfo();
    final fcmToken = await _firebaseMessaging.getToken();
    return DeviceFcmInfo(deviceId: userDeviceInfo.uid, deviceType: userDeviceInfo.platform, fcmToken: fcmToken);
  }

  Future<void> updatedFcmToken() async {
    final fcmInfo = await getFcmToken();
    unawaited(DI().resolve<ApiClient>().put(
      Endpoints.updateFcmToken,
      body: {"device_id": fcmInfo.deviceId, "device_type": fcmInfo.deviceType, "fcm_token": fcmInfo.fcmToken},
    ).then((onValue) => logger.i("#FCM FCM Token updated.")));
  }
}
