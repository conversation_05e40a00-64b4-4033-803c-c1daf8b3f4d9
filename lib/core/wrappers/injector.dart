import 'package:get_it/get_it.dart';

class DI {
  static final di = DI._();

  DI._();

  factory DI() => di;

  static DI get instance => di;

  final _getIt = GetIt.I;

  void registerSingleton<T extends Object>(T singleton) {
    _getIt.registerSingleton<T>(singleton);
  }

 void registerLazySingleton<T extends Object>(T Function() factoryFunc) {
    _getIt.registerLazySingleton<T>(factoryFunc);
  }

  T resolve<T extends Object>() {
    return _getIt<T>();
  }
}
