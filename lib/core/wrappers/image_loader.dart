import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

///This is just an abstraction class of the [SvgPicture] and other libraries
///So that these libraries can be easily changed if needed
class ImageLoader {
  ImageLoader._();

  static Widget fromAsset(
    String assetName, {
    double? height,
    double? width,
    Color? color,
    BoxFit? fit,
  }) {
    if (assetName.endsWith('.svg')) {
      return _assetSvg(assetName, height: height, width: width, color: color, fit: fit);
    } else {
      return _asset(assetName, height: height, width: width, color: color, fit: fit);
    }
  }

  static Widget _asset(
    String assetName, {
    double? height,
    double? width,
    Color? color,
    BoxFit? fit,
  }) {
    return Image.asset(
      assetName,
      height: height,
      width: width,
      color: color,
      fit: fit,
    );
  }

  static Widget file(
    String filePath, {
    double? height,
    double? width,
    int? cacheHeight,
    int? cacheWidth,
    Color? color,
  }) {
    return Image.file(
      File(filePath),
      height: height,
      width: width,
      color: color,
      cacheHeight: cacheHeight,
      cacheWidth: cacheWidth,
    );
  }

  static Widget network(
    String url, {
    double? height,
    double? width,
    Color? color,
    BoxFit? fit,
    ImageErrorWidgetBuilder? errorBuilder,
    ImageLoadingBuilder? loadingBuilder,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.network(
      url,
      height: height,
      width: width,
      color: color,
      fit: fit ?? BoxFit.cover,
      errorBuilder: errorBuilder,
      loadingBuilder: loadingBuilder,
      cacheHeight: cacheHeight,
      cacheWidth: cacheWidth,
    );
  }

  static Widget cachedNetworkImage(
    String url, {
    double? height,
    double? width,
    Color? color,
    BoxFit? fit,
    LoadingErrorWidgetBuilder? errorBuilder,
    ProgressIndicatorBuilder? loadingBuilder,
    PlaceholderWidgetBuilder? placeholderWidgetBuilder,
    int? cacheWidth,
    int? cacheHeight,
    String? cacheKey,
  }) {
    return CachedNetworkImage(
      imageUrl: url,
      height: height,
      width: width,
      color: color,
      placeholder: placeholderWidgetBuilder,
      fit: fit ?? BoxFit.cover,
      errorWidget: errorBuilder,
      progressIndicatorBuilder: loadingBuilder,
      memCacheHeight: cacheHeight,
      memCacheWidth: cacheWidth,
      cacheKey: cacheKey,
    );
  }

  static Widget _assetSvg(
    String assetName, {
    double? height,
    double? width,
    Color? color,
    BoxFit? fit,
  }) {
    return SvgPicture.asset(
      assetName,
      height: height,
      width: width,
      colorFilter: color == null
          ? null
          : ColorFilter.mode(
              color,
              BlendMode.srcIn,
            ),
      fit: fit ?? BoxFit.contain,
    );
  }

  static Widget networkSvg(
    String assetName, {
    double? height,
    Color? color,
    double? width,
  }) {
    return SvgPicture.network(
      assetName,
      height: height,
      width: width,
      colorFilter: color == null
          ? null
          : ColorFilter.mode(
              color,
              BlendMode.srcIn,
            ),
      fit: BoxFit.cover,
    );
  }
}
