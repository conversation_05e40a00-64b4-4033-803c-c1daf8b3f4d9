import 'dart:async';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

class RemoteConfigHelper {
  RemoteConfigHelper._();

  static final FirebaseRemoteConfig _remoteConfig = FirebaseRemoteConfig.instance;

  static Future<void> init() async {
    await _remoteConfig.setDefaults(_defaultValues);
    try {
      await _remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(
            minutes: kReleaseMode ? 60 : 5,
          ),
        ),
      );
      await _remoteConfig.fetch();
      await _remoteConfig.fetchAndActivate();
    } on PlatformException catch (e, stackTrace) {
      await _remoteConfig.setDefaults(_defaultValues);
      logger.e('Remote config data not fetched properly', error: e, stackTrace: stackTrace, recordError: true);
    } catch (e, stackTrace) {
      await _remoteConfig.setDefaults(_defaultValues);
      logger.e('Remote config data not fetched properly', error: e, stackTrace: stackTrace, recordError: true);
    }
  }

  static String getString(String key) {
    return _remoteConfig.getString(key);
  }

  static int getInt(String key) {
    return _remoteConfig.getInt(key);
  }

  static const Map<String, dynamic> _defaultValues = <String, dynamic>{
    RemoteConfigKeys.delayRange: 1000,
    RemoteConfigKeys.appVersionConfig: '''{
      "minimum_supported_version": "1.0.18",
      "update_message": "A new version of Melodyze is available. Please update to continue using the app.",
      "android_app_update_url": "https://appdistribution.firebase.google.com/testerapps/1:959993156379:android:533432d92676d2262ef9be/releases/3oqaturcs3nig?utm_source=firebase-console",
      "ios_app_update_url": "https://appdistribution.firebase.google.com/testerapps/1:959993156379:ios:9d96051f255237102ef9be/releases/11gnu74vs5gm0?utm_source=firebase-console",
      "is_force_update_enabled": true
    }''',
    RemoteConfigKeys.audioLevelRange: '''{
      "min": -20.0,
      "max": -4.0
    }''',
    RemoteConfigKeys.initialTabIndex: 0,
  };
}

class RemoteConfigKeys {
  RemoteConfigKeys._();
  static const String delayRange = 'delay_range';
  static const String appVersionConfig = 'app_version_config';
  static const String audioLevelRange = 'audio_level_range';
  static const String initialTabIndex = 'initial_tab_index';
}
