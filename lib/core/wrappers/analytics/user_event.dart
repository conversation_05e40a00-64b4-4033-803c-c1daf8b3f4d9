// ignore_for_file: non_constant_identifier_names

import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/injector.dart';

String _genName() {
  var stack = StackTrace.current.toString();
  RegExp exp = RegExp(r'(?<=UserEvent\.)\w+');
  Iterable<Match> matches = exp.allMatches(stack, 0);
  for (final Match m in matches) {
    return m[0]!;
  }
  return 'unknown';
}

mixin EventSDKProvider {
  void fire(Map<String, String> map);
}

class EventSDK with EventSDKProvider {
  @override
  void fire(Map<String, String> map) {
    var name = _genName();
    logger.d("🟡 event: $name: $map");
  }
}

class EventSDKMock with EventSDKProvider {
  @override
  void fire(Map<String, String> map) {
    var name = _genName();
    logger.d("🟡 event: $name: $map");
  }
}

class UserEvent {
  final EventSDKProvider _sdk = DI().resolve();
  static UserEvent shared = UserEvent();

  // Login page
  void login_loaded() {
    _sdk.fire({});
  }

  void login_login_click(String type) {
    _sdk.fire({"type": type});
  }

  /// type = apple/google/phone/tictok
  /// error = String
  void login_login_failed(String type, String error) {
    _sdk.fire({'type': type, "error": error});
  }

  void login_success(String type) {
    _sdk.fire({'type': type});
  }

  // Preferences Page
  void prefs_loaded() {
    _sdk.fire({});
  }

  void prefs_select(String prefName, String id) {
    _sdk.fire({"type": prefName, "id": id});
  }

  void prefs_deselect(String prefName, String id) {
    _sdk.fire({"type": prefName, "id": id});
  }

  void prefs_proceed_click(String selectedList) {
    _sdk.fire({"selected_items": selectedList});
  }

  void prefs_proceed_success() {
    _sdk.fire({});
  }

  void prefs_proceed_failure(String error) {
    _sdk.fire({"error": error});
  }

  // home page
  void home_loaded() {
    _sdk.fire({});
  }

  void home_search_clicked() {
    _sdk.fire({});
  }

  void home_grid_item_clicked(name) {
    _sdk.fire({"name": name});
  }

  void home_grid_api_called() {
    _sdk.fire({});
  }

  void home_grid_api_success() {
    _sdk.fire({});
  }

  void home_grid_api_failed() {
    _sdk.fire({});
  }

  void home_tile_explore_clicked(String name) {
    _sdk.fire({"name": name});
  }

  void home_tile_explore_api_success() {
    _sdk.fire({});
  }

  void home_tile_explore_api_failure() {
    _sdk.fire({});
  }

  void home_tile_song_clicked() {
    _sdk.fire({});
  }

  void home_tile_song_api_success() {
    _sdk.fire({});
  }

  void home_tile_song_api_failure() {
    _sdk.fire({});
  }

  void home_tile_horizontal_scroll_event() {
    _sdk.fire({});
  }

  void home_scroll_event() {
    _sdk.fire({});
  }

  // search
  void search_loaded() {
    _sdk.fire({});
  }

  void search_api_called() {
    _sdk.fire({});
  }

  void search_api_success() {
    _sdk.fire({});
  }

  void search_api_failed() {
    _sdk.fire({});
  }

  void search_back_button_clicked() {
    _sdk.fire({});
  }

  void search_scroll_event() {
    _sdk.fire({});
  }

  void search_song_clicked() {
    _sdk.fire({});
  }

// SongList Page
  void songlist_loaded() {
    _sdk.fire({});
  }

  void songlist_scroll_event() {
    _sdk.fire({});
  }

  void songlist_song_clicked() {
    _sdk.fire({});
  }

  void songlist_back_button_clicked() {
    _sdk.fire({});
  }

  void songlist_search_clicked() {
    _sdk.fire({});
  }

  void songlist_song_clicked_api_success() {
    _sdk.fire({});
  }

  void songlist_song_clicked_api_failure() {
    _sdk.fire({});
  }

// Playback/ Personalisation page
  void song_loaded() {
    _sdk.fire({});
  }

  void song_scroll_event_player() {
    _sdk.fire({});
  }

  void song_scroll_event_lyrics_vertical() {
    _sdk.fire({});
  }

  void song_play_pause_event() {
    _sdk.fire({});
  }

  void song_genre_selection_event() {
    _sdk.fire({});
  }

  void song_scale_selection_event() {
    _sdk.fire({});
  }

  void song_tempo_selection_event() {
    _sdk.fire({});
  }

  void song_back_button_click() {
    _sdk.fire({});
  }

  void song_tick_button_click() {
    _sdk.fire({});
  }

  void song_tick_button_api_success() {
    _sdk.fire({});
  }

  void song_tick_button_api_failure() {
    _sdk.fire({});
  }

// Recording page
  void rec_loaded() {
    _sdk.fire({});
  }

  void rec_record_button_clicked() {
    _sdk.fire({});
  }

  void rec_restart_button_clicked() {
    _sdk.fire({});
  }

  void rec_microphone_selector() {
    _sdk.fire({});
  }

  void rec_back_button_clicked() {
    _sdk.fire({});
  }

  void rec_tick_button_clicked() {
    _sdk.fire({});
  }

  void rec_tick_button_click_api_success() {
    _sdk.fire({});
  }

  void rec_tick_button_click_api_faliure() {
    _sdk.fire({});
  }

// SongMasteringPage
  void filter_loaded() {
    _sdk.fire({});
  }

  void filter_save_button_clicked() {
    _sdk.fire({});
  }

  void filter_save_button_click_api_success() {
    _sdk.fire({});
  }

  void filter_save_button_click_api_failure() {
    _sdk.fire({});
  }

  void filter_play_pause_event() {
    _sdk.fire({});
  }

  void filter_playback_scroll_event() {
    _sdk.fire({});
  }

  void filter_bgm_slider_scroll_event() {
    _sdk.fire({});
  }

  void filter_vocal_slider_scroll_event() {
    _sdk.fire({});
  }

  void filter_latency_scoll_event() {
    _sdk.fire({});
  }

  void filter_vocal_filter_scroll_event() {
    _sdk.fire({});
  }

  void filter_vocal_filter_selection_event() {
    _sdk.fire({});
  }

  void filter_denoise_selection_event() {
    _sdk.fire({});
  }

// Listener Feed Page
  void feed_loaded() {
    _sdk.fire({});
  }

  void feed_upper_horizontal_swipe() {
    _sdk.fire({});
  }

  void feed_lower_horizontal_swipe() {
    _sdk.fire({});
  }

  void feed_vertical_swipe() {
    _sdk.fire({});
  }

  void feed_like() {
    _sdk.fire({});
  }

  void feed_share() {
    _sdk.fire({});
  }

  void feed_record() {
    _sdk.fire({});
  }

  // profile
  void profile_loaded() {
    _sdk.fire({});
  }
}
