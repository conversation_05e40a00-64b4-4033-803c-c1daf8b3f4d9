import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/molecules/rendering_error_widget.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

class CrashLogger {
  final FirebaseCrashlytics? _crashlytics = kIsWeb
      ? null
      : (Platform.isAndroid || Platform.isIOS)
          ? FirebaseCrashlytics.instance
          : null;
  final enableCrashReporting = true;

  void initialiseErrorHandlers() {
    if (_crashlytics == null) {
      return;
    }
    _crashlytics.setCrashlyticsCollectionEnabled(enableCrashReporting);
    FlutterError.onError = (details) {
      //LOG errors to console or any error tracking tools
      recordFlutterError(details);
      logger.e('CrashLogger: Flutter Error', error: details.exception, stackTrace: details.stack, recordError: false);
    };
    PlatformDispatcher.instance.onError = (error, stack) {
      //LOG errors to console or any error tracking tools
      logger.e('CrashLogger: Dispatcher error', error: error, stackTrace: stack, recordError: false);
      recordError(error, stack, fatal: false);
      return true;
    };
    if (enableCrashReporting) {
      ErrorWidget.builder = (FlutterErrorDetails details) {
        //this triggers when FlutterErrors are thrown in UI
        return RenderingErrorWidget(errorDetails: details);
      };
    }
    // di.resolve<EventBus>().on<ExceptionEvent>().listen((event) {
    //   logger.e(event.e, recordError: false);
    //   di.resolve<CrashLogger>().recordError(event.e, null, fatal: false);
    // });
  }

  /// Add user id
  Future<void> setUserIdentifier(String identifier) async {
    if (_crashlytics == null) {
      return;
    }
    await FirebaseAnalytics.instance.setUserId(id: identifier);
    await _crashlytics.setUserIdentifier(identifier);
  }

  /// Clear user id
  Future<void> clearUserIdentifier() async {
    if (_crashlytics == null) {
      return;
    }
    await FirebaseAnalytics.instance.setUserId(id: null);
    await _crashlytics.setUserIdentifier('');
  }

  /// Submits a report of a fatal error caught by the Flutter framework.
  Future<void> recordFlutterError(
    FlutterErrorDetails flutterErrorDetails, {
    bool fatal = false,
  }) async {
    if (_crashlytics == null) {
      return;
    }
    await _crashlytics.recordFlutterError(flutterErrorDetails, fatal: true);
  }

  /// Submits a report of a caught error.
  Future<void> recordError(
    Object error,
    StackTrace? stack, {
    dynamic reason,
    Iterable<Object> information = const [],
    bool fatal = false,
  }) async {
    if (_crashlytics == null) {
      return;
    }
    await _crashlytics.recordError(
      error,
      stack,
      reason: reason,
      information: information,
      printDetails: kDebugMode,
      fatal: fatal,
    );
  }

  Future<void> log(String message) async {
    if (_crashlytics == null) {
      return;
    }
    await _crashlytics.log(message);
  }
}
