import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class SecureStorageHelper {
  /// Create storage
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );

  /// Read value
  Future<String?> read(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      logger.e(e.toString(), error: e);
      return null;
    }
  }

  /// Read value
  Future<void> write(String key, String value) async {
    return await _secureStorage.write(key: key, value: value);
  }

  /// Read all values
  Future<Map<String, String>> readAll() async {
    return await _secureStorage.readAll();
  }

  /// Delete value
  Future<void> delete(String key) async {
    return await _secureStorage.delete(key: key);
  }

  /// Delete all
  Future<void> deleteAll() async {
    return await _secureStorage.deleteAll();
  }
}

class SecureStorageKeys {
  SecureStorageKeys._();

  static const List<String> sessionKeys = [
    accessToken,
    user,
    deviceInfo,
    isPreferenceSelected,
  ];

  static const accessToken = 'access_token';
  static const user = 'user';
  static const deviceInfo = 'device_info';
  static const isPreferenceSelected = 'is_preference_selected';
  static const isFirstTime = 'is_first_time';
}

class AccessTokenHelper {
  String? _accessToken;

  Future<String> get accessToken async {
    final tempAccessToken = _accessToken;
    if (tempAccessToken != null && tempAccessToken.isNotEmpty) {
      return tempAccessToken;
    }
    _accessToken = await DI().resolve<SecureStorageHelper>().read(SecureStorageKeys.accessToken) ?? '';
    return _accessToken ?? '';
  }

  Future<void> setAccessToken(String accessToken) async {
    _accessToken = accessToken;
    await DI().resolve<SecureStorageHelper>().write(SecureStorageKeys.accessToken, accessToken);
  }

  Future<void> deleteAccessToken() async {
    _accessToken = null;
    await DI().resolve<SecureStorageHelper>().delete(SecureStorageKeys.accessToken);
  }
}

class FirstLoginHelper {
  String? isFirstTimeCache;

  Future<void> fetchFirstTime() async {
    final isFirstTime = await DI().resolve<SecureStorageHelper>().read(SecureStorageKeys.isFirstTime);
    isFirstTimeCache = isFirstTime ?? 'true';
  }

  bool get isFirstTimeSync => isFirstTimeCache != 'true';

  Future<void> setFirstTime() async {
    if (isFirstTimeSync) await DI().resolve<SecureStorageHelper>().write(SecureStorageKeys.isFirstTime, 'true');
  }
}
