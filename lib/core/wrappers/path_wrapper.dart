import 'dart:io';

import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:path_provider/path_provider.dart' as path;

class PathWrapper {
  PathWrapper._();

  static String? _tempGetApplicationDocumentsDirectoryPath;
  static String? _tempGetTemporaryDirectoryPath;

  static Future<String> getApplicationDocumentsDirectoryPath() async => _tempGetApplicationDocumentsDirectoryPath ??= (await path.getApplicationDocumentsDirectory()).path;
  static Future<String> getTemporaryDirectory() async => _tempGetTemporaryDirectoryPath ??= (await path.getTemporaryDirectory()).path;

  static void deleteFile(String path) {
    final file = File(path);
    if (file.existsSync()) {
      logger.d("PathWrapper.Deleting file: $path");
       file.deleteSync();
    }
  }
  
  static Future<String> getRecordingPath() async {
    final path = '${await getApplicationDocumentsDirectoryPath()}/recording.wav';
    deleteFile(path);
    return path;
  }

  static Future<String> getNormalizedRecordingPath() async {
    final path = '${await getApplicationDocumentsDirectoryPath()}/normalized_recording.wav';
    deleteFile(path);
    return path;
  }

  static Future<String> getMixedRecordFlacPath() async {
    final path = '${await getApplicationDocumentsDirectoryPath()}/mixed_record.flac';
    deleteFile(path);
    return path;
  }

  static Future<String> getJuceMixerExportPath() async {
    final path = '${await getApplicationDocumentsDirectoryPath()}/juce_mixed.flac';
    deleteFile(path);
    return path;
  }

  static Future<String> getTempDir() async {
    final path = '${await getTemporaryDirectory()}/temp_dir';
    final directory = Directory(path);
    if (!directory.existsSync()) {
      await directory.create(recursive: true);
    }
    return path;
  }

  static Future<String> getTempContentVideoPath() async {
    final path = '${await getTempDir()}/content_video.mp4';
    deleteFile(path);
    return path;
  }

  static Future<String> getTempContentThumbPath() async {
    final path = '${await getTempDir()}/content_thumbnail.webp';
    deleteFile(path);
    return path;
  }

}
