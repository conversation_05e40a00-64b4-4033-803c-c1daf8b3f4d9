class RepoResult<T> {
  T? data;
  RepoError? _error;
  final bool isSuccess;

  RepoResult._(this.isSuccess, {this.data, RepoError? error}) : _error = error ?? RepoError("No error", '');

  factory RepoResult.success(T result) => RepoResult._(true, data: result);

  factory RepoResult.failure(RepoError error) => RepoResult._(false, error: error);

  RepoError get error => _error!;
}

class RepoError {
  final String message;

  /// this can be anything exception, string, null,etc.,
  final dynamic e;

  RepoError(this.message, [this.e]);

  @override
  String toString() {
    return message;
  }

  String description() {
    final error = e;
    return '$message ${(error is Error) ? error.stackTrace : error.toString()}';
  }
}
