import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

class JsonStringToInt implements JsonConverter<int, dynamic> {
  const JsonStringToInt();

  @override
  int fromJson(dynamic json) {
    if (json is num) {
      return json.toInt();
    }
    try {
      return int.parse(json);
    } catch (e) {
      logger.e('JsonStringToInt: failed to parse $json to int', error: e, recordError: true);
      return 0;
    }
  }

  @override
  String toJson(int object) => object.toString();
}
