import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/modules/auth/bloc/authentication_bloc.dart';

extension StringExtension on String? {
  bool get isNullOrEmpty {
    final currentString = this;
    return currentString == null || currentString.isEmpty;
  }

  /// Capitalizes the first letter of the string and converts the rest to lowercase.
  ///
  /// Returns an empty string if the string is null or empty.
  ///
  /// # Example
  /// ```dart
  /// String text = "hello world";
  /// print(text.capitalize()); // Output: Hello world
  /// ```
  String capitalize() {
    if (isNullOrEmpty) return "";
    return "${this![0].toUpperCase()}${this!.substring(1).toLowerCase()}";
  }

  /// Capitalizes the only first letter of the string, rest stays the same
  ///
  /// Returns an empty string if the string is null or empty.
  ///
  /// # Example
  /// ```dart
  /// String text = "hellO worLd";
  /// print(text.capitalizeFirst()); // Output: "HellO worLd"
  /// ```
  String capitalizeFirst() {
    if (isNullOrEmpty) return "";
    return "${this![0].toUpperCase()}${this!.substring(1)}";
  }

  Duration parseAsDuration() {
    if (this == null || this!.isEmpty) {
      return Duration.zero;
    }

    final parts = this!.split(':');
    if (parts.length != 3) {
      return Duration.zero;
    }

    final minutes = int.tryParse(parts[0]) ?? 0;
    final seconds = int.tryParse(parts[1]) ?? 0;
    final milliseconds = int.tryParse(parts[2]) ?? 0;

    var totalMillis = (minutes * 60 * 1000) + (seconds * 1000) + milliseconds;

    // Ensure the result is non-negative
    if (totalMillis < 0) {
      return Duration.zero;
    }

    return Duration(milliseconds: totalMillis);
  }
}

extension DurationExtension on Duration {
  /// Returns a formatted time string with two-digit minutes and seconds based on the Duration object in mm:ss format.
  String toMinuteSecondTimeString() {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(inSeconds.remainder(60));
    return "$twoDigitMinutes:$twoDigitSeconds";
  }
}

extension AuthBloc on BuildContext {
  AuthenticationBloc get authBloc => read<AuthenticationBloc>();
}

extension IterableExtensions<T> on Iterable<T>? {
  /// Return if list is null or empty
  bool get isNullOrEmpty => this == null || this?.isEmpty == true;

  /// Return if list is not null or empty
  bool get isNotNullOrEmpty => this != null && this?.isNotEmpty == true;
}
