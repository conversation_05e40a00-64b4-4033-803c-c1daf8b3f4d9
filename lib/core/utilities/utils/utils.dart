import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:intl/intl.dart';

import 'package:crypto/crypto.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/local_storage_helper.dart';
// ignore: depend_on_referenced_packages
import 'package:path/path.dart' as path;

typedef Callback = void Function();

class Debouncer {
  Timer? _timer;

  /// Debounces a function, ensuring it's called at most once within a given duration
  void debounce(Callback func, [int delayMs = 400]) {
    _timer?.cancel(); // Cancel previous timer if active
    _timer = Timer(Duration(milliseconds: delayMs), func);
  }

  /// Cancels any pending debounced function execution.
  void dispose() {
    _timer?.cancel();
  }
}

class LyricsUtils {
  LyricsUtils._();
  static String convertToLrc(LyricsData lyricsModel) {
    StringBuffer lrcBuffer = StringBuffer();
    for (final line in lyricsModel.lyrics.data) {
      lrcBuffer.writeln('[${convertTimeFormat(line.startTime)}]${line.text}');
    }
    return lrcBuffer.toString();
  }

  static convertTimeFormat(String timeStr) {
    final timeComponents = timeStr.split(':').map(int.parse).toList();
    return '${timeComponents[0].toString().padLeft(2, '0')}:${timeComponents[1]}.${timeComponents[2].toInt() == 0 ? '000' : timeComponents[2]}';
  }
}

class StringUtils {
  StringUtils._();

  /// Generating random alphanumeric strings
  static String generateRandomString(int length) {
    const chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
    final random = Random();
    return String.fromCharCodes(Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }
}

class FileNameAndExtension {
  final String fileName;
  final String extension;
  final String fileNameWithExtension;

  FileNameAndExtension(this.fileName, this.extension, this.fileNameWithExtension);
}

class FileUtils {
  FileUtils._();

  static FileNameAndExtension extractFileExtension(String filePathOrUrl) {
    // Parse the URL
    final uri = Uri.parse(filePathOrUrl);

    // Get the last path segment (likely containing the filename with extension)
    final filename = uri.pathSegments.last;

    // Use the 'path' library to extract the extension
    final extension = path.extension(filename);

    // The extension will include the leading dot
    return FileNameAndExtension(filename.split('.').first, extension, filename);
  }

  static Future<String> calculateFileSha256(String filePath) async {
    File file = File(filePath);
    List<int> fileBytes = await file.readAsBytes();
    Digest sha256Result = sha256.convert(fileBytes);

    // Usually, you'll want the hash in hexadecimal form:
    return sha256Result.toString();
  }

  static String getContentTypeFromFilePath(String filePath) {
    final extension = extractFileExtension(filePath).extension;
    return getContentTypeFromExtension(extension);
  }

  static String removeDotBeforeExtension(String filename) {
    int dotIndex = filename.lastIndexOf('.');
    if (dotIndex >= 0) {
      return filename.substring(dotIndex + 1);
    } else {
      return filename; // No dots in the name
    }
  }

  static String getContentTypeFromExtension(String extension) {
    extension = removeDotBeforeExtension(extension.toLowerCase());

    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';

      case 'png':
        return 'image/png';

      case 'gif':
        return 'image/gif';

      case 'pdf':
        return 'application/pdf';

      case 'aac':
        return 'audio/aac';

      case 'mp3':
        return 'audio/mpeg';

      case 'mp4':
        return 'video/mp4';

      case 'webm':
        return 'video/webm';

      case 'ogg':
        return 'video/ogg';

      case 'mov':
        return 'video/quicktime';

      case 'avi':
        return 'video/x-msvideo';

      case 'wav':
        return 'audio/wav';

      case 'flac':
        return 'audio/flac';
      
      case 'webp':
        return 'image/webp';

      default:
        return 'application/octet-stream';
    }
  }

  static String toSnakeCase(String fileName) {
    return fileName.replaceAll(' ', '_');
  }

  static String fromSnakeCase(String fileName) {
    return fileName.replaceAll('_', ' ');
  }
}

class TimeUtils {
  TimeUtils._();

  static String formatEpochMilliseconds(int epochMilliseconds) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(epochMilliseconds);
    return DateFormat('dd/MM/yy HH:mm').format(dateTime);
  }
}

class CommonUtils {
  CommonUtils._();

  static Future<void> saveUserDelay(int delay) async {
    await DI().resolve<LocalStorageHelper>().setInt(LocalStorageKeys.userSetDelay, delay);
  }

  static int getUserDelay() {
    return DI().resolve<LocalStorageHelper>().getInt(LocalStorageKeys.userSetDelay) ?? 0;
  }

  static Map<String, String> getTempoSpeedMap(List<String> tempos) {
    List<String> viewList = ["0.8x", "0.9x", "1.0x", "1.1x", "1.2x"];
    Map<String, String> tempoMap = {};
    for (String tempo in tempos) {
      tempoMap[tempo] = viewList[tempos.indexOf(tempo)];
    }
    return tempoMap;
  }
}
