import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

class ImageUtils {
  static final ImagePicker _picker = ImagePicker();

  /// Request gallery permission
  static Future<bool> requestGalleryPermission() async {
    try {
      if (Platform.isAndroid) {
        // For Android 13+ (API 33+), we need READ_MEDIA_IMAGES permission
        final status = await Permission.photos.request();
        if (status.isGranted) {
          return true;
        }
        
        // Fallback to storage permission for older Android versions
        final storageStatus = await Permission.storage.request();
        return storageStatus.isGranted;
      } else if (Platform.isIOS) {
        final status = await Permission.photos.request();
        return status.isGranted;
      }
      return false;
    } catch (e) {
      logger.e('Error requesting gallery permission', error: e);
      return false;
    }
  }

  /// Pick image from gallery
  static Future<XFile?> pickImageFromGallery() async {
    try {
      final hasPermission = await requestGalleryPermission();
      if (!hasPermission) {
        logger.e('Gallery permission not granted');
        return null;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 100,
      );
      
      return image;
    } catch (e) {
      logger.e('Error picking image from gallery', error: e);
      return null;
    }
  }

/// Crop image to 1:1 aspect ratio with improved Android UI
static Future<String?> processProfilePhoto(String imagePath) async {
  try {
    final croppedFile = await ImageCropper().cropImage(
      sourcePath: imagePath,
      aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
      maxWidth: 250,
      maxHeight: 250,
      compressFormat: ImageCompressFormat.jpg,
      compressQuality: 90,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Adjust Photo',
          toolbarColor: const Color(0xFF1A1A1A),
          toolbarWidgetColor: const Color(0xFFFFFFFF),
          initAspectRatio: CropAspectRatioPreset.square,
          lockAspectRatio: true,
          aspectRatioPresets: [CropAspectRatioPreset.square],
          
          // Fix for status bar overlap and button positioning
          statusBarColor: const Color(0xFF1A1A1A),
          activeControlsWidgetColor: const Color(0xFFFFFFFF),
          
          // Disable problematic zoom controls
          showCropGrid: true,
          hideBottomControls: false,
          
          // Additional padding and layout fixes
          cropGridStrokeWidth: 2,
          cropGridColor: const Color(0xFFFFFFFF),
          
          // These help with button positioning
          cropFrameStrokeWidth: 4,
          cropFrameColor: const Color(0xFFFFFFFF),
        ),
        IOSUiSettings(
          title: 'Adjust Photo',
          doneButtonTitle: "Choose",
          aspectRatioLockEnabled: true,
          resetAspectRatioEnabled: false,
          aspectRatioPresets: [CropAspectRatioPreset.square],
        ),
      ],
    );
    
    return croppedFile?.path;
  } catch (e) {
    logger.e('Error processing image', error: e);
    return null;
  }
}
}
