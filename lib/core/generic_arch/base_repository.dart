import 'package:melodyze/core/exceptions/repo_exception.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

typedef CreateResult = Future<RepoResult> Function(Map<String, dynamic> json);
typedef FetchData = Future<Map<String, dynamic>?> Function();

class BaseRepo {
  Future<RepoResult> executeAndReturnResult<T>(
      FetchData fetchData, CreateResult createResult,
      {String? errorMessage}) async {
    try {
      final json = await fetchData();
      if (json == null) {
        throw RepoException('json cannot be null');
      }
      return await createResult(json);
    } catch (e) {
      logger.e(e, error: e, stackTrace: e is Error ? e.stackTrace : null);
      return RepoResult.failure(RepoError(errorMessage ?? e.toString(), e));
    }
  }
}
