import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/local_storage_helper.dart';
import 'package:no_screenshot/no_screenshot.dart';

/// Service to manage screenshot functionality with persistent settings
class ScreenshotManager {
  static ScreenshotManager? _instance;
  static ScreenshotManager get instance => _instance ??= ScreenshotManager._();

  ScreenshotManager._();

  LocalStorageHelper get _localStorage => DI().resolve<LocalStorageHelper>();

  Future<void> initialize() async {
    try {
      final isEnabled = await getScreenshotEnabled();
      if (isEnabled) {
        await NoScreenshot.instance.screenshotOn();
      } else {
        await NoScreenshot.instance.screenshotOff();
      }
    } catch (e) {
      await NoScreenshot.instance.screenshotOff();
    }
  }

  Future<bool> getScreenshotEnabled() async {
    try {
      final enabled = _localStorage.getBool(LocalStorageKeys.screenshotEnabled);
      return enabled ?? false;
    } catch (e) {
      return false;
    }
  }

  Future<void> enableScreenshots() async {
    try {
      await NoScreenshot.instance.screenshotOn();
      await _localStorage.setBool(LocalStorageKeys.screenshotEnabled, true);
    } catch (e) {
      logger.e('Error enabling screenshots: $e');
    }
  }

  Future<void> disableScreenshots() async {
    try {
      await NoScreenshot.instance.screenshotOff();
      await _localStorage.setBool(LocalStorageKeys.screenshotEnabled, false);
    } catch (e) {
      logger.e('Error disabling screenshots: $e');
    }
  }

  Future<void> toggleScreenshots() async {
    final currentState = await getScreenshotEnabled();
    if (currentState) {
      await disableScreenshots();
    } else {
      await enableScreenshots();
    }
  }

}
