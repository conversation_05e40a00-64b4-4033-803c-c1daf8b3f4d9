import 'dart:io';

class CachedFileEntry {
  final String id;
  final String originalUrl;
  final String localPath;
  final String fileType;
  final DateTime cachedAt;
  final DateTime lastAccessedAt;
  final String? etag;
  final String? contentType;

  const CachedFileEntry({
    required this.id,
    required this.originalUrl,
    required this.localPath,
    required this.fileType,
    required this.cachedAt,
    required this.lastAccessedAt,
    this.etag,
    this.contentType,
  });

  bool get exists => File(localPath).existsSync();

  int get ageInHours {
    return DateTime.now().difference(cachedAt).inHours;
  }

  bool isExpired(int maxAgeHours) {
    return ageInHours > maxAgeHours;
  }

  CachedFileEntry copyWithAccess() {
    return CachedFileEntry(
      id: id,
      originalUrl: originalUrl,
      localPath: localPath,
      fileType: fileType,
      cachedAt: cachedAt,
      lastAccessedAt: DateTime.now(),
      etag: etag,
      contentType: contentType,
    );
  }

  CachedFileEntry copyWith({
    String? id,
    String? originalUrl,
    String? localPath,
    String? fileType,
    int? sizeBytes,
    DateTime? cachedAt,
    DateTime? lastAccessedAt,
    String? etag,
    String? contentType,
  }) {
    return CachedFileEntry(
      id: id ?? this.id,
      originalUrl: originalUrl ?? this.originalUrl,
      localPath: localPath ?? this.localPath,
      fileType: fileType ?? this.fileType,
      cachedAt: cachedAt ?? this.cachedAt,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      etag: etag ?? this.etag,
      contentType: contentType ?? this.contentType,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'originalUrl': originalUrl,
      'localPath': localPath,
      'fileType': fileType,
      'cachedAt': cachedAt.millisecondsSinceEpoch,
      'lastAccessedAt': lastAccessedAt.millisecondsSinceEpoch,
      'etag': etag,
      'contentType': contentType,
    };
  }

  factory CachedFileEntry.fromJson(Map<String, dynamic> json) {
    return CachedFileEntry(
      id: json['id'] as String,
      originalUrl: json['originalUrl'] as String,
      localPath: json['localPath'] as String,
      fileType: json['fileType'] as String,
      cachedAt: DateTime.fromMillisecondsSinceEpoch(json['cachedAt'] as int),
      lastAccessedAt: DateTime.fromMillisecondsSinceEpoch(json['lastAccessedAt'] as int),
      etag: json['etag'] as String?,
      contentType: json['contentType'] as String?,
    );
  }

  @override
  String toString() {
    return 'CachedFileEntry(id: $id, originalUrl: $originalUrl, localPath: $localPath, fileType: $fileType';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CachedFileEntry && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class CachePolicy {
  final int maxFiles;
  final int maxSizeBytes;
  final int maxAgeHours;
  final bool useLRU;

  const CachePolicy({
    required this.maxFiles,
    required this.maxSizeBytes,
    required this.maxAgeHours,
    this.useLRU = true,
  });

  CachePolicy copyWith({
    int? maxFiles,
    int? maxSizeBytes,
    int? maxAgeHours,
    bool? useLRU,
  }) {
    return CachePolicy(
      maxFiles: maxFiles ?? this.maxFiles,
      maxSizeBytes: maxSizeBytes ?? this.maxSizeBytes,
      maxAgeHours: maxAgeHours ?? this.maxAgeHours,
      useLRU: useLRU ?? this.useLRU,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'maxFiles': maxFiles,
      'maxSizeBytes': maxSizeBytes,
      'maxAgeHours': maxAgeHours,
      'useLRU': useLRU,
    };
  }

  factory CachePolicy.fromJson(Map<String, dynamic> json) {
    return CachePolicy(
      maxFiles: json['maxFiles'] as int,
      maxSizeBytes: json['maxSizeBytes'] as int,
      maxAgeHours: json['maxAgeHours'] as int,
      useLRU: json['useLRU'] as bool? ?? true,
    );
  }

  @override
  String toString() {
    return 'CachePolicy(maxFiles: $maxFiles, maxSizeBytes: $maxSizeBytes, maxAgeHours: $maxAgeHours, useLRU: $useLRU)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CachePolicy && other.maxFiles == maxFiles && other.maxSizeBytes == maxSizeBytes && other.maxAgeHours == maxAgeHours && other.useLRU == useLRU;
  }

  @override
  int get hashCode {
    return Object.hash(maxFiles, maxSizeBytes, maxAgeHours, useLRU);
  }
}
