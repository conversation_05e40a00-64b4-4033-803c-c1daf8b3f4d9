library;

import 'package:melodyze/core/services/file_manager/file_manager_service.dart';
import 'package:melodyze/core/services/file_manager/models/file_operation_models.dart';
export 'file_manager_service.dart';
export 'models/file_operation_models.dart';
export 'models/cache_models.dart';
export 'utils/progress_tracker.dart';
export 'utils/retry_utils.dart';
export 'utils/cache_storage.dart';

Future<void> initializeFileManager() async {
  final fileManager = FileManagerService.instance;
  await fileManager.initialize();
}

Future<void> disposeFileManager() async {
  final fileManager = FileManagerService.instance;
  await fileManager.dispose();
}

class FileManagerQuickAccess {
  FileManagerQuickAccess._();

  static FileManagerService get fileManager => FileManagerService.instance;

  static Future<FileOperationResult> download(
    String url,
    FileType fileType, {
    FileOperationProgressCallback? onProgress,
    bool useCache = true,
    bool forceRefresh = false,
  }) async {
    final request = DownloadRequest(
      cacheKey: FileManagerQuickAccess.getCacheKeyFromUrl(url),
      url: url,
      fileType: fileType,
      config: FileOperationConfig(
        useCache: useCache,
        forceRefresh: forceRefresh,
      ),
      onProgress: onProgress,
    );
    return await fileManager.downloadFile(request);
  }

  static Future<FileOperationResult> upload(
    String localPath,
    String uploadEndpoint,
    FileType fileType, {
    FileOperationProgressCallback? onProgress,
    String? customFilename,
    String? finalFilename,
  }) async {
    final request = UploadRequest(
      localPath: localPath,
      uploadEndpoint: uploadEndpoint,
      fileType: fileType,
      onProgress: onProgress,
      customFilename: customFilename,
      finalFilename: finalFilename
    );

    return await fileManager.uploadFile(request);
  }

  static String getCacheKeyFromUrl(String url) {
    final uri = Uri.parse(url);
    final upath = uri.path;
    final uhost = uri.host;
    return '$uhost$upath';
  }
}
