import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:melodyze/core/services/file_manager/models/file_operation_models.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

class RetryUtils {
  RetryUtils._();

  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    Duration maxDelay = const Duration(seconds: 30),
    bool Function(Exception)? shouldRetry,
    String? operationName,
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;

    while (attempt <= maxRetries) {
      try {
        final result = await operation();
        if (attempt > 0) {
          logger.d('${operationName ?? 'Operation'} succeeded after $attempt retries');
        }
        return result;
      } catch (e) {
        attempt++;

        if (attempt > maxRetries) {
          logger.e('${operationName ?? 'Operation'} failed after $maxRetries retries', error: e);
          rethrow;
        }

        final exception = e is Exception ? e : Exception(e.toString());

        if (shouldRetry != null && !shouldRetry(exception)) {
          logger.e('${operationName ?? 'Operation'} failed with non-retryable error', error: e);
          rethrow;
        }

        logger.d('${operationName ?? 'Operation'} failed (attempt $attempt/$maxRetries), '
            'retrying in ${currentDelay.inMilliseconds}ms. Error: $e');

        await Future.delayed(currentDelay);

        currentDelay = Duration(
          milliseconds: min(
            (currentDelay.inMilliseconds * backoffMultiplier).round(),
            maxDelay.inMilliseconds,
          ),
        );

        final jitter = Random().nextDouble() * 0.1; // 0-10% jitter
        currentDelay = Duration(
          milliseconds: (currentDelay.inMilliseconds * (1 + jitter)).round(),
        );
      }
    }

    throw Exception('This should never be reached');
  }

  static bool shouldRetryNetworkOperation(Exception exception) {
    if (exception is SocketException) {
      return true;
    }

    if (exception is TimeoutException) {
      return true;
    }

    if (exception is HttpException) {
      final httpException = exception;
      if (httpException.message.contains('500') ||
          httpException.message.contains('502') ||
          httpException.message.contains('503') ||
          httpException.message.contains('504') ||
          httpException.message.contains('408') ||
          httpException.message.contains('429')) {
        return true;
      }
      return false;
    }

    final message = exception.toString().toLowerCase();
    if (message.contains('network') ||
        message.contains('connection') ||
        message.contains('timeout') ||
        message.contains('unreachable') ||
        message.contains('dns') ||
        message.contains('resolve')) {
      return true;
    }

    return false;
  }

  static bool shouldRetryFileOperation(Exception exception) {
    if (exception is FileSystemException) {
      final fileException = exception;
      if (fileException.message.contains('busy') || fileException.message.contains('locked') || fileException.message.contains('temporary')) {
        return true;
      }

      if (fileException.message.contains('permission') || fileException.message.contains('not found') || fileException.message.contains('access denied')) {
        return false;
      }
    }

    return shouldRetryNetworkOperation(exception);
  }
}

class FileOperationErrorHandler {
  FileOperationErrorHandler._();

  static FileOperationError handleError(Object error, StackTrace? stackTrace, String operation, {Map<String, dynamic>? context}) {
    String message;
    String errorCode;
    Exception? originalException;

    if (error is FileSystemException) {
      final fileError = error;
      message = 'File system error: ${fileError.message}';

      if (fileError.message.contains('permission') || fileError.message.contains('access denied')) {
        errorCode = 'PERMISSION_DENIED';
      } else if (fileError.message.contains('not found')) {
        errorCode = 'FILE_NOT_FOUND';
      } else if (fileError.message.contains('no space')) {
        errorCode = 'INSUFFICIENT_STORAGE';
      } else if (fileError.message.contains('busy') || fileError.message.contains('locked')) {
        errorCode = 'FILE_BUSY';
      } else {
        errorCode = 'FILE_SYSTEM_ERROR';
      }

      originalException = fileError;
    } else if (error is SocketException) {
      final socketError = error;
      message = 'Network error: ${socketError.message}';
      errorCode = 'NETWORK_ERROR';
      originalException = socketError;
    } else if (error is TimeoutException) {
      message = 'Operation timed out';
      errorCode = 'TIMEOUT';
      originalException = error;
    } else if (error is HttpException) {
      final httpError = error;
      message = 'HTTP error: ${httpError.message}';

      if (httpError.message.contains('404')) {
        errorCode = 'NOT_FOUND';
      } else if (httpError.message.contains('403')) {
        errorCode = 'FORBIDDEN';
      } else if (httpError.message.contains('401')) {
        errorCode = 'UNAUTHORIZED';
      } else if (httpError.message.contains('429')) {
        errorCode = 'RATE_LIMITED';
      } else if (httpError.message.contains('5')) {
        errorCode = 'SERVER_ERROR';
      } else {
        errorCode = 'HTTP_ERROR';
      }

      originalException = httpError;
    } else if (error is FormatException) {
      message = 'Data format error: ${error.message}';
      errorCode = 'FORMAT_ERROR';
      originalException = error;
    } else {
      message = error.toString();
      errorCode = 'UNKNOWN_ERROR';
      originalException = error is Exception ? error : Exception(error.toString());
    }

    // Log the error with context
    final contextStr = context != null ? ' Context: $context' : '';
    logger.e('$operation failed: $message$contextStr', error: error, stackTrace: stackTrace);

    return FileOperationError(
      message: message,
      errorCode: errorCode,
      originalException: originalException,
    );
  }

  static bool isRecoverableError(String errorCode) {
    const recoverableErrors = {
      'NETWORK_ERROR',
      'TIMEOUT',
      'SERVER_ERROR',
      'RATE_LIMITED',
      'FILE_BUSY',
    };

    return recoverableErrors.contains(errorCode);
  }

  static String getUserFriendlyMessage(String errorCode, String originalMessage) {
    switch (errorCode) {
      case 'NETWORK_ERROR':
        return 'Network connection failed. Please check your internet connection and try again.';
      case 'TIMEOUT':
        return 'The operation timed out. Please try again.';
      case 'FILE_NOT_FOUND':
        return 'The requested file could not be found.';
      case 'PERMISSION_DENIED':
        return 'Permission denied. Unable to access the file.';
      case 'INSUFFICIENT_STORAGE':
        return 'Not enough storage space available.';
      case 'SERVER_ERROR':
        return 'Server error occurred. Please try again later.';
      case 'RATE_LIMITED':
        return 'Too many requests. Please wait a moment and try again.';
      case 'UNAUTHORIZED':
        return 'Authentication required. Please log in and try again.';
      case 'FORBIDDEN':
        return 'Access forbidden. You don\'t have permission to access this resource.';
      case 'NOT_FOUND':
        return 'The requested resource was not found.';
      case 'FORMAT_ERROR':
        return 'The file format is invalid or corrupted.';
      case 'FILE_BUSY':
        return 'The file is currently in use. Please try again in a moment.';
      default:
        return originalMessage.isNotEmpty ? originalMessage : 'An unexpected error occurred.';
    }
  }
}
