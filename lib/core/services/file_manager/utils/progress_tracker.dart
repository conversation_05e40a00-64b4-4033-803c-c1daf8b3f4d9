import 'dart:async';
import 'package:melodyze/core/services/file_manager/models/file_operation_models.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

/// Enhanced progress tracker for file operations
class ProgressTracker {
  final String operationId;
  final FileOperationProgressCallback? onProgress;
  
  int _totalBytes = 0;
  int _transferredBytes = 0;
  FileOperationPhase _currentPhase = FileOperationPhase.initializing;
  DateTime? _startTime;
  DateTime? _lastUpdateTime;
  final List<int> _speedSamples = [];
  static const int _maxSpeedSamples = 10;
  
  ProgressTracker({
    required this.operationId,
    this.onProgress,
  });
  
  /// Start tracking progress
  void start({int? totalBytes}) {
    _startTime = DateTime.now();
    _lastUpdateTime = _startTime;
    if (totalBytes != null) {
      _totalBytes = totalBytes;
    }
    _updateProgress();
    logger.d('Progress tracking started for operation: $operationId');
  }
  
  /// Update the current phase
  void updatePhase(FileOperationPhase phase) {
    _currentPhase = phase;
    _updateProgress();
  }
  
  /// Update total bytes (useful when total size is discovered during operation)
  void updateTotalBytes(int totalBytes) {
    _totalBytes = totalBytes;
    _updateProgress();
  }
  
  /// Update transferred bytes
  void updateTransferredBytes(int transferredBytes) {
    final now = DateTime.now();
    
    // Calculate speed if we have previous data
    if (_lastUpdateTime != null && transferredBytes > _transferredBytes) {
      final timeDiff = now.difference(_lastUpdateTime!).inMilliseconds;
      if (timeDiff > 0) {
        final bytesDiff = transferredBytes - _transferredBytes;
        final speedBytesPerSecond = (bytesDiff * 1000) / timeDiff;
        _addSpeedSample(speedBytesPerSecond);
      }
    }
    
    _transferredBytes = transferredBytes;
    _lastUpdateTime = now;
    _updateProgress();
  }
  
  /// Add bytes to the transferred count
  void addTransferredBytes(int bytes) {
    updateTransferredBytes(_transferredBytes + bytes);
  }
  
  /// Complete the operation
  void complete() {
    _currentPhase = FileOperationPhase.completed;
    _transferredBytes = _totalBytes;
    _updateProgress();
    logger.d('Progress tracking completed for operation: $operationId');
  }
  
  /// Get current progress percentage (0.0 to 1.0)
  double get progress {
    if (_totalBytes <= 0) return 0.0;
    return (_transferredBytes / _totalBytes).clamp(0.0, 1.0);
  }
  
  /// Get current transfer speed in bytes per second
  double? get speedBytesPerSecond {
    if (_speedSamples.isEmpty) return null;
    return _speedSamples.reduce((a, b) => a + b) / _speedSamples.length;
  }
  
  /// Get estimated time remaining in seconds
  int? get estimatedSecondsRemaining {
    final speed = speedBytesPerSecond;
    if (speed == null || speed <= 0 || _totalBytes <= 0) return null;
    
    final remainingBytes = _totalBytes - _transferredBytes;
    if (remainingBytes <= 0) return 0;
    
    return (remainingBytes / speed).round();
  }
  
  /// Get elapsed time in seconds
  int get elapsedSeconds {
    if (_startTime == null) return 0;
    return DateTime.now().difference(_startTime!).inSeconds;
  }
  
  /// Add a speed sample for averaging
  void _addSpeedSample(double speedBytesPerSecond) {
    _speedSamples.add(speedBytesPerSecond.round());
    if (_speedSamples.length > _maxSpeedSamples) {
      _speedSamples.removeAt(0);
    }
  }
  
  /// Update progress and notify callback
  void _updateProgress() {
    if (onProgress == null) return;
    
    final progressInfo = FileOperationProgress(
      totalBytes: _totalBytes,
      transferredBytes: _transferredBytes,
      progress: progress,
      phase: _currentPhase,
      speedBytesPerSecond: speedBytesPerSecond,
      estimatedSecondsRemaining: estimatedSecondsRemaining,
    );
    
    try {
      onProgress!(progressInfo);
    } catch (e) {
      logger.e('Error in progress callback for operation $operationId', error: e);
    }
  }
}

/// Progress tracker manager for handling multiple concurrent operations
class ProgressTrackerManager {
  static ProgressTrackerManager? _instance;
  static ProgressTrackerManager get instance => _instance ??= ProgressTrackerManager._();
  
  ProgressTrackerManager._();
  
  final Map<String, ProgressTracker> _trackers = {};
  final StreamController<Map<String, FileOperationProgress>> _progressController = 
      StreamController<Map<String, FileOperationProgress>>.broadcast();
  
  /// Stream of all active progress updates
  Stream<Map<String, FileOperationProgress>> get progressStream => _progressController.stream;
  
  /// Create a new progress tracker
  ProgressTracker createTracker(
    String operationId, {
    FileOperationProgressCallback? onProgress,
  }) {
    final tracker = ProgressTracker(
      operationId: operationId,
      onProgress: (progress) {
        // Call individual callback
        onProgress?.call(progress);
        
        // Update global stream
        _updateGlobalProgress();
      },
    );
    
    _trackers[operationId] = tracker;
    logger.d('Created progress tracker: $operationId');
    return tracker;
  }
  
  /// Get a progress tracker by ID
  ProgressTracker? getTracker(String operationId) {
    return _trackers[operationId];
  }
  
  /// Remove a progress tracker
  void removeTracker(String operationId) {
    _trackers.remove(operationId);
    _updateGlobalProgress();
    logger.d('Removed progress tracker: $operationId');
  }
  
  /// Get all active trackers
  Map<String, ProgressTracker> get activeTrackers => Map.unmodifiable(_trackers);
  
  /// Update the global progress stream
  void _updateGlobalProgress() {
    final progressMap = <String, FileOperationProgress>{};
    
    for (final entry in _trackers.entries) {
      final tracker = entry.value;
      progressMap[entry.key] = FileOperationProgress(
        totalBytes: tracker._totalBytes,
        transferredBytes: tracker._transferredBytes,
        progress: tracker.progress,
        phase: tracker._currentPhase,
        speedBytesPerSecond: tracker.speedBytesPerSecond,
        estimatedSecondsRemaining: tracker.estimatedSecondsRemaining,
      );
    }
    
    if (!_progressController.isClosed) {
      _progressController.add(progressMap);
    }
  }
  
  /// Dispose the manager
  void dispose() {
    _trackers.clear();
    _progressController.close();
    logger.d('ProgressTrackerManager disposed');
  }
}

/// Utility class for creating progress callbacks
class ProgressCallbacks {
  ProgressCallbacks._();
  
  /// Create a simple logging progress callback
  static FileOperationProgressCallback createLoggingCallback(String operationName) {
    return (progress) {
      final percentage = (progress.progress * 100).toStringAsFixed(1);
      logger.d('$operationName: $percentage% (${progress.formattedSpeed}, ETA: ${progress.formattedETA})');
    };
  }
  
  /// Create a throttled progress callback that only calls the underlying callback
  /// at most once per specified interval
  static FileOperationProgressCallback createThrottledCallback(
    FileOperationProgressCallback callback,
    Duration throttleInterval,
  ) {
    DateTime? lastCallTime;
    
    return (progress) {
      final now = DateTime.now();
      if (lastCallTime == null || now.difference(lastCallTime!) >= throttleInterval) {
        lastCallTime = now;
        callback(progress);
      }
    };
  }
  
  /// Create a callback that only reports significant progress changes
  static FileOperationProgressCallback createSignificantChangeCallback(
    FileOperationProgressCallback callback, {
    double minProgressDelta = 0.01, // 1% minimum change
  }) {
    double? lastReportedProgress;
    
    return (progress) {
      if (lastReportedProgress == null || 
          (progress.progress - lastReportedProgress!).abs() >= minProgressDelta ||
          progress.phase == FileOperationPhase.completed) {
        lastReportedProgress = progress.progress;
        callback(progress);
      }
    };
  }
  
  /// Create a combined callback that calls multiple callbacks
  static FileOperationProgressCallback createCombinedCallback(
    List<FileOperationProgressCallback> callbacks,
  ) {
    return (progress) {
      for (final callback in callbacks) {
        try {
          callback(progress);
        } catch (e) {
          logger.e('Error in combined progress callback', error: e);
        }
      }
    };
  }
}
