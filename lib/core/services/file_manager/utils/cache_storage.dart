import 'dart:io';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:melodyze/core/services/file_manager/file_manager.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';

abstract class CacheStorage {
  Future<void> initialize();
  Future<void> dispose();
  Future<File?> downloadFile(String url, String cacheKey, FileType fileType);
  Future<String?> validateCache(String key, FileType fileType);
  Future<void> clearCacheByType(FileType fileType);
  Future<void> clearAllCache();
}

class FlutterCacheManagerStorage implements CacheStorage {
  final Map<FileType, CacheManager> _cacheManagers = {};
  bool _initialized = false;

  @override
  Future<void> initialize() async {
    if (_initialized) return;

    // Create cache managers for each file type with their specific policies
    for (final fileType in FileType.values) {
      final policy = fileType.defaultCachePolicy;
      _cacheManagers[fileType] = CacheManager(
        Config(
          'melodyze_${fileType.name}',
          stalePeriod: Duration(hours: policy.maxAgeHours),
          maxNrOfCacheObjects: policy.maxFiles,
          repo: JsonCacheInfoRepository(databaseName: 'melodyze_${fileType.name}'),
          fileService: HttpFileService(),
        ),
      );
    }

    _initialized = true;
    logger.d('FlutterCacheManagerStorage initialized');
  }

  @override
  Future<void> dispose() async {
    _initialized = false;
    logger.d('FlutterCacheManagerStorage disposed');
  }

  Future<void> _ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
  }

  @override
  Future<File?> downloadFile(String url, String cacheKey, FileType fileType) async {
    await _ensureInitialized();
    try {
      final cacheManager = _cacheManagers[fileType]!;
      final file = await cacheManager.getSingleFile(url, key: cacheKey);
      return file;
    } catch (e) {
      logger.e('Error downloading file with flutter_cache_manager: $url', error: e);
      return null;
    }
  }

  @override
  Future<String?> validateCache(String key, FileType fileType) async {
    await _ensureInitialized();

    try {
      final cacheManager = _cacheManagers[fileType]!;
      final fileInfo = await cacheManager.getFileFromCache(key);

      if (fileInfo == null) {
        return null;
      }

      final fileStr = JuceMixer().getAudioFileDetails(fileInfo.file.path);
      logger.d("validateCache: $fileStr");

      if (fileStr.contains("Could not create reader for the file.")) {
        logger.d("validateCache: UNSUPPORTED READER");
        await cacheManager.removeFile(key);
        return null;
      }

      return fileInfo.file.path;
    } catch (e) {
      logger.e('Error getting cached file path for: $key', error: e);
      return null;
    }
  }

  @override
  Future<void> clearCacheByType(FileType fileType) async {
    await _ensureInitialized();

    final cacheManager = _cacheManagers[fileType]!;
    await cacheManager.emptyCache();

    logger.d('Cleared cache for file type: ${fileType.name}');
  }

  @override
  Future<void> clearAllCache() async {
    await _ensureInitialized();

    for (final fileType in FileType.values) {
      await clearCacheByType(fileType);
    }

    logger.d('Cleared all cache');
  }
}



  // /// Cleans up expired cache entries for a specific file type
  // Future<void> cleanupExpiredEntries(FileType fileType) async {
  //   await _ensureInitialized();

  //   try {
  //     final cacheManager = _cacheManagers[fileType]!;
  //     // This will trigger cleanup of expired entries
  //     await cacheManager.emptyCache();
  //     logger.d('Cleaned up expired entries for file type: ${fileType.name}');
  //   } catch (e) {
  //     logger.e('Error cleaning up expired entries for ${fileType.name}', error: e);
  //   }
  // }

  // /// Cleans up expired cache entries for all file types
  // Future<void> cleanupAllExpiredEntries() async {
  //   await _ensureInitialized();

  //   for (final fileType in FileType.values) {
  //     await cleanupExpiredEntries(fileType);
  //   }
  //   logger.d('Cleaned up expired entries for all file types');
  // }
