import 'package:melodyze/core/services/location_service.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/local_storage_helper.dart';

/// Provides the correct base API depending on the user's continent.
class DynamicConfig {
  static const _asiaApi = 'https://app-api.melodyze.ai';
  static const _usApi = 'https://us.app-api.melodyze.ai';

  static String? _baseApi;

  /// Returns the appropriate base API URL after resolving location.
  static const _continentKey = 'user_continent_code';

  static Future<String> getBaseApi() async {
    if (_baseApi != null) return _baseApi!;
    final storage = DI().resolve<LocalStorageHelper>();
    String? continent = storage.getString(_continentKey);
    if (continent == null) {
      continent = await LocationService.getContinentCode();
      if (continent != null) {
        await storage.setString(_continentKey, continent);
      }
    }
    if (continent != 'AS') {
      _baseApi = _usApi;
    } else {
      _baseApi = _asiaApi;
    }
    return _baseApi!;
  }
}
