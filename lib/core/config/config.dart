class Config {
  Config._();

  static const oauth2ClientId = '959993156379-61gprfe4ogt9j0ou88tm2m7etta18ga4.apps.googleusercontent.com';
  static const apiLimit = 100;
  static const noUserDP = "https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/dp_placeholder_for_no_profile_picture.jpeg";

  static const keyMap = {
    "A_sharp": "A# / Bb",
    "C_sharp": "C# / Db",
    "D_sharp": "D# / Eb",
    "F_sharp": "F# / Gb",
    "G_sharp": "G# / Ab",
  };
  
  static const keyMapShort = {
    "A_sharp": "A#",
    "C_sharp": "C#",
    "D_sharp": "D#",
    "F_sharp": "F#",
    "G_sharp": "G#",
  };
}

class Endpoints {
  Endpoints._();

  // non-auth
  static const login = '/auth/v1/login';

  //v1
  static const savePreferences = '/user/v1/save_preferences';
  static const getProfile = '/user/v1/get_profile';
  static const deleteAccount = '/user/v1/delete';
  static const updateFcmToken = '/user/v1/fcm_devices';
  static const updateProfilePhoto = '/user/v1/update_profile_picture';
  static const removeProfilePhoto = '/user/v1/remove_profile_picture';

  // PUT signed url
  static const getUploadRawVocalCachedSignedUrl = '/utilities/v1/s3_presigned_url/raw_vocal_cache';
  static const getUploadFilteredVocalSignedUrl = '/utilities/v1/s3_presigned_url/filtered_raw_vocal_audio';
  static const getUploadFinalMixedAudioSignedUrl = '/utilities/v1/s3_presigned_url/final_mixed_audio';
  static const getUploadProfilePhotoSignedUrl = '/utilities/v1/s3_presigned_url/profile_picture';
  static const getUploadContentVideoSignedUrl = '/utilities/v1/s3_presigned_url/final_video';
  static const getUploadContentThumbnailSignedUrl = '/utilities/v1/s3_presigned_url/content_thumbnail';

  //v2
  static const getPreferences = '/user/v2/preferences';
  static const feedsV2 = '/user/v2/feeds';
  static const searchV2 = '/song/v2/search';
  static const homeFeedGrids = '/user/v2/home_feed_grids';
  static const homeFeedTiles = '/user/v2/home_feed_tiles';
  static const gridOrderSongs = '/user/v2/home_feed_grids/populate_songs';
  static const tileOrderSongs = '/user/v2/home_feed_tiles/populate_songs';
  static const getMasterSongById = '/song/v2/master/';
  static const getAllAnnotation = '/song/v2/annotated/get_all';
  static const getAnnotatedWav = '/song/v2/annotated/tempo_changed_wav_path';

  // recording apis
  static const getRecordings = '/recording/v1/retrieve';
  static const saveRecording = '/recording/v1/save';
  static const deleteRecording = '/recording/v1/delete';
  static const togglePublish = '/recording/v1/toggle_publishing';
}
