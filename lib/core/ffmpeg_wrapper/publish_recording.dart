import 'package:flutter/material.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/services/file_manager/file_manager.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';

class PublishRecordingService {
  static Future<String> createVideo({
    required String recId,
    required String audioPath,
    required String userImagePath,
    required List<LyricLineData> lyrics,
    required String trackName,
    required String username,
    required String originalArtist,
    required double durationSeconds,
    int bgW = 1080,
    int bgH = 1920,
    VoidCallback? onSuccess,
    VoidCallback? onFailure,
  }) async {
    bool callbackCalled = false;
    // --- Load lyrics JSON ---
    final borderPath = await AssetPaths.extractAsset(AssetPaths.profileBorder);
    final brandLogoPath = await AssetPaths.extractAsset(AssetPaths.catComplete);
    final videoWaveformPath = await AssetPaths.extractAsset(AssetPaths.videoWaveform);
    final mask1Path = await AssetPaths.extractAsset(AssetPaths.mask1);
    final mask2Path = await AssetPaths.extractAsset(AssetPaths.mask2);
    final ethFontPath = await AssetPaths.extractAsset(AssetPaths.ethnocentricFontPath);
    final iceFontPath = await AssetPaths.extractAsset(AssetPaths.icelandFontPath);
    final outputPath = await PathWrapper.getTempContentVideoPath();

    // --- Geometry ---
    const imgSize = 250;
    final userX = ((bgW - imgSize) / 9.5).floor();
    final userY = (bgH * 0.15).floor() - (imgSize ~/ 2);

    const logoSize = 90;
    final logoX = userX + imgSize - logoSize ~/ 2 - 40;
    final logoY = userY + imgSize - logoSize ~/ 2 - 20;

    final textX = userX + imgSize + 60;
    final textY = userY;

    final waveformHeight = (bgH * 0.15).floor();
    final waveformY = ((bgH - waveformHeight) / 2.4).floor();

    final List<String> fc = [];

    // background
    fc.add("color=black:s=${bgW}x$bgH:d=${durationSeconds.toStringAsFixed(3)}[bg]");

    // user circle
    fc.add("[0:v]scale=$imgSize:$imgSize,format=yuva444p,"
        "geq=lum='p(X,Y)':a='if(lte(pow(X-(W/2),2)+pow(Y-(H/2),2),pow(min(W,H)/2,2)),255,0)'"
        "[usercircle]");

    // border
    fc.add("[1:v]scale=${imgSize + 16}:${imgSize + 16}[border]");
    fc.add("[bg][border]overlay=x=${userX - 8}:y=${userY - 8}[step0]");

    // user on border
    fc.add("[step0][usercircle]overlay=x=$userX:y=$userY[step1]");

    // logo
    fc.add("[2:v]scale=$logoSize:$logoSize[logo]");
    fc.add("[step1][logo]overlay=x=$logoX:y=$logoY[step2]");

    // waveform
    fc.add("[3:v]scale=$bgW:$waveformHeight[waveform_scaled]");
    fc.add("[waveform_scaled]loop=loop=-1:size=32767:start=0[waveform]");
    fc.add("[step2][waveform]overlay=x=0:y=$waveformY:repeatlast=0[step2_waveform]");

    // titles (Track name: max 2 lines, discard extras)
    final tn = _normalizeLyrics(trackName, maxChars: 14).take(2).toList();

    String inLabel = "step2_waveform";
    int tt = 1;

    // draw up to 2 lines
    int yOffset = 0;
    for (var i = 0; i < tn.length; i++) {
      yOffset = textY + 40 + i * 50;
      fc.add("[$inLabel]drawtext=fontfile='${_q(ethFontPath)}':text='${_q(tn[i])}':"
          "fontcolor=#FEFEFECC:fontsize=42:x=$textX:y=$yOffset[tt$tt]");
      inLabel = "tt$tt";
      tt++;
    }

    // artist
    List<String> artistNameList = _normalizeLyrics(originalArtist, maxChars: 24);
    String wrappedArtistName = artistNameList.length == 1 ? artistNameList.first : '${artistNameList.first}...';

    fc.add("[$inLabel]drawtext=fontfile='${_q(iceFontPath)}':text='${_q(wrappedArtistName)}':"
        "fontcolor=#FEFEFECC:fontsize=36:x=$textX:y=${yOffset + 60}[tt$tt]");
    inLabel = "tt$tt";
    tt++;

    // username
    fc.add("[$inLabel]drawtext=fontfile='${_q(iceFontPath)}':text='FT. ${_q(username)}':"
        "fontcolor=#FEFEFECC:fontsize=44:x=$textX:y=${yOffset + 140}[step3]");

    // 8) Animated lyrics (knob)
    final lyricsAreaTop = waveformY + waveformHeight + 250;

    final knob = _KnobParams(
      yPrev: lyricsAreaTop + 0,
      yCurr: lyricsAreaTop + 140,
      yNext: lyricsAreaTop + 285,
      yEnter: lyricsAreaTop + 305,
      yExit: lyricsAreaTop - 15,
      fontPrev: 30,
      fontCurr: 46,
      fontNext: 30,
      fontCurrIdle: 38,
      transitionDuration: 0.18,
      delayFraction: 0.5,
      fontColor: "#EBC0E8",
      shadowX: 2,
      shadowY: 2,
      maxCharsCurrent: 20, // tight wrap
      maxCharsOther: 28, // looser wrap
      gapCurr: null, // defaults to ~1.15x font size
      gapOther: null, // defaults to ~1.20x font size
      groupVPadPx: 4,
    );

    final knobResult = _buildKnobFilters(
      startLabel: "step3",
      lyrics: lyrics,
      duration: durationSeconds,
      fontPath: ethFontPath,
      params: knob,
    );

    fc.addAll(knobResult.filterComplex);
    var lastLabel = knobResult.lastLabel;

    // 9) PNG overlays OVER lyrics (prev/next masks)
    // scale both to full width x 120px (adjust as needed)
    fc.add("[5:v]scale=$bgW:150[mask1s]");
    fc.add("[6:v]scale=$bgH:150[mask2s]");

    // put masks OVER the lyrics
    fc.add("[$lastLabel][mask1s]overlay=x=(main_w-overlay_w)/2:y=${knob.yPrev - 10}-overlay_h/2[after_mask1]");
    fc.add("[after_mask1][mask2s]overlay=x=(main_w-overlay_w)/2:y=${knob.yNext + 10}-overlay_h/2[after_masks]");

    lastLabel = "after_masks";
    final filterGraph = fc.join(';');

    final args = [
      "-y",
      "-i", userImagePath, // 0
      "-i", borderPath, // 1
      "-i", brandLogoPath, // 2
      "-i", videoWaveformPath, // 3
      "-i", audioPath, // 4 (real audio)
      "-i", mask1Path, // 5
      "-i", mask2Path, // 6
      "-filter_complex", filterGraph, // update any [N:] indices you used

      "-map", "[$lastLabel]",
      "-map", "4:a",

      "-c:v", "libx264", "-preset", "ultrafast", "-profile:v", "high", "-level:v", "4.1",
      "-crf", "38",
      "-g", "30", "-keyint_min", "30", "-sc_threshold", "0",
      "-force_key_frames", "expr:gte(t,n_forced*1)",
      "-maxrate", "2800k", "-bufsize", "5600k",
      "-pix_fmt", "yuv420p",

      "-c:a", "aac", "-b:a", "96k", "-ac", "2",
      "-af", "loudnorm=I=-16:TP=-1.5:LRA=11",

      "-map_metadata", "-1", "-map_chapters", "-1",
      "-movflags", "+faststart",
      "-shortest",
      outputPath,
    ];

    try {
      final session = await FFmpegKit.executeWithArguments(args);
      final ReturnCode? returnCode = await session.getReturnCode();
      logger.d("getReturnCode ${returnCode.toString()}");
      if (returnCode.toString() != "0") {
        if (!callbackCalled) {
          callbackCalled = true;
          onFailure?.call();
        }
        throw Exception('Failed to create video');
      }
    } catch (e) {
      logger.e("Error creating video: ${e.toString()}");
      if (!callbackCalled) {
        callbackCalled = true;
        onFailure?.call();
      }
      throw Exception('Failed to create video');
    }

    try {
      //  upload via file manager
      final uploadResult = await FileManagerQuickAccess.upload(
        outputPath,
        Endpoints.getUploadContentVideoSignedUrl,
        FileType.others,
        finalFilename: "$recId.mp4",
      );

      if (uploadResult is FileOperationError) {
        if (!callbackCalled) {
          callbackCalled = true;
          onFailure?.call();
        }
        throw Exception('Failed to upload video: ${uploadResult.message}');
      }

      final videoS3Path = (uploadResult as FileOperationSuccess).localPath;
      logger.d("Content Video uploaded to: $videoS3Path");
      await _uploadVideoThumbnail(outputPath, recId);

      // Call success callback after everything is completed
      if (!callbackCalled) {
        callbackCalled = true;
        onSuccess?.call();
      }

      return videoS3Path;
    } catch (e) {
      logger.e("Error uploading video: ${e.toString()}");
      if (!callbackCalled) {
        callbackCalled = true;
        onFailure?.call();
      }
      rethrow;
    }
  }

  static Future<void> _uploadVideoThumbnail(String videoPath, String recId) async {
    final playButtonPath = await AssetPaths.extractAsset(AssetPaths.contentPlay);
    final thumbnailPath = await PathWrapper.getTempContentThumbPath();
    final args = [
      "-i",
      videoPath,
      "-i",
      playButtonPath,
      "-ss",
      "00:00:02",
      "-filter_complex",
      "[0:v]crop=1080:800:0:100,scale=600:420[bg];[1:v]scale=125:125[play];[bg][play]overlay=(main_w-overlay_w)/2:(main_h-overlay_h)/2",
      "-vframes",
      "1",
      thumbnailPath
    ];

    await FFmpegKit.executeWithArguments(args);
    await FileManagerQuickAccess.upload(
      thumbnailPath,
      Endpoints.getUploadContentThumbnailSignedUrl,
      FileType.others,
      finalFilename: "$recId.webp",
    );
    logger.d("Content Video thumbnail uploaded");
  }
}

/// ---------- helpers & internals ----------

class _KnobParams {
  final int yPrev, yCurr, yNext, yEnter, yExit;
  final int fontPrev, fontCurr, fontNext, fontCurrIdle;
  final double transitionDuration;
  final double delayFraction;
  final String fontColor;
  final int shadowX, shadowY;
  final int? gapCurr, gapOther;
  final int groupVPadPx;
  final int maxCharsCurrent, maxCharsOther;

  _KnobParams({
    required this.yPrev,
    required this.yCurr,
    required this.yNext,
    required this.yEnter,
    required this.yExit,
    required this.fontPrev,
    required this.fontCurr,
    required this.fontNext,
    required this.fontCurrIdle,
    required this.transitionDuration,
    required this.delayFraction,
    required this.fontColor,
    required this.shadowX,
    required this.shadowY,
    required this.maxCharsCurrent,
    required this.maxCharsOther,
    this.gapCurr,
    this.gapOther,
    this.groupVPadPx = 4,
  });
}

class _KnobBuildResult {
  final List<String> filterComplex;
  final String lastLabel;
  _KnobBuildResult(this.filterComplex, this.lastLabel);
}

_KnobBuildResult _buildKnobFilters({
  required String startLabel,
  required List<LyricLineData> lyrics,
  required double duration,
  required String fontPath,
  required _KnobParams params,
}) {
  final filters = <String>[];
  var currLabel = startLabel;

  int gapCurr = params.gapCurr ?? (params.fontCurr * 1.15).round();
  int gapOther = params.gapOther ?? (params.fontPrev * 1.20).round();

  String T(int idx) {
    if (idx < 0 || idx >= lyrics.length) return "";
    return _fmtNum(_parseTime(lyrics[idx].startTime));
  }

  // per-lyric expressions
  for (var i = 0; i < lyrics.length; i++) {
    final li = lyrics[i];
    final tPrev = T(i - 1);
    final tCurr = T(i);
    final tNext = T(i + 1);
    final tExit = T(i + 2);

// LAST item: hold CURRENT for +10s (clamped to duration)
    final String? lastHoldEnd = (i == lyrics.length - 1) ? _fmtNum(duration) : null;

    // double-wrap: CURRENT vs OTHER
    final linesCurr = _normalizeLyrics(li.text, maxChars: params.maxCharsCurrent);
    final linesOther = _normalizeLyrics(li.text, maxChars: params.maxCharsOther);

    // Build Y and fontsize phased expressions (shared by all three slot-groups)
    final yPhases = <String>[];
    final fsPhases = <String>[];
    final td = _fmtNum(params.transitionDuration);

    // delayed NEXT pull-in anchor
    String? delayedInStart, delayedInEnd;
    if (i >= 2 && tPrev.isNotEmpty) {
      final dis = _exprAdd(tPrev, _fmtNum(params.delayFraction * params.transitionDuration));
      delayedInStart = dis;
      delayedInEnd = _exprAdd(dis, td);
    }

    if (i == 0) {
      // CURRENT from 0
      yPhases.add("if(between(t\\,0\\,${tNext.isEmpty ? _fmtNum(duration) : tNext})\\,${params.yCurr}");
      if (tNext.isNotEmpty) {
        yPhases.add("if(between(t\\,$tNext\\,${_exprAdd(tNext, td)})\\,lerp(${params.yCurr}\\,${params.yPrev}\\,(t-$tNext)/$td)");
        if (tExit.isNotEmpty) {
          yPhases.add("if(between(t\\,${_exprAdd(tNext, td)}\\,$tExit)\\,${params.yPrev}");
          yPhases.add("if(between(t\\,$tExit\\,${_exprAdd(tExit, td)})\\,lerp(${params.yPrev}\\,${params.yExit}\\,(t-$tExit)/$td)");
        }
      }
      fsPhases.add("if(between(t\\,0\\,$tCurr)\\,${params.fontCurrIdle}");
      fsPhases.add("if(between(t\\,$tCurr\\,${tNext.isEmpty ? _fmtNum(duration) : tNext})\\,${params.fontCurr}");
      if (tNext.isNotEmpty) {
        fsPhases.add("if(between(t\\,$tNext\\,${_exprAdd(tNext, td)})\\,lerp(${params.fontCurr}\\,${params.fontPrev}\\,(t-$tNext)/$td)");
        if (tExit.isNotEmpty) {
          fsPhases.add("if(between(t\\,${_exprAdd(tNext, td)}\\,$tExit)\\,${params.fontPrev}");
          fsPhases.add("if(between(t\\,$tExit\\,${_exprAdd(tExit, td)})\\,lerp(${params.fontPrev}\\,0\\,(t-$tExit)/$td)");
        }
      }
    } else if (i == 1) {
      yPhases.add("if(between(t\\,0\\,$tCurr)\\,${params.yNext}");
      yPhases.add("if(between(t\\,$tCurr\\,${_exprAdd(tCurr, td)})\\,lerp(${params.yNext}\\,${params.yCurr}\\,(t-$tCurr)/$td)");
      if (tNext.isNotEmpty) {
        yPhases.add("if(between(t\\,${_exprAdd(tCurr, td)}\\,$tNext)\\,${params.yCurr}");
        yPhases.add("if(between(t\\,$tNext\\,${_exprAdd(tNext, td)})\\,lerp(${params.yCurr}\\,${params.yPrev}\\,(t-$tNext)/$td)");
        if (tExit.isNotEmpty) {
          yPhases.add("if(between(t\\,${_exprAdd(tNext, td)}\\,$tExit)\\,${params.yPrev}");
          yPhases.add("if(between(t\\,$tExit\\,${_exprAdd(tExit, td)})\\,lerp(${params.yPrev}\\,${params.yExit}\\,(t-$tExit)/$td)");
        }
      }
      fsPhases.add("if(between(t\\,0\\,$tCurr)\\,${params.fontNext}");
      fsPhases.add("if(between(t\\,$tCurr\\,${_exprAdd(tCurr, td)})\\,lerp(${params.fontNext}\\,${params.fontCurr}\\,(t-$tCurr)/$td)");
      if (tNext.isNotEmpty) {
        fsPhases.add("if(between(t\\,${_exprAdd(tCurr, td)}\\,$tNext)\\,${params.fontCurr}");
        fsPhases.add("if(between(t\\,$tNext\\,${_exprAdd(tNext, td)})\\,lerp(${params.fontCurr}\\,${params.fontPrev}\\,(t-$tNext)/$td)");
        if (tExit.isNotEmpty) {
          fsPhases.add("if(between(t\\,${_exprAdd(tNext, td)}\\,$tExit)\\,${params.fontPrev}");
          fsPhases.add("if(between(t\\,$tExit\\,${_exprAdd(tExit, td)})\\,lerp(${params.fontPrev}\\,0\\,(t-$tExit)/$td)");
        }
      }
    } else {
      // i >= 2: delayed NEXT pull-in, then CURRENT, then PREV/exit
      if (delayedInStart != null && delayedInEnd != null) {
        yPhases.add("if(between(t\\,$delayedInStart\\,$delayedInEnd)\\,"
            "lerp(${params.yEnter}\\,${params.yNext}\\,(t-$delayedInStart)/$td)");
        yPhases.add("if(between(t\\,$delayedInEnd\\,$tCurr)\\,${params.yNext}");

        fsPhases.add("if(between(t\\,$delayedInStart\\,$delayedInEnd)\\,"
            "lerp(0\\,${params.fontNext}\\,(t-$delayedInStart)/$td)");
        fsPhases.add("if(between(t\\,$delayedInEnd\\,$tCurr)\\,${params.fontNext}");
      }

      // Promote to CURRENT
      yPhases.add("if(between(t\\,$tCurr\\,${_exprAdd(tCurr, td)})\\,"
          "lerp(${params.yNext}\\,${params.yCurr}\\,(t-$tCurr)/$td)");
      fsPhases.add("if(between(t\\,$tCurr\\,${_exprAdd(tCurr, td)})\\,"
          "lerp(${params.fontNext}\\,${params.fontCurr}\\,(t-$tCurr)/$td)");

      if (lastHoldEnd != null) {
        // LAST item: stay CURRENT until lastHoldEnd
        yPhases.add("if(between(t\\,${_exprAdd(tCurr, td)}\\,$lastHoldEnd)\\,${params.yCurr}");
        fsPhases.add("if(between(t\\,${_exprAdd(tCurr, td)}\\,$lastHoldEnd)\\,${params.fontCurr}");

        // Then CURRENT -> PREV at lastHoldEnd, and stay PREV until video end
        yPhases.add("if(between(t\\,$lastHoldEnd\\,${_exprAdd(lastHoldEnd, td)})\\,"
            "lerp(${params.yCurr}\\,${params.yPrev}\\,(t-$lastHoldEnd)/$td)");
        fsPhases.add("if(between(t\\,$lastHoldEnd\\,${_exprAdd(lastHoldEnd, td)})\\,"
            "lerp(${params.fontCurr}\\,${params.fontPrev}\\,(t-$lastHoldEnd)/$td)");
        yPhases.add("if(between(t\\,${_exprAdd(lastHoldEnd, td)}\\,${_fmtNum(duration)})\\,${params.yPrev}");
        fsPhases.add("if(between(t\\,${_exprAdd(lastHoldEnd, td)}\\,${_fmtNum(duration)})\\,${params.fontPrev}");
      } else {
        // Non-last items: remain CURRENT until next starts
        if (tNext.isNotEmpty) {
          yPhases.add("if(between(t\\,${_exprAdd(tCurr, td)}\\,$tNext)\\,${params.yCurr}");
          fsPhases.add("if(between(t\\,${_exprAdd(tCurr, td)}\\,$tNext)\\,${params.fontCurr}");

          // CURRENT -> PREV at next start, then PREV -> exit (if applicable)
          yPhases.add("if(between(t\\,$tNext\\,${_exprAdd(tNext, td)})\\,"
              "lerp(${params.yCurr}\\,${params.yPrev}\\,(t-$tNext)/$td)");
          fsPhases.add("if(between(t\\,$tNext\\,${_exprAdd(tNext, td)})\\,"
              "lerp(${params.fontCurr}\\,${params.fontPrev}\\,(t-$tNext)/$td)");

          if (tExit.isNotEmpty) {
            yPhases.add("if(between(t\\,${_exprAdd(tNext, td)}\\,$tExit)\\,${params.yPrev}");
            yPhases.add("if(between(t\\,$tExit\\,${_exprAdd(tExit, td)})\\,"
                "lerp(${params.yPrev}\\,${params.yExit}\\,(t-$tExit)/$td)");

            fsPhases.add("if(between(t\\,${_exprAdd(tNext, td)}\\,$tExit)\\,${params.fontPrev}");
            fsPhases.add("if(between(t\\,$tExit\\,${_exprAdd(tExit, td)})\\,"
                "lerp(${params.fontPrev}\\,0\\,(t-$tExit)/$td)");
          }
        }
      }
    }

    final yExpr = "'${yPhases.join("\\,")}\\,${params.yExit}${")" * yPhases.length}'";
    final fsExpr = "'${fsPhases.join("\\,")}\\,0${")" * fsPhases.length}'";

    // Slot windows
    String? nextStart, nextEnd;
    if (i == 1) {
      nextStart = "0";
      nextEnd = tCurr;
    } else if (i >= 2 && delayedInStart != null) {
      nextStart = delayedInStart;
      nextEnd = tCurr;
    }

    final currStart = (i == 0) ? "0" : tCurr; // idle current visible

    // LAST item: current holds until lastHoldEnd, otherwise until tNext or duration
    final currEnd = (lastHoldEnd != null) ? lastHoldEnd : (tNext.isEmpty ? _fmtNum(duration) : tNext);

    // PREV window:
    // - For LAST item: start after the lastHoldEnd transition and run to duration.
    // - For others: start at tNext; end at duration if no exit, else a bit after tExit.
    String? prevStart, prevEnd;
    if (lastHoldEnd != null) {
      prevStart = _exprAdd(lastHoldEnd, td);
      prevEnd = _fmtNum(duration);
    } else if (tNext.isNotEmpty) {
      prevStart = tNext;
      prevEnd = tExit.isEmpty ? _fmtNum(duration) : _exprAdd(tExit, td);
    }

    // Render three groups with different wraps
    // add named param {int addYOffset = 0} and apply it to the offset
    void renderGroup(
      List<String> lines,
      int gap,
      String? enStart,
      String? enEnd,
      String prefix,
      List<String> filters,
      String fontPath, {
      int addYOffset = 0,
    }) {
      if (enStart == null || enEnd == null) return;

      final hGroup = (params.groupVPadPx * 2) + ((lines.length - 1) * gap);
      for (var j = 0; j < lines.length; j++) {
        final ln = _escLine(lines[j]);
        final offset = (-hGroup / 2 + params.groupVPadPx + j * gap + addYOffset).round();
        final newLabel = "${prefix}_${i}_$j";

        filters.add("[$currLabel]drawtext="
            "fontfile='${_q(fontPath)}':"
            "text='${_q(ln)}':"
            "fontcolor=${params.fontColor}:"
            "fontsize=$fsExpr:"
            "x=(w-text_w)/2:"
            "y=$yExpr+$offset:"
            "enable='between(t,$enStart,$enEnd)'"
            "[$newLabel]");
        currLabel = newLabel;
      }
    }

    // how many lines when adjacent item is CURRENT
    final prevCurrCount = (i - 1) >= 0 ? _normalizeLyrics(lyrics[i - 1].text, maxChars: params.maxCharsCurrent).length : 0;
    final nextCurrCount = (i + 1) < lyrics.length ? _normalizeLyrics(lyrics[i + 1].text, maxChars: params.maxCharsCurrent).length : 0;

    // extra push = half of the extra CURRENT height beyond 2 lines
    final int padFromPrevCurr = prevCurrCount > 2 ? ((prevCurrCount - 2) * gapCurr ~/ 2) : 0;
    final int padFromNextCurr = nextCurrCount > 2 ? ((nextCurrCount - 2) * gapCurr ~/ 2) : 0;

    renderGroup(linesOther.take(2).toList(), gapOther, nextStart, nextEnd, "lyrNEXT", filters, fontPath, addYOffset: padFromPrevCurr);
    renderGroup(linesCurr, gapCurr, currStart, currEnd, "lyrCURR", filters, fontPath);
    renderGroup(linesOther.take(2).toList(), gapOther, prevStart, prevEnd, "lyrPREV", filters, fontPath, addYOffset: -padFromNextCurr);
  }

  return _KnobBuildResult(filters, currLabel);
}

// ---------- small utilities ----------

double _parseTime(String ts) {
  // "m:ss:ms" (ms 3 digits)
  final parts = ts.split(':');
  if (parts.length != 3) return 0.0;
  final m = int.tryParse(parts[0]) ?? 0;
  final s = int.tryParse(parts[1]) ?? 0;
  final ms = int.tryParse(parts[2]) ?? 0;
  return m * 60 + s + ms / 1000.0;
}

List<String> _normalizeLyrics(String text, {int maxChars = 24}) {
  // mirror your Python normalize_lyrics (escaping + word wrap)
  var t = text;
  t = t.replaceAll("'", '‘').replaceAll(":", r"\:").replaceAll(",", r"\,").replaceAll("%", r"\%").replaceAll("@", r"\@");
  final stripped = t.trim();

  // simple word-wrap by maxChars without breaking words/hyphens
  final words = stripped.split(RegExp(r'\s+'));
  final lines = <String>[];
  final buf = StringBuffer();
  for (final w in words) {
    if (buf.isEmpty) {
      if (w.length <= maxChars) {
        buf.write(w);
      } else {
        lines.add(w);
      } // if single word longer, place as-is
    } else {
      if (buf.length + 1 + w.length <= maxChars) {
        buf.write(' ');
        buf.write(w);
      } else {
        lines.add(buf.toString());
        buf.clear();
        if (w.length <= maxChars) {
          buf.write(w);
        } else {
          lines.add(w);
        }
      }
    }
  }
  if (buf.isNotEmpty) lines.add(buf.toString());
  return lines;
}

String _fmtNum(num v) => v.toStringAsFixed(3).replaceAll(',', '.');

String _exprAdd(String a, String b) => "($a+$b)";

String _escLine(String s) => s.replaceAll(r'\', r'\\').replaceAll("'", '‘');

String _q(String s) => s.replaceAll("'", "’");
