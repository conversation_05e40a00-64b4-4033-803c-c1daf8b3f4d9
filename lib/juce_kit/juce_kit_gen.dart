// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
import 'dart:ffi' as ffi;
import 'package:ffi/ffi.dart' as pkg_ffi;

/// Generate c header to dart bindings
class JuceKitGen {
  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  JuceKitGen(ffi.DynamicLibrary dynamicLibrary)
      : _lookup = dynamicLibrary.lookup;

  /// The symbols are looked up with [lookup].
  JuceKitGen.fromLookup(
      ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
          lookup)
      : _lookup = lookup;

  void juceMessageManagerInit() {
    return _juceMessageManagerInit();
  }

  late final _juceMessageManagerInitPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>(
          'juceMessageManagerInit');
  late final _juceMessageManagerInit =
      _juceMessageManagerInitPtr.asFunction<void Function()>();

  void juce_enableLogs(
    int enable,
  ) {
    return _juce_enableLogs(
      enable,
    );
  }

  late final _juce_enableLogsPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Int)>>(
          'juce_enableLogs');
  late final _juce_enableLogs =
      _juce_enableLogsPtr.asFunction<void Function(int)>();

  ffi.Pointer<pkg_ffi.Utf8> juce_applyReverbEffect(
    ffi.Pointer<pkg_ffi.Utf8> inputFilePath,
    ffi.Pointer<pkg_ffi.Utf8> outputFilePath,
    ffi.Pointer<pkg_ffi.Utf8> JSONpath,
  ) {
    return _juce_applyReverbEffect(
      inputFilePath,
      outputFilePath,
      JSONpath,
    );
  }

  late final _juce_applyReverbEffectPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('juce_applyReverbEffect');
  late final _juce_applyReverbEffect = _juce_applyReverbEffectPtr.asFunction<
      ffi.Pointer<pkg_ffi.Utf8> Function(ffi.Pointer<pkg_ffi.Utf8>,
          ffi.Pointer<pkg_ffi.Utf8>, ffi.Pointer<pkg_ffi.Utf8>)>();

  ffi.Pointer<pkg_ffi.Utf8> juce_applyProcessingGraph(
    ffi.Pointer<pkg_ffi.Utf8> inputPath,
    ffi.Pointer<pkg_ffi.Utf8> outputPath,
    ffi.Pointer<pkg_ffi.Utf8> jsonPath,
    ffi.Pointer<pkg_ffi.Utf8> genreMapPath,
    ffi.Pointer<pkg_ffi.Utf8> bgmGenre,
    ffi.Pointer<
            ffi.NativeFunction<ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>
        callback,
    ffi.Pointer<
            ffi.NativeFunction<ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>
        completion,
  ) {
    return _juce_applyProcessingGraph(
      inputPath,
      outputPath,
      jsonPath,
      genreMapPath,
      bgmGenre,
      callback,
      completion,
    );
  }

  late final _juce_applyProcessingGraphPtr = _lookup<
          ffi.NativeFunction<
              ffi.Pointer<pkg_ffi.Utf8> Function(
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>)>>(
      'juce_applyProcessingGraph');
  late final _juce_applyProcessingGraph =
      _juce_applyProcessingGraphPtr.asFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>)>();

  ffi.Pointer<pkg_ffi.Utf8> juce_applyProcessingGraphMaster(
    ffi.Pointer<pkg_ffi.Utf8> inputPath,
    ffi.Pointer<pkg_ffi.Utf8> outputPath,
    ffi.Pointer<pkg_ffi.Utf8> jsonPath,
    ffi.Pointer<pkg_ffi.Utf8> genreMapPath,
    ffi.Pointer<pkg_ffi.Utf8> bgmGenre,
    ffi.Pointer<
            ffi.NativeFunction<ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>
        callback,
    ffi.Pointer<
            ffi.NativeFunction<ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>
        completion,
  ) {
    return _juce_applyProcessingGraphMaster(
      inputPath,
      outputPath,
      jsonPath,
      genreMapPath,
      bgmGenre,
      callback,
      completion,
    );
  }

  late final _juce_applyProcessingGraphMasterPtr = _lookup<
          ffi.NativeFunction<
              ffi.Pointer<pkg_ffi.Utf8> Function(
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>)>>(
      'juce_applyProcessingGraphMaster');
  late final _juce_applyProcessingGraphMaster =
      _juce_applyProcessingGraphMasterPtr.asFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>)>();

  ffi.Pointer<pkg_ffi.Utf8> juce_getAudioFileDetails(
    ffi.Pointer<pkg_ffi.Utf8> inputFilePath,
  ) {
    return _juce_getAudioFileDetails(
      inputFilePath,
    );
  }

  late final _juce_getAudioFileDetailsPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(
              ffi.Pointer<pkg_ffi.Utf8>)>>('juce_getAudioFileDetails');
  late final _juce_getAudioFileDetails =
      _juce_getAudioFileDetailsPtr.asFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(ffi.Pointer<pkg_ffi.Utf8>)>();

  ffi.Pointer<pkg_ffi.Utf8> juce_convertWavToFlac(
    ffi.Pointer<pkg_ffi.Utf8> inPath,
    ffi.Pointer<pkg_ffi.Utf8> outPath,
  ) {
    return _juce_convertWavToFlac(
      inPath,
      outPath,
    );
  }

  late final _juce_convertWavToFlacPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('juce_convertWavToFlac');
  late final _juce_convertWavToFlac = _juce_convertWavToFlacPtr.asFunction<
      ffi.Pointer<pkg_ffi.Utf8> Function(
          ffi.Pointer<pkg_ffi.Utf8>, ffi.Pointer<pkg_ffi.Utf8>)>();

  ffi.Pointer<ffi.Void> JuceMixer_init() {
    return _JuceMixer_init();
  }

  late final _JuceMixer_initPtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Void> Function()>>(
          'JuceMixer_init');
  late final _JuceMixer_init =
      _JuceMixer_initPtr.asFunction<ffi.Pointer<ffi.Void> Function()>();

  void JuceMixer_deinit(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _JuceMixer_deinit(
      ptr,
    );
  }

  late final _JuceMixer_deinitPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'JuceMixer_deinit');
  late final _JuceMixer_deinit =
      _JuceMixer_deinitPtr.asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  ffi.Pointer<pkg_ffi.Utf8> JuceMixer_getAudioFileDetails(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> filePath,
  ) {
    return _JuceMixer_getAudioFileDetails(
      ptr,
      filePath,
    );
  }

  late final _JuceMixer_getAudioFileDetailsPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('JuceMixer_getAudioFileDetails');
  late final _JuceMixer_getAudioFileDetails =
      _JuceMixer_getAudioFileDetailsPtr.asFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(
              ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>();

  double JuceMixer_getAudioFileDuration(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> filePath,
  ) {
    return _JuceMixer_getAudioFileDuration(
      ptr,
      filePath,
    );
  }

  late final _JuceMixer_getAudioFileDurationPtr = _lookup<
      ffi.NativeFunction<
          ffi.Float Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('JuceMixer_getAudioFileDuration');
  late final _JuceMixer_getAudioFileDuration =
      _JuceMixer_getAudioFileDurationPtr.asFunction<
          double Function(ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>();

  ffi.Pointer<pkg_ffi.Utf8> JuceMixer_exportToFile(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> json,
  ) {
    return _JuceMixer_exportToFile(
      ptr,
      json,
    );
  }

  late final _JuceMixer_exportToFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('JuceMixer_exportToFile');
  late final _JuceMixer_exportToFile = _JuceMixer_exportToFilePtr.asFunction<
      ffi.Pointer<pkg_ffi.Utf8> Function(
          ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>();

  void juce_initAudioFilters(
    ffi.Pointer<pkg_ffi.Utf8> configJsonString,
    ffi.Pointer<pkg_ffi.Utf8> configMasterJsonString,
  ) {
    return _juce_initAudioFilters(
      configJsonString,
      configMasterJsonString,
    );
  }

  late final _juce_initAudioFiltersPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('juce_initAudioFilters');
  late final _juce_initAudioFilters = _juce_initAudioFiltersPtr.asFunction<
      void Function(ffi.Pointer<pkg_ffi.Utf8>, ffi.Pointer<pkg_ffi.Utf8>)>();

  ffi.Pointer<pkg_ffi.Utf8> JuceMixPlayer_setVocalFilter(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> marker,
  ) {
    return _JuceMixPlayer_setVocalFilter(
      ptr,
      marker,
    );
  }

  late final _JuceMixPlayer_setVocalFilterPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('JuceMixPlayer_setVocalFilter');
  late final _JuceMixPlayer_setVocalFilter =
      _JuceMixPlayer_setVocalFilterPtr.asFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(
              ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>();

  ffi.Pointer<pkg_ffi.Utf8> JuceMixPlayer_setMasterFilter(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> marker,
  ) {
    return _JuceMixPlayer_setMasterFilter(
      ptr,
      marker,
    );
  }

  late final _JuceMixPlayer_setMasterFilterPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('JuceMixPlayer_setMasterFilter');
  late final _JuceMixPlayer_setMasterFilter =
      _JuceMixPlayer_setMasterFilterPtr.asFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(
              ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>();

  void juce_applyStereoAndNormalizeVocal(
    ffi.Pointer<pkg_ffi.Utf8> inputPath,
    ffi.Pointer<pkg_ffi.Utf8> outputPath,
    ffi.Pointer<
            ffi.NativeFunction<ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>
        completion,
  ) {
    return _juce_applyStereoAndNormalizeVocal(
      inputPath,
      outputPath,
      completion,
    );
  }

  late final _juce_applyStereoAndNormalizeVocalPtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>)>>(
      'juce_applyStereoAndNormalizeVocal');
  late final _juce_applyStereoAndNormalizeVocal =
      _juce_applyStereoAndNormalizeVocalPtr.asFunction<
          void Function(
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<pkg_ffi.Utf8>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>)>();
}
