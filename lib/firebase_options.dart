// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAH0yA2ySiwxTQVSw3v-aMzQlqBTk9H0dc',
    appId: '1:959993156379:android:533432d92676d2262ef9be',
    messagingSenderId: '959993156379',
    projectId: 'melodyze-65923',
    databaseURL: 'https://melodyze-65923-default-rtdb.firebaseio.com',
    storageBucket: 'melodyze-65923.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyADm1wKSFT_gf63LIAMkW35gysd_0jAuWI',
    appId: '1:959993156379:ios:9d96051f255237102ef9be',
    messagingSenderId: '959993156379',
    projectId: 'melodyze-65923',
    databaseURL: 'https://melodyze-65923-default-rtdb.firebaseio.com',
    storageBucket: 'melodyze-65923.appspot.com',
    androidClientId: '959993156379-01qloq3ola5jhejcpr4pkibikvu2f09e.apps.googleusercontent.com',
    iosClientId: '959993156379-isflruq776hspo25bahceo66n7gkpji2.apps.googleusercontent.com',
    iosBundleId: 'ai.melodyze.music',
  );

}