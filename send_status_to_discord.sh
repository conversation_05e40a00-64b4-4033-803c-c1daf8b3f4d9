#!/bin/bash

STATUS="$1"
TITLE="${2:-Build Status: $STATUS}" # Use provided title or default

# Default Yellow color
COLOR="16763904"

case "$STATUS" in
"started") COLOR="16763904" ;;   # Muted Gold
"processing") COLOR="4886754" ;; # Soft Blue
"success") COLOR="5763719" ;;    # Gentle Green
"failed") COLOR="15548997" ;;    # Warm Red
"canceled") COLOR="9807270" ;;   # Medium Gray
esac

# Ensure webhook URL is set in environment
if [ -z "$DISCORD_WEBHOOK_URL" ]; then
  echo "❌ ERROR: DISCORD_WEBHOOK_URL is not set!"
  exit 1
fi

MESSAGE_FILE=".discord_message_id" # File to store the message ID

# Build URL using CM_PROJECT_ID and CM_BUILD_ID
BUILD_URL="https://codemagic.io/app/$CM_PROJECT_ID/build/$CM_BUILD_ID"

CURRENT_VERSION="$(grep 'version:' pubspec.yaml | sed 's/version: //')"

PAYLOAD=$(
  cat <<EOF
{
  "username": "Codemagic",
  "avatar_url": "https://codemagic.io/media/landing/press-kit/png/star-gradient.png",
  "embeds": [
    {
      "title": "$TITLE",
      "description": "**Build:** $BUILD_NUMBER\n**Branch:** $CM_BRANCH\n**Commit:** ${CM_COMMIT:0:8}",
      "color": $COLOR,
      "fields": [
        {
          "name": "**Current Version: **",
          "value": "\`$CURRENT_VERSION\`",
          "inline": true
        },
        {
          "name": "**Build URL: **",
          "value": "$BUILD_URL",
          "inline": false
        }
      ],
      "footer": {
        "text": "Codemagic CI/CD",
        "icon_url": "https://codemagic.io/media/landing/press-kit/png/star-gradient.png"
      }
    }
  ]
}
EOF
)

# Check if a message ID exists to update
if [ -f "$MESSAGE_FILE" ]; then
  MESSAGE_ID=$(cat "$MESSAGE_FILE")

  if [[ -n "$MESSAGE_ID" ]]; then
    EDIT_URL="$DISCORD_WEBHOOK_URL/messages/$MESSAGE_ID?wait=true"
    RESPONSE=$(curl -X PATCH -H "Content-Type: application/json" -d "$PAYLOAD" "$EDIT_URL")

    if echo "$RESPONSE" | grep -q "Unknown Message"; then
      echo "Message not found, sending a new one..."
      RESPONSE=$(curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "$DISCORD_WEBHOOK_URL?wait=true")
      echo "$RESPONSE" | jq -r '.id' >"$MESSAGE_FILE"
    fi
  else
    echo "Message ID is empty, sending a new message..."
    RESPONSE=$(curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "$DISCORD_WEBHOOK_URL?wait=true")
    echo "$RESPONSE" | jq -r '.id' >"$MESSAGE_FILE"
  fi
else
  RESPONSE=$(curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "$DISCORD_WEBHOOK_URL?wait=true")
  echo "$RESPONSE" | jq -r '.id' >"$MESSAGE_FILE"
fi

# Post build script for workflow
# echo "Build result... $CM_BUILD_STEP_STATUS"
# if [[ "$CM_BUILD_STEP_STATUS" == "success" ]]; then
#   bash send_status_to_discord.sh "success" "Build Success 🎉"
# elif [[ "$CM_BUILD_STEP_STATUS" == "failed" ]]; then
#   bash send_status_to_discord.sh "failed" "Build Failed ❌"
# else
#   bash send_status_to_discord.sh "canceled" "Build Canceled ⚠️"
# fi
