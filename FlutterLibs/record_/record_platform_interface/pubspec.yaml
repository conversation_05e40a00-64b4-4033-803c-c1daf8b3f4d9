name: record_platform_interface
description: A common interface for the record package to call dedicated platforms with method channel.
homepage: https://github.com/llfbandit/record/tree/master/record_platform_interface
version: 1.1.0

environment:
  sdk: ^3.3.0
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  plugin_platform_interface: ^2.1.8

dev_dependencies:
  flutter_lints: ^4.0.0
