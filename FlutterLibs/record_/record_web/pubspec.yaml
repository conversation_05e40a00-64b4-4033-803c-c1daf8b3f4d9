name: record_web
description: Web specific implementation for record package called by record_platform_interface.
version: 1.1.3
homepage: https://github.com/llfbandit/record/tree/master/record_web

environment:
  sdk: ^3.4.0
  flutter: ">=3.22.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_web_plugins:
    sdk: flutter

  web: ">=0.5.1 <2.0.0"
  record_platform_interface: ^1.0.2

dev_dependencies:
  flutter_lints: ^4.0.0

flutter:
  plugin:
    platforms:
      web:
        pluginClass: RecordPluginWeb
        fileName: 'record_web.dart'
  
  assets:
    - assets/js/record.worklet.js
    - assets/js/record.fixwebmduration.js
