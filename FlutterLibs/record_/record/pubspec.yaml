name: record
description: Audio recorder from microphone to file or stream with multiple codecs, bit rate and sampling rate options.
version: 5.1.3
homepage: https://github.com/llfbandit/record/tree/master/record

environment:
  sdk: ^3.3.0
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  # https://pub.dev/packages/uuid
  uuid: ">=3.0.7 <5.0.0"

  record_platform_interface: ^1.1.0
  record_web: ^1.1.3
  record_windows: ^1.0.2
  record_linux: '>=0.5.0 <1.0.0'
  record_android: ^1.2.6
  record_darwin: ^1.1.2

dev_dependencies:
  # https://pub.dev/packages/flutter_lints
  flutter_lints: ^4.0.0

# The following section is specific to Flutter.
flutter:
  plugin:
    platforms:
      android:
        default_package: record_android
      ios:
        default_package: record_darwin
      web:
        default_package: record_web
      windows:
        default_package: record_windows
      macos:
        default_package: record_darwin
      linux:
        default_package: record_linux
