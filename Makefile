# Makefile for Flutter version management

# Detect operating system
ifeq ($(OS),Windows_NT)
	OS_SCRIPT = powershell.exe -ExecutionPolicy Bypass -File ./switchFlutter/switch_flutter_version.ps1
else
	OS_SCRIPT = bash ./switchFlutter/switch_flutter_version.sh
endif

.PHONY: setup-flutter
switch_flutter_version:
	@echo "Setting up Flutter version..."
	@$(OS_SCRIPT)

.PHONY: help
help:
	@echo "Available commands:"
	@echo "  make switch_flutter_version  - Set up the correct Flutter version using FVM"
	@echo "  make help          - Show this help message"