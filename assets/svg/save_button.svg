<svg width="44" height="27" viewBox="0 0 44 27" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_262_165)">
<path d="M19.5902 21L15 16.2273L19.3607 18.5L29 11L19.5902 21Z" fill="url(#paint0_linear_262_165)"/>
</g>
<g filter="url(#filter1_ii_262_165)">
<path d="M35.9999 25.5L6.99988 25.5C5.99988 25.5 2.00012 23 2 18.5C1.99982 11.9808 6.49994 10.5 11.9999 11C11.9999 8.5 14.4999 2 22.4999 2C28.8999 2 31.9999 6.83336 32.4999 9.00004C37.4999 8.50004 42.4999 11.5 42.4999 17.5C42.4999 22.3 39.4999 25 35.9999 25.5Z" stroke="url(#paint1_linear_262_165)" stroke-width="2.5"/>
</g>
<defs>
<filter id="filter0_ii_262_165" x="14.9" y="10" width="14.3" height="11.3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.1" dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_262_165"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="1.5"/>
<feGaussianBlur stdDeviation="0.15"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_262_165" result="effect2_innerShadow_262_165"/>
</filter>
<filter id="filter1_ii_262_165" x="0.65" y="-0.25" width="43.2998" height="27.3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.1" dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_262_165"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="1.5"/>
<feGaussianBlur stdDeviation="0.15"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_262_165" result="effect2_innerShadow_262_165"/>
</filter>
<linearGradient id="paint0_linear_262_165" x1="20.2776" y1="21" x2="22.5302" y2="10.7634" gradientUnits="userSpaceOnUse">
<stop offset="0.124027" stop-color="#00E6FF"/>
<stop offset="1" stop-color="#FF00FF"/>
</linearGradient>
<linearGradient id="paint1_linear_262_165" x1="22.5" y1="1.5" x2="22.2499" y2="25.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF42EC"/>
<stop offset="0.59" stop-color="#841FFF"/>
<stop offset="1" stop-color="#1EF7FF"/>
</linearGradient>
</defs>
</svg>
