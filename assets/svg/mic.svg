<svg width="59" height="59" viewBox="0 0 59 59" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dddddd_262_88)">
<circle cx="29.5" cy="29.5" r="23.5" fill="url(#paint0_linear_262_88)"/>
<circle cx="29.5" cy="29.5" r="23" stroke="url(#paint1_linear_262_88)"/>
</g>
<path d="M29.19 33.8267C22.5111 33.8267 20.4291 28.0548 20.2229 25.1688H18.3677C18.4707 28.7763 20.7795 35.9912 29.19 35.9912C37.6006 35.9912 39.9093 28.7763 40.0124 25.1688H37.8479C37.7449 28.0548 35.869 33.8267 29.19 33.8267Z" stroke="white"/>
<path d="M35.0651 25.7872H31.9729V24.2412H35.0651V22.6951H31.9729V21.1491H35.0651V19.603H31.9729V17.7478H35.3743C34.4466 16.5109 32.0966 13.728 28.8808 13.728C25.665 13.728 23.4181 16.5109 22.6966 17.7478H26.0979V19.603H22.6966V21.1491H26.0979V22.6951H22.6966V24.2412H26.0979V25.7872H22.6966C22.5936 27.9517 23.7479 32.2807 29.19 32.2807C34.4466 32.1726 35.3743 27.9517 35.0651 25.7872Z" stroke="white"/>
<rect x="26.9116" y="36.1846" width="3.94737" height="7.03946" stroke="white"/>
<rect x="25.0547" y="45.0781" width="3.94737" height="7.65789" transform="rotate(-90 25.0547 45.0781)" stroke="white"/>
<defs>
<filter id="filter0_dddddd_262_88" x="0.7584" y="0.7584" width="57.4832" height="57.4832" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.0624"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.203922 0 0 0 0 0.929412 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_262_88"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.1248"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.203922 0 0 0 0 0.929412 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_262_88" result="effect2_dropShadow_262_88"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.4368"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.203922 0 0 0 0 0.929412 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_262_88" result="effect3_dropShadow_262_88"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.8736"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.203922 0 0 0 0 0.929412 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_262_88" result="effect4_dropShadow_262_88"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.4976"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.203922 0 0 0 0 0.929412 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_262_88" result="effect5_dropShadow_262_88"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.6208"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.203922 0 0 0 0 0.929412 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect5_dropShadow_262_88" result="effect6_dropShadow_262_88"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect6_dropShadow_262_88" result="shape"/>
</filter>
<linearGradient id="paint0_linear_262_88" x1="29.5" y1="6" x2="29.5" y2="53" gradientUnits="userSpaceOnUse">
<stop stop-color="#E339B9"/>
<stop offset="0.48"/>
<stop offset="1" stop-color="#7048FF"/>
</linearGradient>
<linearGradient id="paint1_linear_262_88" x1="29.5" y1="6" x2="29.5" y2="53" gradientUnits="userSpaceOnUse">
<stop stop-color="#D355D3"/>
<stop offset="0.515" stop-color="#080C28"/>
<stop offset="1" stop-color="#1BB7C9"/>
</linearGradient>
</defs>
</svg>
