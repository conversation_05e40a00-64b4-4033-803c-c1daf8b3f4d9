<svg width="34" height="30" viewBox="0 0 34 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_198_181)">
<path d="M16.5 27C23.3118 27 27.5 21.3333 29 18L31.5 19C31 21 26.3048 30 16.5 30C8.9 30 4 23.5 2.5 20L0 22.5L2 15L8.5 19H5C5 20 10 27 16.5 27Z" fill="url(#paint0_linear_198_181)"/>
</g>
<g filter="url(#filter1_ii_198_181)">
<path d="M17 3C10.1882 3 6 8.66667 4.5 12L2 11C2.5 9 7.19521 0 17 0C24.6 0 29.5 6.5 31 10L33.5 7.5L31.5 15L25 11H28.5C28.5 10 23.5 3 17 3Z" fill="url(#paint1_linear_198_181)"/>
</g>
<defs>
<filter id="filter0_ii_198_181" x="0" y="14.6" width="31.5" height="15.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_198_181"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.5"/>
<feGaussianBlur stdDeviation="0.2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_198_181" result="effect2_innerShadow_198_181"/>
</filter>
<filter id="filter1_ii_198_181" x="2" y="-0.4" width="31.5" height="15.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_198_181"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.5"/>
<feGaussianBlur stdDeviation="0.2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_198_181" result="effect2_innerShadow_198_181"/>
</filter>
<linearGradient id="paint0_linear_198_181" x1="30" y1="18.5" x2="3.5" y2="18.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#960DFF"/>
<stop offset="1" stop-color="#00F7FF"/>
</linearGradient>
<linearGradient id="paint1_linear_198_181" x1="3.5" y1="11.5" x2="30" y2="11.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF0DEF"/>
<stop offset="1" stop-color="#7300FF"/>
</linearGradient>
</defs>
</svg>
