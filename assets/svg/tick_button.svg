<svg width="31" height="22" viewBox="0 0 31 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_262_336)">
<path d="M10 22L0 11.5L9.5 16.5L30.5 0L10 22Z" fill="url(#paint0_linear_262_336)"/>
</g>
<defs>
<filter id="filter0_ii_262_336" x="-0.1" y="-1" width="30.8" height="23.3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.1" dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_262_336"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="1.5"/>
<feGaussianBlur stdDeviation="0.15"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_262_336" result="effect2_innerShadow_262_336"/>
</filter>
<linearGradient id="paint0_linear_262_336" x1="11.4976" y1="22" x2="16.4976" y2="-0.5" gradientUnits="userSpaceOnUse">
<stop offset="0.124027" stop-color="#00E6FF"/>
<stop offset="1" stop-color="#FF00FF"/>
</linearGradient>
</defs>
</svg>
