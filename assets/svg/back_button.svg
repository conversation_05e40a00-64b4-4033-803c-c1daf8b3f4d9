<svg width="15" height="28" viewBox="0 0 15 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_1_449)">
<path d="M0 14L15 0L5.45455 14L15 28L0 14Z" fill="url(#paint0_linear_1_449)"/>
</g>
<defs>
<filter id="filter0_ii_1_449" x="-0.1" y="-1" width="15.3" height="29.3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.1" dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_449"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="1.5"/>
<feGaussianBlur stdDeviation="0.15"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1_449" result="effect2_innerShadow_1_449"/>
</filter>
<linearGradient id="paint0_linear_1_449" x1="15" y1="28" x2="19.8449" y2="1.38184" gradientUnits="userSpaceOnUse">
<stop offset="0.266334" stop-color="#00A4B7"/>
<stop offset="0.54" stop-color="#262852"/>
<stop offset="0.824458" stop-color="#BF00BF"/>
</linearGradient>
</defs>
</svg>
