{"NG": [{"id": "Attack Time", "value": "10.0"}, {"id": "<PERSON><PERSON>", "value": "2.0"}, {"id": "Release Time", "value": "40.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-50.0"}, {"width": 656.0, "height": 160.0}], "BP_AC": [{"id": "HighPassFrequency", "value": "50.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "20000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "BF_1_AC": [{"id": "CenterFrequency", "value": "191.0"}, {"id": "GainDB", "value": "1.670000076293945"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "BF_2_AC": [{"id": "CenterFrequency", "value": "430.0"}, {"id": "GainDB", "value": "1.5"}, {"id": "QFactor", "value": "0.7000000476837158"}, {"width": 536.0, "height": 160.0}], "BF_3_AC": [{"id": "CenterFrequency", "value": "6500.0"}, {"id": "GainDB", "value": "2.0"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "SF_AC": [{"id": "HighShelfFrequency", "value": "11800.0"}, {"id": "HighShelfGain", "value": "3.0"}, {"id": "LowShelfFrequency", "value": "20000.0"}, {"id": "LowShelfGain", "value": "0.0"}, {"width": 656.0, "height": 160.0}], "Cmp_AC_1": [{"GainReduction": "1.258925437927246"}, {"id": "Attack", "value": "20.0"}, {"id": "Knee", "value": "6.0"}, {"id": "<PERSON>up<PERSON><PERSON>", "value": "2.0"}, {"id": "<PERSON><PERSON>", "value": "2.0"}, {"id": "Release", "value": "132.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-22.70000076293945"}, {"width": 912.0, "height": 320.0}], "DS_AC_1": [{"GainReduction": "1.0"}, {"id": "Attack", "value": "10.0"}, {"id": "HighFrequency", "value": "6500.0"}, {"id": "LowFrequency", "value": "16000.0"}, {"id": "<PERSON><PERSON>", "value": "3.0"}, {"id": "Release", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-32.0"}, {"width": 912.0, "height": 320.0}], "BL_AC_1": [{"id": "Ceiling", "value": "-0.1000003814697266"}, {"id": "Knee", "value": "0.699999988079071"}, {"id": "Release", "value": "100.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-1.0"}, {"width": 656.0, "height": 160.0}], "PR_AC_1": [{"id": "Damping", "value": "16000.0"}, {"id": "DecayTime", "value": "2400.0"}, {"id": "DryWet", "value": "38.0"}, {"id": "HighCut", "value": "7600.0"}, {"id": "LowCut", "value": "200.0"}, {"id": "PreDelay", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "150.0"}, {"width": 776.0, "height": 320.0}], "BP_AC_1": [{"id": "HighPassFrequency", "value": "20.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "10800.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "Cmp_AC_2": [{"GainReduction": "1.258925437927246"}, {"id": "Attack", "value": "10.0"}, {"id": "Knee", "value": "6.0"}, {"id": "<PERSON>up<PERSON><PERSON>", "value": "2.0"}, {"id": "<PERSON><PERSON>", "value": "4.0"}, {"id": "Release", "value": "80.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-22.0"}, {"width": 912.0, "height": 320.0}], "DS_AC_2": [{"GainReduction": "1.0"}, {"id": "Attack", "value": "10.0"}, {"id": "HighFrequency", "value": "6500.0"}, {"id": "LowFrequency", "value": "16000.0"}, {"id": "<PERSON><PERSON>", "value": "3.0"}, {"id": "Release", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-32.0"}, {"width": 912.0, "height": 320.0}], "BL_AC_2": [{"id": "Ceiling", "value": "-0.1000003814697266"}, {"id": "Knee", "value": "0.699999988079071"}, {"id": "Release", "value": "100.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-1.0"}, {"width": 656.0, "height": 160.0}], "PR_AC_2": [{"id": "Damping", "value": "1000.0"}, {"id": "DecayTime", "value": "2400.0"}, {"id": "DryWet", "value": "44.0"}, {"id": "HighCut", "value": "9000.0"}, {"id": "LowCut", "value": "120.0"}, {"id": "PreDelay", "value": "60.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "120.0"}, {"width": 776.0, "height": 320.0}], "BP_AC_2": [{"id": "HighPassFrequency", "value": "500.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "6800.0"}, {"id": "LowQFactor", "value": "0.800000011920929"}, {"width": 656.0, "height": 160.0}], "BP_1_Lofi_1": [{"id": "HighPassFrequency", "value": "60.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "16000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "BF_1_Lofi_1": [{"id": "CenterFrequency", "value": "200.0"}, {"id": "GainDB", "value": "-1.0"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "BF_2_Lofi_1": [{"id": "CenterFrequency", "value": "430.0"}, {"id": "GainDB", "value": "1.5"}, {"id": "QFactor", "value": "0.7000000476837158"}, {"width": 536.0, "height": 160.0}], "BF_3_Lofi_1": [{"id": "CenterFrequency", "value": "6500.0"}, {"id": "GainDB", "value": "2.0"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "SF_Lofi_1": [{"id": "HighShelfFrequency", "value": "12000.0"}, {"id": "HighShelfGain", "value": "4.0"}, {"id": "LowShelfFrequency", "value": "20000.0"}, {"id": "LowShelfGain", "value": "0.0"}, {"width": 656.0, "height": 160.0}], "Cmp_Lofi_1": [{"GainReduction": "1.258925437927246"}, {"id": "Attack", "value": "20.0"}, {"id": "Knee", "value": "6.0"}, {"id": "<PERSON>up<PERSON><PERSON>", "value": "2.0"}, {"id": "<PERSON><PERSON>", "value": "4.0"}, {"id": "Release", "value": "100.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-22.70000076293945"}, {"width": 912.0, "height": 320.0}], "DS_Lofi_1": [{"GainReduction": "1.0"}, {"id": "Attack", "value": "10.0"}, {"id": "HighFrequency", "value": "6500.0"}, {"id": "LowFrequency", "value": "16000.0"}, {"id": "<PERSON><PERSON>", "value": "5.0"}, {"id": "Release", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-32.0"}, {"width": 912.0, "height": 320.0}], "BL_Lofi_1": [{"id": "Ceiling", "value": "-0.1000003814697266"}, {"id": "Knee", "value": "0.699999988079071"}, {"id": "Release", "value": "100.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-1.0"}, {"width": 656.0, "height": 160.0}], "PR_Lofi_1": [{"id": "Damping", "value": "1700.0"}, {"id": "DecayTime", "value": "4000.0"}, {"id": "DryWet", "value": "38.0"}, {"id": "HighCut", "value": "20000.0"}, {"id": "LowCut", "value": "20.0"}, {"id": "PreDelay", "value": "10.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "150.0"}, {"width": 776.0, "height": 320.0}], "BP_2_Lofi_1": [{"id": "HighPassFrequency", "value": "400.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "3000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "BP_1_Lofi_2": [{"id": "HighPassFrequency", "value": "160.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "10000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "BF_1_Lofi_2": [{"id": "CenterFrequency", "value": "200.0"}, {"id": "GainDB", "value": "-1.0"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "BF_2_Lofi_2": [{"id": "CenterFrequency", "value": "430.0"}, {"id": "GainDB", "value": "1.5"}, {"id": "QFactor", "value": "0.7000000476837158"}, {"width": 536.0, "height": 160.0}], "BF_3_Lofi_2": [{"id": "CenterFrequency", "value": "6000.0"}, {"id": "GainDB", "value": "2.0"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "SF_Lofi_2": [{"id": "HighShelfFrequency", "value": "11800.0"}, {"id": "HighShelfGain", "value": "3.0"}, {"id": "LowShelfFrequency", "value": "20000.0"}, {"id": "LowShelfGain", "value": "0.0"}, {"width": 656.0, "height": 160.0}], "Cmp_Lofi_2": [{"GainReduction": "1.258925437927246"}, {"id": "Attack", "value": "20.0"}, {"id": "Knee", "value": "6.0"}, {"id": "<PERSON>up<PERSON><PERSON>", "value": "2.0"}, {"id": "<PERSON><PERSON>", "value": "4.0"}, {"id": "Release", "value": "132.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-22.70000076293945"}, {"width": 912.0, "height": 320.0}], "DS_Lofi_2": [{"GainReduction": "1.0"}, {"id": "Attack", "value": "10.0"}, {"id": "HighFrequency", "value": "6500.0"}, {"id": "LowFrequency", "value": "16000.0"}, {"id": "<PERSON><PERSON>", "value": "3.0"}, {"id": "Release", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-32.0"}, {"width": 912.0, "height": 320.0}], "BL_Lofi_2": [{"id": "Ceiling", "value": "-0.1000003814697266"}, {"id": "Knee", "value": "0.699999988079071"}, {"id": "Release", "value": "100.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-1.0"}, {"width": 656.0, "height": 160.0}], "PR_Lofi_2": [{"id": "Damping", "value": "1700.0"}, {"id": "DecayTime", "value": "4500.0"}, {"id": "DryWet", "value": "44.0"}, {"id": "HighCut", "value": "20000.0"}, {"id": "LowCut", "value": "20.0"}, {"id": "PreDelay", "value": "10.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "150.0"}, {"width": 776.0, "height": 320.0}], "BP_2_Lofi_2": [{"id": "HighPassFrequency", "value": "400.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "3000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "BP_PopRock": [{"id": "HighPassFrequency", "value": "50.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "20000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "BF_1_Pop": [{"id": "CenterFrequency", "value": "241.0"}, {"id": "GainDB", "value": "2.19999885559082"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "BF_2_Pop": [{"id": "CenterFrequency", "value": "5915.0"}, {"id": "GainDB", "value": "2.0"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "BF_3_Pop": [{"id": "CenterFrequency", "value": "7723.0"}, {"id": "GainDB", "value": "2.0"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "SF_Pop_1": [{"id": "HighShelfFrequency", "value": "10309.0"}, {"id": "HighShelfGain", "value": "3.0"}, {"id": "LowShelfFrequency", "value": "20000.0"}, {"id": "LowShelfGain", "value": "0.0"}, {"width": 656.0, "height": 160.0}], "Cmp_Pop_1": [{"GainReduction": "0.7176803350448608"}, {"id": "Attack", "value": "10.0"}, {"id": "Knee", "value": "2.0"}, {"id": "<PERSON>up<PERSON><PERSON>", "value": "2.0"}, {"id": "<PERSON><PERSON>", "value": "4.0"}, {"id": "Release", "value": "60.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-26.0"}, {"width": 912.0, "height": 320.0}], "DS_Pop_1": [{"GainReduction": "1.0"}, {"id": "Attack", "value": "10.0"}, {"id": "HighFrequency", "value": "6500.0"}, {"id": "LowFrequency", "value": "16000.0"}, {"id": "<PERSON><PERSON>", "value": "2.0"}, {"id": "Release", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-34.0"}, {"width": 912.0, "height": 320.0}], "BL_Pop_1": [{"id": "Ceiling", "value": "-0.1000003814697266"}, {"id": "Knee", "value": "0.699999988079071"}, {"id": "Release", "value": "100.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-4.0"}, {"width": 656.0, "height": 160.0}], "PR_Pop_1": [{"id": "Damping", "value": "1000.0"}, {"id": "DecayTime", "value": "2500.0"}, {"id": "DryWet", "value": "30.0"}, {"id": "HighCut", "value": "20000.0"}, {"id": "LowCut", "value": "20.0"}, {"id": "PreDelay", "value": "40.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "120.0"}, {"width": 776.0, "height": 320.0}], "BP_1_Pop_1": [{"id": "HighPassFrequency", "value": "400.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "7000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "SD_Pop_1": [{"id": "BpmSync", "value": "0.0"}, {"id": "DelayTime", "value": "571.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "0.1000000014901161"}, {"id": "Mix", "value": "0.1000000014901161"}, {"id": "Subdivision", "value": "4.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "1.0"}, {"width": 776.0, "height": 320.0}], "BP_2_Pop_1": [{"id": "HighPassFrequency", "value": "400.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "8000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "SF_Pop_2": [{"id": "HighShelfFrequency", "value": "10309.0"}, {"id": "HighShelfGain", "value": "4.399999618530273"}, {"id": "LowShelfFrequency", "value": "20000.0"}, {"id": "LowShelfGain", "value": "0.0"}, {"width": 656.0, "height": 160.0}], "Cmp_Pop_2": [{"GainReduction": "1.258583664894104"}, {"id": "Attack", "value": "5.0"}, {"id": "Knee", "value": "0.1000000014901161"}, {"id": "<PERSON>up<PERSON><PERSON>", "value": "2.0"}, {"id": "<PERSON><PERSON>", "value": "5.0"}, {"id": "Release", "value": "40.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-26.0"}, {"width": 912.0, "height": 320.0}], "DS_Pop_2": [{"GainReduction": "1.0"}, {"id": "Attack", "value": "10.0"}, {"id": "HighFrequency", "value": "6500.0"}, {"id": "LowFrequency", "value": "16000.0"}, {"id": "<PERSON><PERSON>", "value": "2.0"}, {"id": "Release", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-34.0"}, {"width": 912.0, "height": 320.0}], "BL_Pop_2": [{"id": "Ceiling", "value": "-0.1000003814697266"}, {"id": "Knee", "value": "0.699999988079071"}, {"id": "Release", "value": "100.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-4.0"}, {"width": 656.0, "height": 160.0}], "PR_Pop_2": [{"id": "Damping", "value": "1000.0"}, {"id": "DecayTime", "value": "3200.0"}, {"id": "DryWet", "value": "34.0"}, {"id": "HighCut", "value": "20000.0"}, {"id": "LowCut", "value": "20.0"}, {"id": "PreDelay", "value": "40.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "120.0"}, {"width": 776.0, "height": 320.0}], "BP_1_Pop_2": [{"id": "HighPassFrequency", "value": "400.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "7000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "SD_Pop_2": [{"id": "BpmSync", "value": "0.0"}, {"id": "DelayTime", "value": "571.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "0.1000000014901161"}, {"id": "Mix", "value": "0.2000000029802322"}, {"id": "Subdivision", "value": "4.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "1.0"}, {"width": 776.0, "height": 320.0}], "BP_2_Pop_2": [{"id": "HighPassFrequency", "value": "400.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "8000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "Chr_Pop_2": [{"id": "Delay", "value": "2.0"}, {"id": "Intensity", "value": "0.5"}, {"id": "Mix", "value": "0.5"}, {"id": "Rate", "value": "1.5"}, {"width": 656.0, "height": 160.0}], "BF_1_Rock_1": [{"id": "CenterFrequency", "value": "250.0"}, {"id": "GainDB", "value": "2.0"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "BF_2_Rock_1": [{"id": "CenterFrequency", "value": "978.4000244140625"}, {"id": "GainDB", "value": "-2.960000991821289"}, {"id": "QFactor", "value": "1.800000071525574"}, {"width": 536.0, "height": 160.0}], "BF_3_Rock_1": [{"id": "CenterFrequency", "value": "8857.0"}, {"id": "GainDB", "value": "2.729999542236328"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "SF_Rock_1": [{"id": "HighShelfFrequency", "value": "11000.0"}, {"id": "HighShelfGain", "value": "3.0"}, {"id": "LowShelfFrequency", "value": "20000.0"}, {"id": "LowShelfGain", "value": "0.0"}, {"width": 656.0, "height": 160.0}], "Cmp_Rock_1": [{"GainReduction": "1.258925437927246"}, {"id": "Attack", "value": "20.0"}, {"id": "Knee", "value": "4.0"}, {"id": "<PERSON>up<PERSON><PERSON>", "value": "2.0"}, {"id": "<PERSON><PERSON>", "value": "3.0"}, {"id": "Release", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-24.29999923706055"}, {"width": 912.0, "height": 320.0}], "DS_Rock_1": [{"GainReduction": "1.0"}, {"id": "Attack", "value": "10.0"}, {"id": "HighFrequency", "value": "6000.0"}, {"id": "LowFrequency", "value": "14000.0"}, {"id": "<PERSON><PERSON>", "value": "2.0"}, {"id": "Release", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-36.0"}, {"width": 912.0, "height": 320.0}], "BL_Rock_1": [{"id": "Ceiling", "value": "-0.1000003814697266"}, {"id": "Knee", "value": "0.699999988079071"}, {"id": "Release", "value": "100.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-4.0"}, {"width": 656.0, "height": 160.0}], "PR_Rock_1": [{"id": "Damping", "value": "1000.0"}, {"id": "DecayTime", "value": "3000.0"}, {"id": "DryWet", "value": "26.0"}, {"id": "HighCut", "value": "20000.0"}, {"id": "LowCut", "value": "20.0"}, {"id": "PreDelay", "value": "20.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "120.0"}, {"width": 776.0, "height": 320.0}], "BP_Rock_1": [{"id": "HighPassFrequency", "value": "400.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "6000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "BF_1_Rock_2": [{"id": "CenterFrequency", "value": "250.0"}, {"id": "GainDB", "value": "1.079999923706055"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "BF_2_Rock_2": [{"id": "CenterFrequency", "value": "1458.0"}, {"id": "GainDB", "value": "-1.420000076293945"}, {"id": "QFactor", "value": "1.800000071525574"}, {"width": 536.0, "height": 160.0}], "BF_3_Rock_2": [{"id": "CenterFrequency", "value": "5900.0"}, {"id": "GainDB", "value": "1.0"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "SF_Rock_2": [{"id": "HighShelfFrequency", "value": "10000.0"}, {"id": "HighShelfGain", "value": "3.0"}, {"id": "LowShelfFrequency", "value": "20000.0"}, {"id": "LowShelfGain", "value": "0.0"}, {"width": 656.0, "height": 160.0}], "Cmp_Rock_2": [{"GainReduction": "1.258925437927246"}, {"id": "Attack", "value": "10.0"}, {"id": "Knee", "value": "0.1000000014901161"}, {"id": "<PERSON>up<PERSON><PERSON>", "value": "2.0"}, {"id": "<PERSON><PERSON>", "value": "4.0"}, {"id": "Release", "value": "40.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-24.0"}, {"width": 912.0, "height": 320.0}], "DS_Rock_2": [{"GainReduction": "1.0"}, {"id": "Attack", "value": "10.0"}, {"id": "HighFrequency", "value": "6000.0"}, {"id": "LowFrequency", "value": "14000.0"}, {"id": "<PERSON><PERSON>", "value": "2.0"}, {"id": "Release", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-36.0"}, {"width": 912.0, "height": 320.0}], "BL_Rock_2": [{"id": "Ceiling", "value": "-0.1000003814697266"}, {"id": "Knee", "value": "0.699999988079071"}, {"id": "Release", "value": "100.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-4.0"}, {"width": 656.0, "height": 160.0}], "PR_Rock_2": [{"id": "Damping", "value": "1000.0"}, {"id": "DecayTime", "value": "3000.0"}, {"id": "DryWet", "value": "50.0"}, {"id": "HighCut", "value": "20000.0"}, {"id": "LowCut", "value": "20.0"}, {"id": "PreDelay", "value": "20.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "120.0"}, {"width": 776.0, "height": 320.0}], "BP_1_Rock_2": [{"id": "HighPassFrequency", "value": "400.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "6000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "SD_Rock_2": [{"id": "BpmSync", "value": "0.0"}, {"id": "DelayTime", "value": "545.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "0.4000000059604645"}, {"id": "Mix", "value": "0.2000000029802322"}, {"id": "Subdivision", "value": "4.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "2.0"}, {"width": 776.0, "height": 320.0}], "BP_2_Rock_2": [{"id": "HighPassFrequency", "value": "200.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "12000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "BP_1_Elec_1": [{"id": "HighPassFrequency", "value": "100.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "20000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "BF_1_Elec_1": [{"id": "CenterFrequency", "value": "241.0"}, {"id": "GainDB", "value": "2.2"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "BF_2_Elec_1": [{"id": "CenterFrequency", "value": "5915.0"}, {"id": "GainDB", "value": "2.0"}, {"id": "QFactor", "value": "1.0000000476837158"}, {"width": 536.0, "height": 160.0}], "BF_3_Elec_1": [{"id": "CenterFrequency", "value": "7723.0"}, {"id": "GainDB", "value": "2.0"}, {"id": "QFactor", "value": "1.0"}, {"width": 536.0, "height": 160.0}], "SF_Elec_1": [{"id": "HighShelfFrequency", "value": "10309.0"}, {"id": "HighShelfGain", "value": "2.0"}, {"id": "LowShelfFrequency", "value": "20000.0"}, {"id": "LowShelfGain", "value": "0.0"}, {"width": 656.0, "height": 160.0}], "Cmp_Elec_1": [{"GainReduction": "1.258925437927246"}, {"id": "Attack", "value": "5.0"}, {"id": "Knee", "value": "0.1"}, {"id": "<PERSON>up<PERSON><PERSON>", "value": "3.0"}, {"id": "<PERSON><PERSON>", "value": "5.0"}, {"id": "Release", "value": "30.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-25.70000076293945"}, {"width": 912.0, "height": 320.0}], "DS_Elec_1": [{"GainReduction": "1.0"}, {"id": "Attack", "value": "10.0"}, {"id": "HighFrequency", "value": "6500.0"}, {"id": "LowFrequency", "value": "16000.0"}, {"id": "<PERSON><PERSON>", "value": "3.0"}, {"id": "Release", "value": "50.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-34.0"}, {"width": 912.0, "height": 320.0}], "BL_Elec_1": [{"id": "Ceiling", "value": "-0.1000003814697266"}, {"id": "Knee", "value": "0.699999988079071"}, {"id": "Release", "value": "100.0"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "value": "-4.0"}, {"width": 656.0, "height": 160.0}], "PR_Elec_1": [{"id": "Damping", "value": "1000.0"}, {"id": "DecayTime", "value": "4000.0"}, {"id": "DryWet", "value": "45.0"}, {"id": "HighCut", "value": "20000.0"}, {"id": "LowCut", "value": "20.0"}, {"id": "PreDelay", "value": "40.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "120.0"}, {"width": 776.0, "height": 320.0}], "BP_2_Elec_1": [{"id": "HighPassFrequency", "value": "400.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "7000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "SD_Elec_1": [{"id": "BpmSync", "value": "0.0"}, {"id": "DelayTime", "value": "571.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "0.1000000014901161"}, {"id": "Mix", "value": "0.2000000029802322"}, {"id": "Subdivision", "value": "4.0"}, {"id": "<PERSON><PERSON><PERSON>", "value": "1.0"}, {"width": 776.0, "height": 320.0}], "BP_3_Elec_1": [{"id": "HighPassFrequency", "value": "400.0"}, {"id": "HighQFactor", "value": "1.0"}, {"id": "LowPassFrequency", "value": "8000.0"}, {"id": "LowQFactor", "value": "1.0"}, {"width": 656.0, "height": 160.0}], "Chr_Elec_1": [{"id": "Delay", "value": "2.0"}, {"id": "Intensity", "value": "0.5"}, {"id": "Mix", "value": "0.4"}, {"id": "Rate", "value": "1.5"}, {"width": 656.0, "height": 160.0}]}