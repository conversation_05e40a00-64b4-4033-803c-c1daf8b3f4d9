#!/bin/bash

# Check if GROQ_API_KEY is set
if [ -z "$GROQ_API_KEY" ]; then
    echo "Error: GROQ_API_KEY environment variable is not set. Skipping API call."
    exit 1
fi

# Get the previous tag and release notes
prev_tag=$(git for-each-ref --sort=-creatordate --format '%(objectname)' refs/tags | sed -n 2p)
release_notes=$(git log --pretty=format:"- %s" "$prev_tag"..HEAD)

# Convert multiline output to single line, remove trailing newline, and sanitize control characters
request=$(echo "$release_notes" | tr '\n' ' ' | sed 's/- //' | sed 's/[[:space:]]*$//' | sed 's/[[:cntrl:]]//g')

# Call Groq API with error handling
response=$(curl --location "https://api.groq.com/openai/v1/chat/completions" \
    --header "Content-Type: application/json" \
    --header "Authorization: Bearer $GROQ_API_KEY" \
    --data "{
            \"model\": \"llama3-8b-8192\",
            \"messages\": [
            {
                \"role\": \"user\",
                \"content\": \"From the following release notes, extract and list the top most important features or updates. Output strictly as a numbered list (1, 2, 3, …) with no extra text, no explanations, no titles. Keep it short. Do not include commit messages directly, only features.\n\nCommits:\n$request\"
            }
            ]
}" 2>/dev/null)

if [ $? -ne 0 ]; then
    echo "Error: curl command failed. Check network or API endpoint."
    exit 1
fi

if [[ -n "$response" ]]; then
    echo "$response" | jq -R '.' | jq -s '.' | jq -r 'join("  ")' | jq -r '.choices[0].message.content' > release_notes.txt
else
    echo "Error: Failed to get response from Groq API."
fi

echo "Script execution complete."